"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { MainNavigation } from "@/components/layout/main-navigation";
import { NotificationCenter } from "@/components/layout/notification-center";

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check authentication status by making a request to a protected endpoint
    const checkAuth = async () => {
      try {
        const response = await fetch("/api/auth/me", {
          method: "GET",
          credentials: "include", // Include cookies
        });

        if (response.ok) {
          const userData = await response.json();
          // Store user data in localStorage for client-side access
          localStorage.setItem("user", JSON.stringify(userData.data));
          setIsAuthenticated(true);
        } else {
          // Clear any stale user data
          localStorage.removeItem("user");
          router.push("/login");
          return;
        }
      } catch (error) {
        console.error("Auth check failed:", error);
        localStorage.removeItem("user");
        router.push("/login");
        return;
      }

      setIsLoading(false);
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNavigation />
      <main className="pt-16">
        {children}
      </main>
      <NotificationCenter />
    </div>
  );
}
