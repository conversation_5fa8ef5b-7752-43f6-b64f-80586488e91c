'use client';

import {
  Workflow,
  FileText,
  Calendar,
  Settings,
  BarChart3,
  Target,
} from 'lucide-react';
import React from 'react';

import { ApprovalWorkflowManager } from '@/components/admin/approval-workflow-manager';
import { DocumentBuilder } from '@/components/admin/document-builder';
import { DocumentTemplateManager } from '@/components/admin/document-template-manager';
import { ProcurementScheduleManager } from '@/components/admin/procurement-schedule-manager';
import { VendorRequirementsManager } from '@/components/admin/vendor-requirements-manager';
import { WorkflowBuilder } from '@/components/admin/workflow-builder/WorkflowBuilder';
import { WorkflowManagementDashboard } from '@/components/admin/workflow-management-dashboard';
import { WorkflowTemplateManager } from '@/components/admin/workflow-template-manager';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function AdminWorkflowsPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Workflow Management</h1>
        <p className="text-gray-600 mt-2">
          Comprehensive procurement workflow management system
        </p>
      </div>

      <Tabs defaultValue="dashboard" className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="workflows" className="flex items-center gap-2">
            <Workflow className="h-4 w-4" />
            Workflows
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="requirements" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Requirements
          </TabsTrigger>
          <TabsTrigger value="schedules" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Schedules
          </TabsTrigger>
          <TabsTrigger value="builder" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Builder
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <WorkflowManagementDashboard />
        </TabsContent>

        <TabsContent value="workflows">
          <ApprovalWorkflowManager />
        </TabsContent>

        <TabsContent value="templates">
          <WorkflowTemplateManager />
        </TabsContent>

        <TabsContent value="documents">
          <DocumentTemplateManager />
        </TabsContent>

        <TabsContent value="requirements">
          <VendorRequirementsManager />
        </TabsContent>

        <TabsContent value="schedules">
          <ProcurementScheduleManager />
        </TabsContent>

        <TabsContent value="builder">
          <Tabs defaultValue="workflow" className="space-y-6">
            <TabsList>
              <TabsTrigger value="workflow">Workflow Builder</TabsTrigger>
              <TabsTrigger value="document">Document Builder</TabsTrigger>
            </TabsList>

            <TabsContent value="workflow">
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold">Workflow Builder</h2>
                  <p className="text-gray-600 mt-2">
                    Design and customize procurement workflows with drag-and-drop interface
                  </p>
                </div>
                <WorkflowBuilder />
              </div>
            </TabsContent>

            <TabsContent value="document">
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold">Document Builder</h2>
                  <p className="text-gray-600 mt-2">
                    Create and customize document templates with visual editor
                  </p>
                </div>
                <DocumentBuilder />
              </div>
            </TabsContent>
          </Tabs>
        </TabsContent>
      </Tabs>
    </div>
  );
}
