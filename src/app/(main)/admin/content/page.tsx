"use client";

import { Shield, Upload, Eye, Settings, FileText, Package, Building2, Workflow } from "lucide-react";

import { AboutContentManager } from "@/components/admin/about-content-manager";
import { NewsContentManager } from "@/components/admin/news-content-manager";
import { ProcurementContentManager } from "@/components/admin/procurement-content-manager";
import { PublicAssetManager } from "@/components/admin/public-asset-manager";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function AdminContentPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Manajemen Konten Publik
        </h1>
        <p className="text-gray-600">
          Kelola asset dan konten untuk halaman landing dan portal publik
        </p>
      </div>

      <Tabs defaultValue="news" className="space-y-6">
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="news" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Berita
          </TabsTrigger>
          <TabsTrigger value="procurement" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Pengadaan
          </TabsTrigger>
          <TabsTrigger value="about" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Tentang Kami
          </TabsTrigger>
          <TabsTrigger value="assets" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Asset Manager
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Preview
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Keamanan
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Pengaturan
          </TabsTrigger>
          <TabsTrigger value="workflows" className="flex items-center gap-2">
            <Workflow className="h-4 w-4" />
            Workflows
          </TabsTrigger>
        </TabsList>

        <TabsContent value="news">
          <NewsContentManager />
        </TabsContent>

        <TabsContent value="procurement">
          <ProcurementContentManager />
        </TabsContent>

        <TabsContent value="about">
          <AboutContentManager />
        </TabsContent>

        <TabsContent value="assets">
          <PublicAssetManager />
        </TabsContent>

        <TabsContent value="preview">
          <Card>
            <CardHeader>
              <CardTitle>Preview Halaman Publik</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <a
                    href="/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <h3 className="font-medium mb-2">Halaman Landing</h3>
                    <p className="text-sm text-gray-600">
                      Preview halaman utama portal publik
                    </p>
                  </a>

                  <a
                    href="/procurements"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <h3 className="font-medium mb-2">Daftar Pengadaan</h3>
                    <p className="text-sm text-gray-600">
                      Preview halaman pengadaan publik
                    </p>
                  </a>

                  <a
                    href="/news"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <h3 className="font-medium mb-2">Berita & Pengumuman</h3>
                    <p className="text-sm text-gray-600">
                      Preview halaman berita publik
                    </p>
                  </a>

                  <a
                    href="/about"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <h3 className="font-medium mb-2">Tentang Kami</h3>
                    <p className="text-sm text-gray-600">
                      Preview halaman tentang perusahaan
                    </p>
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Keamanan Asset
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">File yang Diizinkan</h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>• Gambar: JPG, PNG, WebP, SVG, GIF</div>
                      <div>• Dokumen: PDF</div>
                      <div>• Maksimal ukuran: 10MB</div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Perlindungan Keamanan</h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>• Validasi header file</div>
                      <div>• Pemindaian konten berbahaya</div>
                      <div>• Isolasi storage terpisah</div>
                      <div>• Security headers otomatis</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Statistik Keamanan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">File yang Diblokir</span>
                    <span className="font-medium">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Ancaman Terdeteksi</span>
                    <span className="font-medium">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Upload Berhasil</span>
                    <span className="font-medium text-green-600">100%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Storage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-2">Konfigurasi Storage</h4>
                  <div className="text-sm text-gray-600 space-y-2">
                    <div>Provider: {process.env.PUBLIC_ASSETS_PROVIDER || "local"}</div>
                    <div>Path: {process.env.PUBLIC_ASSETS_LOCAL_PATH || "./public/assets"}</div>
                    <div>URL: {process.env.PUBLIC_ASSETS_LOCAL_URL || "/assets"}</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">CDN Configuration</h4>
                  <div className="text-sm text-gray-600">
                    {process.env.PUBLIC_ASSETS_CDN_URL ? (
                      <div>CDN URL: {process.env.PUBLIC_ASSETS_CDN_URL}</div>
                    ) : (
                      <div>CDN tidak dikonfigurasi (menggunakan storage lokal)</div>
                    )}
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Rekomendasi Produksi</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <div>• Gunakan CDN untuk performa optimal</div>
                    <div>• Pisahkan storage dari server aplikasi</div>
                    <div>• Aktifkan backup otomatis</div>
                    <div>• Monitor penggunaan storage</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workflows">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Workflow className="h-5 w-5" />
                Workflow Management Integration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-2">Quick Access</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Access workflow management features directly from the content management interface
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a
                      href="/admin/workflows?tab=dashboard"
                      className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <h3 className="font-medium mb-2">Workflow Dashboard</h3>
                      <p className="text-sm text-gray-600">
                        Monitor workflow performance and metrics
                      </p>
                    </a>

                    <a
                      href="/admin/workflows?tab=requirements"
                      className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <h3 className="font-medium mb-2">Vendor Requirements</h3>
                      <p className="text-sm text-gray-600">
                        Manage vendor qualification requirements
                      </p>
                    </a>

                    <a
                      href="/admin/workflows?tab=schedules"
                      className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <h3 className="font-medium mb-2">Schedule Templates</h3>
                      <p className="text-sm text-gray-600">
                        Configure procurement timelines and milestones
                      </p>
                    </a>

                    <a
                      href="/admin/workflows?tab=builder"
                      className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <h3 className="font-medium mb-2">Workflow Builder</h3>
                      <p className="text-sm text-gray-600">
                        Design custom procurement workflows
                      </p>
                    </a>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Integration Benefits</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <div>• Unified content and workflow management</div>
                    <div>• Streamlined admin experience</div>
                    <div>• Consistent user interface</div>
                    <div>• Centralized system administration</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
