import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { uploadFile } from "@/lib/upload";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);

    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return handleApiError(new Error("No file provided"));
    }

    // Upload file using configured storage provider
    const result = await uploadFile(file);

    return createSuccessResponse(
      result,
      "File uploaded successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
