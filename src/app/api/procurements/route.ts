import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { createNotification } from "@/lib/notifications/notification-service";
import {
  enhancedProcurementSchema,
  EnhancedProcurementInput
} from "@/lib/validations/enhanced-procurement";
import { procurementSchema } from "@/lib/validations/procurement";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const type = searchParams.get("type");
    const search = searchParams.get("search");

    // Build where clause based on user role
    const where: any = {};
    
    // Filter by status if provided
    if (status) {
      where.status = status;
    }
    
    // Filter by type if provided
    if (type) {
      where.type = type;
    }
    
    // Search functionality
    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { procurementNumber: { contains: search, mode: "insensitive" } },
      ];
    }

    // Role-based filtering
    if (user.roles.includes("VENDOR")) {
      // Vendors can only see published procurements
      where.status = { in: ["PUBLISHED", "SUBMISSION", "EVALUATION", "AWARDED", "COMPLETED"] };
    }

    const [procurements, total] = await Promise.all([
      prisma.procurement.findMany({
        where,
        include: {
          items: true,
          stages: {
            orderBy: { sequence: "asc" },
          },
          committee: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          offers: user.roles.includes("VENDOR") ? {
            where: { vendorId: user.vendor?.id },
            select: {
              id: true,
              status: true,
              submissionDate: true,
            },
          } : {
            select: {
              id: true,
              status: true,
              vendor: {
                select: {
                  id: true,
                  companyName: true,
                },
              },
            },
          },
          _count: {
            select: {
              offers: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.procurement.count({ where }),
    ]);

    return createSuccessResponse({
      procurements,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    // Check if user has permission to create procurements
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER")) {
      return handleApiError(new Error("Unauthorized to create procurements"));
    }

    const body = await request.json();

    // Try enhanced schema first, fall back to legacy schema
    let validatedData: any;
    let isEnhanced = false;

    try {
      validatedData = enhancedProcurementSchema.parse(body);
      isEnhanced = true;
    } catch {
      validatedData = procurementSchema.parse(body);
    }

    // Generate procurement number
    const procurementNumber = `PROC-${Date.now()}`;

    // Create procurement with all related data in transaction
    const procurement = await prisma.$transaction(async (tx: any) => {
      // Create the main procurement
      const procurementData: any = {
        title: validatedData.title,
        procurementNumber,
        createdById: user.id,
      };

      if (isEnhanced) {
        // Enhanced procurement data
        procurementData.description = validatedData.description;
        procurementData.category = validatedData.category;
        procurementData.estimatedValue = validatedData.estimatedValue;
        procurementData.submissionDeadline = new Date(validatedData.submissionDeadline);
        procurementData.workTimeUnit = validatedData.workTimeUnit;
        procurementData.hpsIncludesVat = validatedData.hpsIncludesVat;
        procurementData.vatRate = validatedData.vatRate;
        procurementData.evaluationTemplateId = validatedData.evaluationTemplateId;
      } else {
        // Legacy procurement data
        procurementData.type = validatedData.type;
        procurementData.status = validatedData.status;
        procurementData.ownerEstimate = validatedData.ownerEstimate;
        procurementData.showOwnerEstimateToVendor = validatedData.showOwnerEstimateToVendor;
        procurementData.evaluationMethod = validatedData.evaluationMethod;
      }

      const newProcurement = await tx.procurement.create({
        data: procurementData,
      });

      // Create procurement items
      const itemsData = validatedData.items.map((item: any) => ({
        procurementId: newProcurement.id,
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        unit: item.unit,
        estimatedPrice: item.estimatedPrice || item.ownerEstimate,
        specifications: item.specifications,
      }));

      await tx.procurementItem.createMany({
        data: itemsData,
      });

      // Create procurement stages (if provided)
      if (validatedData.stages && validatedData.stages.length > 0) {
        const stagesData = validatedData.stages.map((stage: any) => ({
          procurementId: newProcurement.id,
          name: stage.name,
          sequence: stage.sequence,
          startDate: isEnhanced ? new Date(stage.startDate) : stage.startDate,
          endDate: isEnhanced ? new Date(stage.endDate) : stage.endDate,
        }));

        await tx.procurementStage.createMany({
          data: stagesData,
        });
      }

      // Create committee members (if provided)
      if (validatedData.committee && validatedData.committee.length > 0) {
        const committeeData = validatedData.committee.map((member: any) => ({
          procurementId: newProcurement.id,
          userId: member.userId,
          role: member.role || member.committeeRole,
        }));

        await tx.procurementCommitteeMember.createMany({
          data: committeeData,
        });
      }

      return newProcurement;
    });

    // Send notifications to vendors if status is PUBLISHED
    if (validatedData.status === "PUBLISHED") {
      const verifiedVendors = await prisma.vendor.findMany({
        where: { verificationStatus: "VERIFIED" },
        include: { user: true },
      });

      for (const vendor of verifiedVendors) {
        await createNotification({
          userId: vendor.userId,
          title: `Pengadaan Baru: ${validatedData.title}`,
          message: `Pengadaan baru telah dipublikasikan. Silakan submit penawaran Anda.`,
          type: "INFO",
        });
      }
    }

    return createSuccessResponse(
      procurement,
      "Procurement created successfully",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}
