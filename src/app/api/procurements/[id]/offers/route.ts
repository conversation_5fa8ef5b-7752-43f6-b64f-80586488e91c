import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError, ConflictError } from "@/lib/errors";
import { createNotification } from "@/lib/notifications/notification-service";
import { vendorOfferSchema } from "@/lib/validations/procurement";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // Check if procurement exists
    const procurement = await prisma.procurement.findUnique({
      where: { id: params.id },
    });

    if (!procurement) {
      throw new NotFoundError("Procurement not found");
    }

    // Build where clause based on user role
    const where: any = { procurementId: params.id };
    
    if (user.roles.includes("VENDOR")) {
      // Vendors can only see their own offers
      where.vendorId = user.vendor?.id;
    }

    const offers = await prisma.vendorOffer.findMany({
      where,
      include: {
        vendor: {
          select: {
            id: true,
            companyName: true,
            picName: true,
          },
        },
        items: {
          include: {
            item: {
              select: {
                id: true,
                name: true,
                quantity: true,
                unit: true,
                ownerEstimate: true,
              },
            },
          },
        },
        documents: true,
      },
      orderBy: { submissionDate: "desc" },
    });

    return createSuccessResponse(offers);
  } catch (error) {
    return handleApiError(error);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // Check if user is a vendor
    if (!user.roles.includes("VENDOR") || !user.vendor) {
      return handleApiError(new Error("Only verified vendors can submit offers"));
    }

    // Check if vendor is verified
    if (user.vendor.verificationStatus !== "VERIFIED") {
      return handleApiError(new Error("Your vendor account must be verified to submit offers"));
    }

    const body = await request.json();
    const validatedData = vendorOfferSchema.parse(body);

    // Check if procurement exists and is in submission phase
    const procurement = await prisma.procurement.findUnique({
      where: { id: params.id },
      include: {
        items: true,
        stages: {
          where: { name: { contains: "Penawaran" } },
          orderBy: { sequence: "asc" },
        },
        committee: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!procurement) {
      throw new NotFoundError("Procurement not found");
    }

    if (procurement.status !== "SUBMISSION") {
      return handleApiError(new Error("Procurement is not accepting submissions"));
    }

    // Check if submission deadline has passed
    const submissionStage = procurement.stages.find((stage: any) =>
      stage.name.toLowerCase().includes("penawaran") ||
      stage.name.toLowerCase().includes("submission")
    );

    if (submissionStage && submissionStage.endDate && new Date() > submissionStage.endDate) {
      return handleApiError(new Error("Submission deadline has passed"));
    }

    // Check if vendor has already submitted an offer
    const existingOffer = await prisma.vendorOffer.findFirst({
      where: {
        procurementId: params.id,
        vendorId: user.vendor.id,
      },
    });

    if (existingOffer) {
      throw new ConflictError("You have already submitted an offer for this procurement");
    }

    // Validate that all procurement items are included in the offer
    const procurementItemIds = procurement.items.map((item: any) => item.id);
    const offerItemIds = validatedData.offerItems.map((item: any) => item.itemId);

    const missingItems = procurementItemIds.filter((id: any) => !offerItemIds.includes(id));
    if (missingItems.length > 0) {
      return handleApiError(new Error("All procurement items must be included in your offer"));
    }

    // Calculate total offered price
    const totalOfferedPrice = validatedData.offerItems.reduce(
      (total: any, item: any) => total + item.offeredPrice,
      0
    );

    // Generate offer number
    const offerNumber = `OFFER-${Date.now()}-${user.vendor.id.slice(-4)}`;

    // Create vendor offer in transaction
    const vendorOffer = await prisma.$transaction(async (tx: any) => {
      // Create the main offer
      const newOffer = await tx.vendorOffer.create({
        data: {
          procurementId: params.id,
          vendorId: user.vendor!.id,
          offerNumber,
          totalOfferedPrice,
          status: "SUBMITTED",
        },
      });

      // Create offer items
      await tx.vendorOfferItem.createMany({
        data: validatedData.offerItems.map((item) => ({
          vendorOfferId: newOffer.id,
          itemId: item.itemId,
          offeredPrice: item.offeredPrice,
        })),
      });

      // Create documents if provided
      if (validatedData.documents && validatedData.documents.length > 0) {
        await tx.document.createMany({
          data: validatedData.documents.map((doc) => ({
            fileName: doc.fileName,
            fileUrl: doc.fileUrl,
            fileType: doc.fileType,
            description: doc.description || doc.documentType,
            vendorOfferId: newOffer.id,
          })),
        });
      }

      return newOffer;
    });

    // Send notification to procurement committee
    for (const member of procurement.committee) {
      await createNotification({
        userId: member.userId,
        title: `Penawaran Baru dari ${user.vendor.companyName}`,
        message: `Penawaran baru telah diterima untuk ${procurement.title}`,
        type: "INFO",
      });
    }

    return createSuccessResponse(
      vendorOffer,
      "Offer submitted successfully",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}
