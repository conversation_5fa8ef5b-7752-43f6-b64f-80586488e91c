import { NextRequest } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { discussionService } from "@/lib/discussion/discussion-service";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { auditSecurity } from "@/lib/security/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";

const createDiscussionSchema = z.object({
  procurementId: z.string(),
  title: z.string(),
  description: z.string().optional(),
  type: z.enum(["AANWIJZING", "QA_SESSION", "CLARIFICATION", "NEGOTIATION", "TECHNICAL_DISCUSSION", "GENERAL"]),
  meetingDate: z.string().transform(str => new Date(str)).optional(),
  meetingLocation: z.string().optional(),
  meetingType: z.enum(["PHYSICAL", "VIRTUAL", "HYBRID"]).optional(),
  maxParticipants: z.number().optional(),
  isPublic: z.boolean().default(true),
  allowAnonymous: z.boolean().default(false),
  endDate: z.string().transform(str => new Date(str)).optional(),
});

// List discussions
const listHandler = requireRoles(["ADMIN", "COMMITTEE", "VENDOR"])(async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    const { searchParams } = new URL(request.url);
    
    const procurementId = searchParams.get("procurementId");
    const type = searchParams.get("type");
    const status = searchParams.get("status");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (procurementId) {
      where.procurementId = procurementId;
    }
    
    if (type) {
      where.type = type;
    }
    
    if (status) {
      where.status = status;
    }

    // For vendors, only show public discussions or ones they participate in
    if (user.roles.includes("VENDOR")) {
      where.OR = [
        { isPublic: true },
        {
          participants: {
            some: {
              userId: user.id,
            },
          },
        },
      ];
    }

    const [discussions, totalCount] = await Promise.all([
      prisma.procurementDiscussion.findMany({
        where,
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
            },
          },
          procurement: {
            select: {
              id: true,
              title: true,
              procurementNumber: true,
            },
          },
          _count: {
            select: {
              messages: true,
              participants: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.procurementDiscussion.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return createSuccessResponse({
      discussions,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
});

// Create discussion
const createHandler = requireRoles(["ADMIN", "COMMITTEE"])(
  auditSecurity("CREATE_DISCUSSION", {
    description: "Create procurement discussion",
    severity: "MEDIUM",
    captureRequest: true,
  })(async function POST(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = createDiscussionSchema.parse(body);

      // Create discussion
      const discussion = await discussionService.createDiscussion({
        procurementId: validatedData.procurementId,
        title: validatedData.title,
        description: validatedData.description,
        type: validatedData.type as any, // Type assertion for enum compatibility
        meetingDate: validatedData.meetingDate,
        meetingLocation: validatedData.meetingLocation,
        meetingType: validatedData.meetingType as any,
        maxParticipants: validatedData.maxParticipants,
        isPublic: validatedData.isPublic,
        allowAnonymous: validatedData.allowAnonymous,
        endDate: validatedData.endDate,
        createdById: user.id,
      });

      // Log the creation
      console.log(`Discussion created by ${user.email}:`, {
        discussionId: discussion.id,
        title: discussion.title,
        type: discussion.type,
        procurementId: discussion.procurementId,
      });

      return createSuccessResponse(discussion, "Discussion created successfully");
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { listHandler as GET, createHandler as POST };
