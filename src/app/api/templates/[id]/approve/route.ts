import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to approve templates
    if (!user.roles.includes("ADMIN")) {
      return handleApiError(new Error("Unauthorized to approve templates"));
    }

    const template = await prisma.documentTemplate.findUnique({
      where: { id: params.id },
    });

    if (!template) {
      throw new NotFoundError("Template not found");
    }

    // Check if template is in pending approval status
    if (template.status !== "PENDING_APPROVAL") {
      return handleApiError(new Error("Template is not pending approval"));
    }

    // Update template status to approved
    const updatedTemplate = await prisma.documentTemplate.update({
      where: { id: params.id },
      data: {
        status: "APPROVED",
        approvedBy: user.id,
        approvedAt: new Date(),
        isActive: true, // Approved templates become active
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        approver: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return createSuccessResponse(updatedTemplate);
  } catch (error) {
    return handleApiError(error);
  }
}
