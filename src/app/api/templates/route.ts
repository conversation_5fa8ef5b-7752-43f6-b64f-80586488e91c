import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { documentTemplateSchema, templateSearchSchema } from "@/lib/validations/template";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to view templates
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER")) {
      return handleApiError(new Error("Unauthorized to view templates"));
    }

    const { searchParams } = new URL(request.url);
    const validatedParams = templateSearchSchema.parse({
      search: searchParams.get("search"),
      type: searchParams.get("type"),
      category: searchParams.get("category"),
      status: searchParams.get("status"),
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
    });

    // Build where clause
    const where: any = {};
    
    if (validatedParams.search) {
      where.OR = [
        { name: { contains: validatedParams.search, mode: "insensitive" } },
        { description: { contains: validatedParams.search, mode: "insensitive" } },
        { category: { contains: validatedParams.search, mode: "insensitive" } },
      ];
    }
    
    if (validatedParams.type) {
      where.type = validatedParams.type;
    }
    
    if (validatedParams.category) {
      where.category = validatedParams.category;
    }
    
    if (validatedParams.status) {
      where.status = validatedParams.status;
    }

    const [templates, total] = await Promise.all([
      prisma.documentTemplate.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          approver: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              versions: true,
              usages: true,
            },
          },
        },
        orderBy: {
          updatedAt: "desc",
        },
        skip: (validatedParams.page - 1) * validatedParams.limit,
        take: validatedParams.limit,
      }),
      prisma.documentTemplate.count({ where }),
    ]);

    return createSuccessResponse({
      templates,
      pagination: {
        page: validatedParams.page,
        limit: validatedParams.limit,
        total,
        totalPages: Math.ceil(total / validatedParams.limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to create templates
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER")) {
      return handleApiError(new Error("Unauthorized to create templates"));
    }

    const body = await request.json();
    const validatedData = documentTemplateSchema.parse(body);

    // Create template in transaction
    const template = await prisma.$transaction(async (tx: any) => {
      // Create the main template
      const newTemplate = await tx.documentTemplate.create({
        data: {
          name: validatedData.name,
          description: validatedData.description,
          type: validatedData.type,
          category: validatedData.category,
          content: validatedData.content,
          variables: validatedData.variables,
          createdBy: user.id,
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // Create initial version
      await tx.documentTemplateVersion.create({
        data: {
          templateId: newTemplate.id,
          version: 1,
          content: validatedData.content,
          variables: validatedData.variables,
          changelog: "Initial version",
          createdBy: user.id,
        },
      });

      return newTemplate;
    });

    return createSuccessResponse(
      template,
      "Template created successfully",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}
