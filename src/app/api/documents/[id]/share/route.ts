import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { documentSecurity, DocumentPermission, AccessLevel } from '@/lib/documents/security';
import { documentSharing } from '@/lib/documents/sharing';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();
    
    const {
      sharedWith,
      permissions,
      accessLevel,
      expiresAt,
      message,
      requiresApproval,
      conditions,
    } = body;

    if (!sharedWith || !Array.isArray(sharedWith) || sharedWith.length === 0) {
      return NextResponse.json(
        { error: 'sharedWith array is required and must not be empty' },
        { status: 400 }
      );
    }

    if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
      return NextResponse.json(
        { error: 'permissions array is required and must not be empty' },
        { status: 400 }
      );
    }

    // Validate permissions
    const validPermissions = Object.values(DocumentPermission);
    const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      return NextResponse.json(
        { error: `Invalid permissions: ${invalidPermissions.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate access level
    if (accessLevel && !Object.values(AccessLevel).includes(accessLevel)) {
      return NextResponse.json(
        { error: `Invalid access level: ${accessLevel}` },
        { status: 400 }
      );
    }

    const shareResult = await documentSharing.shareDocument({
      documentId: params.id,
      sharedBy: user.id,
      sharedWith,
      permissions,
      accessLevel: accessLevel || AccessLevel.INTERNAL,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      message,
      requiresApproval,
      conditions,
    });

    return NextResponse.json(shareResult);
  } catch (error) {
    console.error('Error sharing document:', error);
    
    if (error instanceof Error && error.message.includes('permission')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to share this document' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to share document' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // Check if user has permission to view shares
    const permission = await documentSecurity.checkPermission(
      params.id,
      user.id,
      DocumentPermission.READ
    );

    if (!permission.allowed) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view document shares' },
        { status: 403 }
      );
    }

    const shares = await documentSharing.getDocumentShares(params.id);
    return NextResponse.json(shares);
  } catch (error) {
    console.error('Error getting document shares:', error);
    return NextResponse.json(
      { error: 'Failed to get document shares' },
      { status: 500 }
    );
  }
}
