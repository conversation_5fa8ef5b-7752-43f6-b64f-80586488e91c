import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { documentSecurity, DocumentPermission } from '@/lib/documents/security';
import { documentSharing } from '@/lib/documents/sharing';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();
    
    const {
      permissions,
      expiresAt,
      maxUses,
      requiresPassword,
      password,
    } = body;

    if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
      return NextResponse.json(
        { error: 'permissions array is required and must not be empty' },
        { status: 400 }
      );
    }

    // Validate permissions
    const validPermissions = Object.values(DocumentPermission);
    const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      return NextResponse.json(
        { error: `Invalid permissions: ${invalidPermissions.join(', ')}` },
        { status: 400 }
      );
    }

    // Check if user has permission to share the document
    const sharePermission = await documentSecurity.checkPermission(
      params.id,
      user.id,
      DocumentPermission.SHARE
    );

    if (!sharePermission.allowed) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create share link for this document' },
        { status: 403 }
      );
    }

    const shareLink = await documentSharing.createShareLink(
      params.id,
      user.id,
      permissions,
      {
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        maxUses,
        requiresPassword,
        password,
      }
    );

    // Don't return the full token in the response for security
    const response = {
      ...shareLink,
      shareUrl: `${process.env.NEXT_PUBLIC_APP_URL}/shared/${shareLink.token}`,
      token: undefined, // Remove token from response
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error creating share link:', error);
    return NextResponse.json(
      { error: 'Failed to create share link' },
      { status: 500 }
    );
  }
}
