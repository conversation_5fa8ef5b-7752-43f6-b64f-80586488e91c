import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { documentSecurity, DocumentPermission } from '@/lib/documents/security';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const { searchParams } = new URL(request.url);
    
    // Check if user has permission to view audit trail
    const permission = await documentSecurity.checkPermission(
      params.id,
      user.id,
      DocumentPermission.ADMIN
    );

    if (!permission.allowed) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view audit trail' },
        { status: 403 }
      );
    }

    const options = {
      userId: searchParams.get('userId') || undefined,
      action: searchParams.get('action') as any || undefined,
      startDate: searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined,
      endDate: searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined,
      limit: parseInt(searchParams.get('limit') || '50'),
      offset: parseInt(searchParams.get('offset') || '0'),
    };

    const auditTrail = await documentSecurity.getAuditTrail(params.id, options);
    
    return NextResponse.json({
      auditTrail,
      pagination: {
        limit: options.limit,
        offset: options.offset,
        total: auditTrail.length, // In real implementation, this would be the total count
      },
    });
  } catch (error) {
    console.error('Error getting audit trail:', error);
    return NextResponse.json(
      { error: 'Failed to get audit trail' },
      { status: 500 }
    );
  }
}
