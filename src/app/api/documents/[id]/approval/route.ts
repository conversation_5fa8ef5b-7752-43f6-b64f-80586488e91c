import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { documentApprovalWorkflow } from '@/lib/documents/approval-workflow';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const { workflowTemplateId, metadata } = body;

    // Start approval workflow for document
    const approvalRequest = await documentApprovalWorkflow.startApprovalWorkflow(
      params.id,
      user.id,
      workflowTemplateId,
      metadata || {}
    );

    return NextResponse.json({
      success: true,
      approvalRequest,
    });
  } catch (error) {
    console.error('Error starting approval workflow:', error);
    return NextResponse.json(
      { error: 'Failed to start approval workflow' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get approval history for document
    const approvalHistory = await documentApprovalWorkflow.getDocumentApprovalHistory(params.id);

    return NextResponse.json(approvalHistory);
  } catch (error) {
    console.error('Error fetching approval history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch approval history' },
      { status: 500 }
    );
  }
}
