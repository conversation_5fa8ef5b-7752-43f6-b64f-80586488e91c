import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { createSuccessResponse, handleApiError } from "@/lib/errors";

// GET /api/public/news-categories - Get all active news categories
export async function GET(_request: NextRequest) {
  try {
    const categories = await prisma.newsCategory.findMany({
      where: {
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return createSuccessResponse({
      categories,
    });
  } catch (error) {
    console.error("Error fetching news categories:", error);
    return handleApiError(error);
  }
}
