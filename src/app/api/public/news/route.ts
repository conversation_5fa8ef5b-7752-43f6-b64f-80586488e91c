import { Prisma } from "@prisma/client";
import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    const category = searchParams.get("category") || "";
    const sortBy = searchParams.get("sortBy") || "newest";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.NewsArticleWhereInput = {
      // Only show published articles for public access
      status: "PUBLISHED"
    };

    // Add search filter
    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { excerpt: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
      ];
    }

    // Add status filter (allow override for admin access)
    if (status && status !== "published") {
      where.status = status.toUpperCase() as any;
    }

    // Add category filter (skip if "all" is selected)
    if (category && category !== "all") {
      where.category = {
        slug: category
      };
    }

    // Build order by clause
    let orderBy: Prisma.NewsArticleOrderByWithRelationInput = {};
    switch (sortBy) {
      case "oldest":
        orderBy = { publishedAt: "asc" };
        break;
      case "title":
        orderBy = { title: "asc" };
        break;
      case "updated":
        orderBy = { updatedAt: "desc" };
        break;
      default: // newest
        orderBy = { publishedAt: "desc" };
    }

    console.log("🔍 [DEBUG API] Query params:", { page, limit, search, status, category, sortBy });
    console.log("🔍 [DEBUG API] Where clause:", where);
    console.log("🔍 [DEBUG API] Order by clause:", orderBy);

    // Fetch articles
    const [articles, totalCount] = await Promise.all([
      prisma.newsArticle.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        select: {
          id: true,
          title: true,
          slug: true,
          excerpt: true,
          content: true,
          featuredImage: true,
          status: true,
          publishedAt: true,
          createdAt: true,
          updatedAt: true,
          author: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      }),
      prisma.newsArticle.count({ where }),
    ]);

    console.log("📊 [DEBUG API] Found articles count:", articles.length);
    console.log("📊 [DEBUG API] Total count:", totalCount);
    console.log("🎯 [DEBUG API] First article:", articles[0]);

    const totalPages = Math.ceil(totalCount / limit);

    const responseData = {
      data: {
        articles,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    };

    console.log("📦 [DEBUG API] Response structure:", {
      success: true,
      data: {
        articles: responseData.data.articles.length + " articles",
        pagination: responseData.data.pagination
      }
    });

    return createSuccessResponse(responseData);
  } catch (error) {
    console.error("Error fetching news articles:", error);
    return handleApiError(error);
  }
}