import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    if (!slug) {
      return Response.json(
        { error: "Article slug is required" },
        { status: 400 }
      );
    }

    // Fetch article by slug
    const article = await prisma.newsArticle.findFirst({
      where: {
        slug: slug,
        status: "PUBLISHED", // Only show published articles
      },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        content: true,
        featuredImage: true,
        status: true,
        publishedAt: true,
        createdAt: true,
        updatedAt: true,
        author: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!article) {
      return Response.json(
        { error: "Article not found" },
        { status: 404 }
      );
    }

    return createSuccessResponse({
      data: {
        article,
      },
    });
  } catch (error) {
    console.error("Error fetching news article:", error);
    return handleApiError(error);
  }
}