import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createSuccessResponse, handleApiError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

const createAboutContentSchema = z.object({
  heroTitle: z.string().optional(),
  heroSubtitle: z.string().optional(),
  heroDescription: z.string().optional(),
  aboutTitle: z.string().optional(),
  aboutDescription: z.string().optional(),
  companyTitle: z.string().optional(),
  companyDescription: z.string().optional(),
  companyName: z.string().optional(),
  companyFullName: z.string().optional(),
  operationalArea: z.string().optional(),
  vision: z.string().optional(),
  features: z.array(z.string()).optional(),
  contactTitle: z.string().optional(),
  contactDescription: z.string().optional(),
  address: z.string().optional(),
  phones: z.array(z.string()).optional(),
  emails: z.array(z.string()).optional(),
  ctaTitle: z.string().optional(),
  ctaDescription: z.string().optional(),
});

// GET /api/admin/about-content - List all about content
const getHandler = requireRoles(["ADMIN"])(async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const section = searchParams.get("section");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const where: Record<string, unknown> = {};

    // Filter by section if provided
    if (section) {
      where.section = section;
    }

    const [content, totalCount] = await Promise.all([
      prisma.aboutPageContent.findMany({
        where,
        include: {
          updatedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.aboutPageContent.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return createSuccessResponse({
      content,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching about content:", error);
    return handleApiError(error);
  }
});

// POST /api/admin/about-content - Create new about content
const postHandler = requireRoles(["ADMIN"])(async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();

    const validatedData = createAboutContentSchema.parse(body);

    const aboutContent = await prisma.aboutPageContent.create({
      data: {
        ...validatedData,
        features: validatedData.features || [],
        phones: validatedData.phones || [],
        emails: validatedData.emails || [],
        updatedById: user.id,
      },
      include: {
        updatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return createSuccessResponse(aboutContent);
  } catch (error) {
    console.error("Error creating about content:", error);
    return handleApiError(error);
  }
});

export const GET = getHandler;
export const POST = postHandler;
