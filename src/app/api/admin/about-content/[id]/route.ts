import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createSuccessResponse, handleApiError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

const updateAboutContentSchema = z.object({
  heroTitle: z.string().optional(),
  heroSubtitle: z.string().optional(),
  heroDescription: z.string().optional(),
  aboutTitle: z.string().optional(),
  aboutDescription: z.string().optional(),
  companyTitle: z.string().optional(),
  companyDescription: z.string().optional(),
  companyName: z.string().optional(),
  companyFullName: z.string().optional(),
  operationalArea: z.string().optional(),
  vision: z.string().optional(),
  features: z.array(z.string()).optional(),
  contactTitle: z.string().optional(),
  contactDescription: z.string().optional(),
  address: z.string().optional(),
  phones: z.array(z.string()).optional(),
  emails: z.array(z.string()).optional(),
  ctaTitle: z.string().optional(),
  ctaDescription: z.string().optional(),
});

// GET /api/admin/about-content/[id] - Get specific about content
const getHandler = requireRoles(["ADMIN"])(async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const aboutContent = await prisma.aboutPageContent.findUnique({
      where: { id: params.id },
      include: {
        updatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!aboutContent) {
      return NextResponse.json(
        { error: "About content not found" },
        { status: 404 }
      );
    }

    return createSuccessResponse(aboutContent);
  } catch (error) {
    console.error("Error fetching about content:", error);
    return handleApiError(error);
  }
});

// PUT /api/admin/about-content/[id] - Update about content
const putHandler = requireRoles(["ADMIN"])(async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();

    const validatedData = updateAboutContentSchema.parse(body);

    // Check if about content exists
    const existingContent = await prisma.aboutPageContent.findUnique({
      where: { id: params.id },
    });

    if (!existingContent) {
      return NextResponse.json(
        { error: "About content not found" },
        { status: 404 }
      );
    }

    const updateData: z.infer<typeof updateAboutContentSchema> & { updatedById: string } = {
      ...validatedData,
      updatedById: user.id,
    };

    const updatedContent = await prisma.aboutPageContent.update({
      where: { id: params.id },
      data: updateData,
      include: {
        updatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return createSuccessResponse(updatedContent);
  } catch (error) {
    console.error("Error updating about content:", error);
    return handleApiError(error);
  }
});

// DELETE /api/admin/about-content/[id] - Delete about content
const deleteHandler = requireRoles(["ADMIN"])(async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // Check if about content exists
    const existingContent = await prisma.aboutPageContent.findUnique({
      where: { id: params.id },
    });

    if (!existingContent) {
      return NextResponse.json(
        { error: "About content not found" },
        { status: 404 }
      );
    }

    await prisma.aboutPageContent.delete({
      where: { id: params.id },
    });

    return createSuccessResponse({ message: "About content deleted successfully" });
  } catch (error) {
    console.error("Error deleting about content:", error);
    return handleApiError(error);
  }
});

export const GET = getHandler;
export const PUT = putHandler;
export const DELETE = deleteHandler;
