import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");

    // Fetch recent activities from audit logs and system events
    const auditLogs = await prisma.auditLog.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { name: true, email: true }
        }
      }
    });

    // Transform audit logs to activity format
    const activities = auditLogs.map(log => ({
      id: log.id,
      type: log.action,
      description: `${log.user?.name || 'System'} ${log.action.toLowerCase().replace('_', ' ')} ${log.entityType}`,
      timestamp: log.createdAt.toISOString(),
      severity: log.action.includes('DELETE') ? 'HIGH' : log.action.includes('UPDATE') ? 'MEDIUM' : 'LOW',
      user: log.user?.name,
      entityType: log.entityType,
      entityId: log.entityId
    }));

    // Add some mock activities if no audit logs exist
    const mockActivities = activities.length === 0 ? [
      {
        id: "1",
        type: "USER_LOGIN",
        description: "Admin user logged into the system",
        timestamp: new Date().toISOString(),
        severity: "LOW",
      },
      {
        id: "2", 
        type: "VENDOR_VERIFICATION",
        description: "New vendor registration pending verification",
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        severity: "MEDIUM",
      },
      {
        id: "3",
        type: "PROCUREMENT_CREATED",
        description: "New procurement package created",
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        severity: "LOW",
      },
      {
        id: "4",
        type: "SYSTEM_BACKUP",
        description: "Daily system backup completed successfully",
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        severity: "LOW",
      },
      {
        id: "5",
        type: "SECURITY_ALERT",
        description: "Multiple failed login attempts detected",
        timestamp: new Date(Date.now() - 172800000).toISOString(),
        severity: "HIGH",
      },
    ] : [];

    // Return real activities or mock data if no real data exists
    const finalActivities = activities.length > 0 ? activities : mockActivities.slice(0, limit);

    return NextResponse.json({
      success: true,
      data: finalActivities,
    });

  } catch (error) {
    console.error("Error fetching admin activities:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch activities",
      },
      { status: 500 }
    );
  }
}