import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getCurrentUser } from '@/lib/auth';
// import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

const createVendorRequirementSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  category: z.enum(['LEGAL', 'FINANCIAL', 'TECHNICAL', 'EXPERIENCE']),
  type: z.enum(['MANDATORY', 'PREFERRED', 'SCORING']),
  templateId: z.string().optional(),
  criteria: z.object({
    description: z.string(),
    requirements: z.array(z.object({
      item: z.string(),
      description: z.string(),
      required: z.boolean().default(true),
      scoringCriteria: z.object({
        minScore: z.number().optional(),
        maxScore: z.number().optional(),
        weightage: z.number().optional(),
      }).optional(),
    })),
    evaluation: z.object({
      method: z.enum(['PASS_FAIL', 'SCORING', 'RANKING']),
      passingScore: z.number().optional(),
      maxScore: z.number().optional(),
    }),
  }),
  weight: z.number().min(0).max(100).optional(),
  validationRules: z.array(z.object({
    field: z.string(),
    rule: z.string(),
    value: z.any(),
    message: z.string(),
  })).optional(),
  requiredDocuments: z.array(z.object({
    name: z.string(),
    type: z.string(),
    required: z.boolean().default(true),
    format: z.array(z.string()).optional(),
    maxSize: z.number().optional(),
    description: z.string().optional(),
  })).optional(),
});

const updateVendorRequirementSchema = createVendorRequirementSchema.partial().extend({
  id: z.string(),
});

// GET /api/admin/vendor-requirements
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const type = searchParams.get('type');
    const templateId = searchParams.get('templateId');
    const isActive = searchParams.get('isActive');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (category) where.category = category;
    if (type) where.type = type;
    if (templateId) where.templateId = templateId;
    if (isActive !== null) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [requirements, total] = await Promise.all([
      prisma.vendorRequirementTemplate.findMany({
        where,
        include: {
          creator: {
            select: { id: true, name: true, email: true },
          },
          template: {
            select: { id: true, name: true, type: true, category: true },
          },
          _count: {
            select: {
              procurementRequirements: true,
            },
          },
        },
        orderBy: [
          { category: 'asc' },
          { type: 'asc' },
          { createdAt: 'desc' },
        ],
        skip,
        take: limit,
      }),
      prisma.vendorRequirementTemplate.count({ where }),
    ]);

    return NextResponse.json({
      requirements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching vendor requirements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch vendor requirements' },
      { status: 500 }
    );
  }
}

// POST /api/admin/vendor-requirements
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createVendorRequirementSchema.parse(body);

    // If templateId is provided, verify it exists
    if (validatedData.templateId) {
      const template = await prisma.procurementWorkflowTemplate.findUnique({
        where: { id: validatedData.templateId },
      });

      if (!template) {
        return NextResponse.json(
          { error: 'Workflow template not found' },
          { status: 404 }
        );
      }
    }

    const requirement = await prisma.vendorRequirementTemplate.create({
      data: {
        ...validatedData,
        createdBy: user.id,
      },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        template: {
          select: { id: true, name: true, type: true, category: true },
        },
      },
    });

    return NextResponse.json(requirement, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating vendor requirement:', error);
    return NextResponse.json(
      { error: 'Failed to create vendor requirement' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/vendor-requirements - Update vendor requirement
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateVendorRequirementSchema.parse(body);

    // Update vendor requirement by ID
    const { id, ...updateData } = validatedData;
    if (!id) {
      return NextResponse.json({ error: 'Vendor requirement ID is required' }, { status: 400 });
    }

    const updatedRequirement = await prisma.vendorRequirement.update({
      where: { id },
      data: {
        ...updateData,
        updatedById: user.id,
      },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true },
        },
        updatedBy: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedRequirement,
    });

  } catch (error) {
    console.error('Error updating vendor requirement:', error);
    return NextResponse.json(
      { error: 'Failed to update vendor requirement' },
      { status: 500 }
    );
  }
}
