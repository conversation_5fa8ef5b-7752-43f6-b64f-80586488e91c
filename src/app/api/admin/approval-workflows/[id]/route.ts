import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { enhancedWorkflowEngine } from '@/lib/approval/enhanced-workflow-engine';
import { auditUpdate, auditDelete, auditRead } from '@/lib/audit/audit-middleware';
import { getCurrentUser } from '@/lib/auth';
import { handleApiError, createSuccessResponse } from '@/lib/errors';
import { requireRoles } from '@/lib/security/rbac-middleware';

const updateWorkflowSchema = z.object({
  name: z.string().min(1, 'Workflow name is required').optional(),
  description: z.string().optional(),
  entityType: z.string().min(1, 'Entity type is required').optional(),
  stage: z.enum([
    'vendor_registration',
    'rfq_creation', 
    'rfq_publication',
    'offer_submission',
    'offer_evaluation',
    'contract_award',
    'po_creation',
    'po_approval',
    'delivery_confirmation',
    'invoice_processing',
    'payment_approval',
    'contract_completion'
  ]).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  steps: z.array(z.object({
    id: z.string().optional(), // For existing steps
    name: z.string().min(1, 'Step name is required'),
    description: z.string().optional(),
    sequence: z.number().min(1),
    stepType: z.enum(['APPROVAL', 'REVIEW', 'NOTIFICATION', 'CONDITIONAL', 'PARALLEL', 'SEQUENTIAL', 'ESCALATION', 'SIGNATURE']),
    isRequired: z.boolean(),
    approverAssignment: z.object({
      type: z.enum(['SPECIFIC_USER', 'ROLE_BASED', 'DEPARTMENT', 'HIERARCHY', 'DYNAMIC', 'COMMITTEE', 'VALUE_THRESHOLD']),
      config: z.object({
        userIds: z.array(z.string()).optional(),
        roleNames: z.array(z.string()).optional(),
        departmentIds: z.array(z.string()).optional(),
        hierarchyLevel: z.number().optional(),
        dynamicRule: z.string().optional(),
        committeeType: z.string().optional(),
        valueThresholds: z.array(z.object({
          minValue: z.number(),
          maxValue: z.number().optional(),
          approverIds: z.array(z.string()),
        })).optional(),
      }),
    }),
    requiredCount: z.number().min(1),
    allowDelegation: z.boolean(),
    timeoutHours: z.number().optional(),
    escalationConfig: z.object({
      escalateToIds: z.array(z.string()),
      escalationMessage: z.string(),
    }).optional(),
    signatureConfig: z.object({
      position: z.object({
        x: z.number(),
        y: z.number(),
      }),
      size: z.object({
        width: z.number(),
        height: z.number(),
      }),
      page: z.number().optional(),
      required: z.boolean(),
    }).optional(),
  })).optional(),
  conditions: z.array(z.object({
    field: z.string(),
    operator: z.enum(['eq', 'gt', 'gte', 'lt', 'lte', 'in', 'contains']),
    value: z.any(),
    logicalOperator: z.enum(['AND', 'OR']).optional(),
  })).optional(),
});

// Get specific approval workflow
const getHandler = requireRoles(['ADMIN', 'COMMITTEE'])(
  auditRead('approval_workflows')(async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);

      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      // Get workflow usage statistics
      const usageStats = await enhancedWorkflowEngine.getWorkflowUsageStats(params.id);

      return createSuccessResponse({
        workflow: {
          ...workflow,
          usageStats,
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Update approval workflow
const updateHandler = requireRoles(['ADMIN'])(
  auditUpdate('approval_workflows', {
    description: 'Update approval workflow configuration',
    severity: 'HIGH',
    category: 'APPROVAL_WORKFLOW',
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = updateWorkflowSchema.parse(body);

      // Check if workflow exists
      const existingWorkflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!existingWorkflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      // If setting as default, unset other defaults for the same entity type and stage
      if (validatedData.isDefault) {
        const entityType = validatedData.entityType || existingWorkflow.entityType;
        const stage = validatedData.stage || existingWorkflow.stage;
        
        await enhancedWorkflowEngine.unsetDefaultWorkflows(entityType, stage || '', params.id);
      }

      // Transform steps if provided
      let transformedSteps;
      if (validatedData.steps) {
        transformedSteps = validatedData.steps.map((step: any) => ({
          id: step.id,
          name: step.name,
          description: step.description,
          sequence: step.sequence,
          stepType: step.stepType as any,
          isRequired: step.isRequired,
          approverAssignment: step.approverAssignment,
          requiredCount: step.requiredCount,
          allowDelegation: step.allowDelegation,
          timeoutHours: step.timeoutHours,
          escalationConfig: step.escalationConfig,
          signatureConfig: step.signatureConfig,
        }));
      }

      // Update workflow
      const updatedWorkflow = await enhancedWorkflowEngine.updateWorkflow(params.id, {
        name: validatedData.name,
        description: validatedData.description,
        entityType: validatedData.entityType,
        stage: validatedData.stage as any,
        isActive: validatedData.isActive,
        isDefault: validatedData.isDefault,
        steps: transformedSteps,
        conditions: validatedData.conditions as any,
      });

      // Log the update
      console.log(`Approval workflow updated by ${user.email}:`, {
        workflowId: params.id,
        changes: Object.keys(validatedData),
      });

      return createSuccessResponse({
        workflow: updatedWorkflow,
        message: 'Approval workflow updated successfully',
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Delete approval workflow
const deleteHandler = requireRoles(['ADMIN'])(
  auditDelete('approval_workflows', {
    description: 'Delete approval workflow',
    severity: 'HIGH',
    category: 'APPROVAL_WORKFLOW',
  })(async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);

      // Check if workflow exists
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      // Check if workflow is being used
      const usageStats = await enhancedWorkflowEngine.getWorkflowUsageStats(params.id);
      if (usageStats.activeInstances > 0) {
        return NextResponse.json(
          { error: 'Cannot delete workflow with active approval instances' },
          { status: 400 }
        );
      }

      // Delete workflow
      await enhancedWorkflowEngine.deleteWorkflow(params.id);

      // Log the deletion
      console.log(`Approval workflow deleted by ${user.email}:`, {
        workflowId: params.id,
        workflowName: workflow.name,
      });

      return createSuccessResponse({
        message: 'Approval workflow deleted successfully',
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { getHandler as GET, updateHandler as PUT, deleteHandler as DELETE };
