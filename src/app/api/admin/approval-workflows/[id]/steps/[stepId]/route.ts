import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { enhancedWorkflowEngine } from '@/lib/approval/enhanced-workflow-engine';
import { auditUpdate, auditDelete, auditRead } from '@/lib/audit/audit-middleware';
import { getCurrentUser } from '@/lib/auth';
import { handleApiError, createSuccessResponse } from '@/lib/errors';
import { requireRoles } from '@/lib/security/rbac-middleware';

const updateStepSchema = z.object({
  name: z.string().min(1, 'Step name is required').optional(),
  description: z.string().optional(),
  sequence: z.number().min(1).optional(),
  stepType: z.enum(['APPROVAL', 'REVIEW', 'NOTIFICATION', 'CONDITIONAL', 'PARALLEL', 'SEQUENTIAL', 'ESCALATION', 'SIGNATURE']).optional(),
  isRequired: z.boolean().optional(),
  approverAssignment: z.object({
    type: z.enum(['SPECIFIC_USER', 'ROLE_BASED', 'DEPARTMENT', 'HIERARCHY', 'DYNAMIC', 'COMMITTEE', 'VALUE_THRESHOLD']),
    config: z.object({
      userIds: z.array(z.string()).optional(),
      roleNames: z.array(z.string()).optional(),
      departmentIds: z.array(z.string()).optional(),
      hierarchyLevel: z.number().optional(),
      dynamicRule: z.string().optional(),
      committeeType: z.string().optional(),
      valueThresholds: z.array(z.object({
        minValue: z.number(),
        maxValue: z.number().optional(),
        approverIds: z.array(z.string()),
      })).optional(),
    }),
  }).optional(),
  requiredCount: z.number().min(1).optional(),
  allowDelegation: z.boolean().optional(),
  timeoutHours: z.number().optional(),
  escalationConfig: z.object({
    escalateToIds: z.array(z.string()),
    escalationMessage: z.string(),
  }).optional(),
  signatureConfig: z.object({
    position: z.object({
      x: z.number(),
      y: z.number(),
    }),
    size: z.object({
      width: z.number(),
      height: z.number(),
    }),
    page: z.number().optional(),
    required: z.boolean(),
  }).optional(),
});

// Get specific workflow step
const getHandler = requireRoles(['ADMIN', 'COMMITTEE'])(
  auditRead('approval_workflow_steps')(async function GET(
    request: NextRequest,
    { params }: { params: { id: string; stepId: string } }
  ) {
    try {
      // Verify workflow exists
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      const step = await enhancedWorkflowEngine.getWorkflowStepById(params.stepId);
      
      if (!step || step.workflowId !== params.id) {
        return NextResponse.json(
          { error: 'Workflow step not found' },
          { status: 404 }
        );
      }

      return createSuccessResponse({ step });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Update workflow step
const updateHandler = requireRoles(['ADMIN'])(
  auditUpdate('approval_workflow_steps', {
    description: 'Update approval workflow step',
    severity: 'MEDIUM',
    category: 'APPROVAL_WORKFLOW',
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string; stepId: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = updateStepSchema.parse(body);

      // Verify workflow and step exist
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      const existingStep = await enhancedWorkflowEngine.getWorkflowStepById(params.stepId);
      if (!existingStep || existingStep.workflowId !== params.id) {
        return NextResponse.json(
          { error: 'Workflow step not found' },
          { status: 404 }
        );
      }

      // If updating sequence, check for conflicts
      if (validatedData.sequence && validatedData.sequence !== existingStep.sequence) {
        const existingSteps = await enhancedWorkflowEngine.getWorkflowSteps(params.id);
        const sequenceExists = existingSteps.some(
          step => step.sequence === validatedData.sequence && step.id !== params.stepId
        );
        
        if (sequenceExists) {
          return NextResponse.json(
            { error: 'Step with this sequence already exists' },
            { status: 400 }
          );
        }
      }

      // Update step
      const updatedStep = await enhancedWorkflowEngine.updateWorkflowStep(params.stepId, {
        name: validatedData.name,
        description: validatedData.description,
        sequence: validatedData.sequence,
        stepType: validatedData.stepType as any,
        isRequired: validatedData.isRequired,
        approverAssignment: validatedData.approverAssignment,
        requiredCount: validatedData.requiredCount,
        allowDelegation: validatedData.allowDelegation,
        timeoutHours: validatedData.timeoutHours,
        escalationConfig: validatedData.escalationConfig,
        signatureConfig: validatedData.signatureConfig,
      });

      // Log the update
      console.log(`Approval workflow step updated by ${user.email}:`, {
        workflowId: params.id,
        stepId: params.stepId,
        changes: Object.keys(validatedData),
      });

      return createSuccessResponse({
        step: updatedStep,
        message: 'Approval workflow step updated successfully',
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Delete workflow step
const deleteHandler = requireRoles(['ADMIN'])(
  auditDelete('approval_workflow_steps', {
    description: 'Delete approval workflow step',
    severity: 'MEDIUM',
    category: 'APPROVAL_WORKFLOW',
  })(async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string; stepId: string } }
  ) {
    try {
      const user = await getCurrentUser(request);

      // Verify workflow and step exist
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      const step = await enhancedWorkflowEngine.getWorkflowStepById(params.stepId);
      if (!step || step.workflowId !== params.id) {
        return NextResponse.json(
          { error: 'Workflow step not found' },
          { status: 404 }
        );
      }

      // Check if this is the last required step
      const allSteps = await enhancedWorkflowEngine.getWorkflowSteps(params.id);
      const requiredSteps = allSteps.filter(s => s.isRequired);
      
      if (step.isRequired && requiredSteps.length <= 1) {
        return NextResponse.json(
          { error: 'Cannot delete the last required step' },
          { status: 400 }
        );
      }

      // Check if step is being used in active instances
      const stepUsage = await enhancedWorkflowEngine.getStepUsageStats(params.stepId);
      if (stepUsage.activeInstances > 0) {
        return NextResponse.json(
          { error: 'Cannot delete step with active approval instances' },
          { status: 400 }
        );
      }

      // Delete step
      await enhancedWorkflowEngine.deleteWorkflowStep(params.stepId);

      // Reorder remaining steps to fill gaps
      const remainingSteps = allSteps
        .filter(s => s.id !== params.stepId)
        .sort((a, b) => a.sequence - b.sequence);

      if (remainingSteps.length > 0) {
        const reorderData = remainingSteps.map((step, index) => ({
          id: step.id,
          sequence: index + 1,
        }));

        await enhancedWorkflowEngine.reorderWorkflowSteps(params.id, reorderData);
      }

      // Log the deletion
      console.log(`Approval workflow step deleted by ${user.email}:`, {
        workflowId: params.id,
        stepId: params.stepId,
        stepName: step.name,
      });

      return createSuccessResponse({
        message: 'Approval workflow step deleted successfully',
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { getHandler as GET, updateHandler as PUT, deleteHandler as DELETE };
