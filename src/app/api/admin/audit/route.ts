import { NextRequest } from "next/server";
import { z } from "zod";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getAuditLogs } from "@/lib/security/audit-trail";
import { requirePermissions } from "@/lib/security/rbac-middleware";

const auditSearchSchema = z.object({
  userId: z.string().optional(),
  action: z.string().optional(),
  resource: z.string().optional(),
  severity: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]).optional(),
  category: z.enum([
    "GENERAL",
    "AUTHENTICATION", 
    "AUTHORIZATION",
    "DATA_ACCESS",
    "DATA_MODIFICATION",
    "SYSTEM_CONFIGURATION",
    "PROCUREMENT_PROCESS",
    "VENDOR_MANAGEMENT",
    "FINANCIAL_TRANSACTION",
    "SECURITY_INCIDENT"
  ]).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(50),
});

async function handleGET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const validatedParams = auditSearchSchema.parse({
      userId: searchParams.get("userId") || undefined,
      action: searchParams.get("action") || undefined,
      resource: searchParams.get("resource") || undefined,
      severity: searchParams.get("severity") || undefined,
      category: searchParams.get("category") || undefined,
      startDate: searchParams.get("startDate") || undefined,
      endDate: searchParams.get("endDate") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "50"),
    });

    // Convert date strings to Date objects
    const filters = {
      ...validatedParams,
      startDate: validatedParams.startDate ? new Date(validatedParams.startDate) : undefined,
      endDate: validatedParams.endDate ? new Date(validatedParams.endDate) : undefined,
    };

    const result = await getAuditLogs(filters);

    return createSuccessResponse(result);
  } catch (error) {
    return handleApiError(error);
  }
}

// Require audit.read permission
export const GET = requirePermissions([
  { resource: "audit", action: "read" }
])(handleGET);
