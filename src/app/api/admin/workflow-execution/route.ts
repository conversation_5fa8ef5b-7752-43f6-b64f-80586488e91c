import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { Prisma } from '@prisma/client';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';

const executeWorkflowSchema = z.object({
  workflowId: z.string().min(1, "Workflow ID is required"),
  entityId: z.string().min(1, "Entity ID is required"),
  entityType: z.string().min(1, "Entity type is required"),
  metadata: z.record(z.unknown()).optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
  dueDate: z.string().optional(),
});

const updateWorkflowStatusSchema = z.object({
  status: z.enum(['PENDING', 'IN_PROGRESS', 'APPROVED', 'REJECTED', 'CANCELLED']),
  comment: z.string().optional(),
  metadata: z.record(z.unknown()).optional(),
});

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const entityType = searchParams.get('entityType');
    const workflowId = searchParams.get('workflowId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build where clause
    const where: Prisma.ApprovalInstanceWhereInput = {};
    if (status) where.status = status as any;
    if (entityType) where.workflow = { entityType };
    if (workflowId) where.workflowId = workflowId;

    const [instances, total] = await Promise.all([
      prisma.approvalInstance.findMany({
        where,
        include: {
          workflow: {
            select: {
              id: true,
              name: true,
              entityType: true,
              steps: true,
            },
          },
          initiatedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          stepInstances: {
            include: {
              step: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  sequence: true,
                },
              },
            },
            orderBy: { sequence: 'asc' },
          },
        },
        orderBy: { startedAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.approvalInstance.count({ where }),
    ]);

    return NextResponse.json({
      instances,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching workflow instances:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow instances' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = executeWorkflowSchema.parse(body);

    // Check if workflow exists
    const workflow = await prisma.approvalWorkflow.findUnique({
      where: { id: validatedData.workflowId },
      include: { steps: { orderBy: { sequence: 'asc' } } },
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    if (!workflow.isActive) {
      return NextResponse.json(
        { error: 'Workflow is not active' },
        { status: 400 }
      );
    }

    // Create workflow instance
    const instance = await prisma.approvalInstance.create({
      data: {
        workflowId: validatedData.workflowId,
        entityId: validatedData.entityId,
        entityType: validatedData.entityType,
        status: 'PENDING',
        priority: validatedData.priority,
        dueDate: validatedData.dueDate ? new Date(validatedData.dueDate) : null,
        metadata: validatedData.metadata as any || {},
        initiatedById: user.id,
      },
    });

    // Create approval step instances based on workflow steps
    const stepInstances = await Promise.all(
      workflow.steps.map(async (step, index) => {
        return prisma.approvalStepInstance.create({
          data: {
            instanceId: instance.id,
            stepId: step.id,
            sequence: step.sequence,
            status: index === 0 ? 'PENDING' : 'PENDING',
            dueDate: validatedData.dueDate
              ? new Date(new Date(validatedData.dueDate).getTime() + (step.timeoutHours || 24) * 60 * 60 * 1000)
              : null,
          },
        });
      })
    );

    // Return the created instance with step instances
    const createdInstance = await prisma.approvalInstance.findUnique({
      where: { id: instance.id },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            entityType: true,
            steps: true,
          },
        },
        initiatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        stepInstances: {
          include: {
            step: {
              select: {
                id: true,
                name: true,
                description: true,
                sequence: true,
              },
            },
          },
          orderBy: { sequence: 'asc' },
        },
      },
    });

    return NextResponse.json(createdInstance, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error executing workflow:', error);
    return NextResponse.json(
      { error: 'Failed to execute workflow' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const instanceId = searchParams.get('instanceId');

    if (!instanceId) {
      return NextResponse.json(
        { error: 'Instance ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updateWorkflowStatusSchema.parse(body);

    // Check if instance exists
    const instance = await prisma.approvalInstance.findUnique({
      where: { id: instanceId },
      include: {
        stepInstances: { orderBy: { sequence: 'asc' } },
      },
    });

    if (!instance) {
      return NextResponse.json(
        { error: 'Workflow instance not found' },
        { status: 404 }
      );
    }

    // Update instance status
    const updatedInstance = await prisma.approvalInstance.update({
      where: { id: instanceId },
      data: {
        status: validatedData.status,
        completedAt: validatedData.status === 'APPROVED' ? new Date() : null,
        metadata: validatedData.metadata
          ? { ...(instance.metadata as Record<string, unknown> || {}), ...validatedData.metadata } as any
          : instance.metadata,
      },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            entityType: true,
            steps: true,
          },
        },
        initiatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        stepInstances: {
          include: {
            step: {
              select: {
                id: true,
                name: true,
                description: true,
                sequence: true,
              },
            },
          },
          orderBy: { sequence: 'asc' },
        },
      },
    });

    return NextResponse.json(updatedInstance);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating workflow status:', error);
    return NextResponse.json(
      { error: 'Failed to update workflow status' },
      { status: 500 }
    );
  }
}
