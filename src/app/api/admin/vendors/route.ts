import { Prisma } from "@prisma/client";
import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";

export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const user = await getCurrentUser(request);
    if (!user.roles.includes("ADMIN")) {
      return handleApiError(new Error("Unauthorized"));
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status") as "PENDING" | "VERIFIED" | "REJECTED" | null;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search");

    // Build where clause
    const where: Prisma.VendorWhereInput = {};
    if (status) {
      where.verificationStatus = status;
    }
    if (search) {
      where.OR = [
        { companyName: { contains: search, mode: "insensitive" } },
        { taxId: { contains: search } },
        { picName: { contains: search, mode: "insensitive" } },
        { user: { email: { contains: search, mode: "insensitive" } } },
      ];
    }

    // Get vendors with pagination
    const [vendors, total] = await Promise.all([
      prisma.vendor.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              createdAt: true,
            },
          },
          documents: {
            select: {
              id: true,
              fileName: true,
              fileUrl: true,
              fileType: true,
              description: true,
              uploadedAt: true,
            },
          },
          verificationSteps: {
            include: {
              verifiedBy: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: {
              stepName: "asc",
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.vendor.count({ where }),
    ]);

    return createSuccessResponse({
      vendors,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}
