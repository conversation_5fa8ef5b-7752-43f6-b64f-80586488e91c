import { VendorStatus } from "@prisma/client";
import { NextRequest } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { sendCombinedNotification } from "@/lib/notifications/notification-service";

const updateStatusSchema = z.object({
  status: z.enum(["VERIFIED", "REJECTED"]),
  rejectionReason: z.string().optional(),
});

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and authorization
    const currentUser = await getCurrentUser(request);
    if (!currentUser.roles.includes("ADMIN")) {
      return handleApiError(new Error("Unauthorized"));
    }

    const body = await request.json();
    const validatedData = updateStatusSchema.parse(body);

    // Validate rejection reason is provided when rejecting
    if (validatedData.status === "REJECTED" && !validatedData.rejectionReason) {
      return handleApiError(new Error("Rejection reason is required when rejecting a vendor"));
    }

    // Find the vendor
    const vendor = await prisma.vendor.findUnique({
      where: { id: params.id },
      include: {
        user: true,
      },
    });

    if (!vendor) {
      throw new NotFoundError("Vendor not found");
    }

    // Update vendor status
    const updatedVendor = await prisma.vendor.update({
      where: { id: params.id },
      data: {
        status: validatedData.status as VendorStatus,
        verifiedAt: validatedData.status === "VERIFIED" ? new Date() : null,
        verificationNotes: validatedData.rejectionReason || null,
      },
    });

    // Get user information separately
    const vendorUser = await prisma.user.findUnique({
      where: { id: updatedVendor.userId },
      select: {
        id: true,
        email: true,
        name: true,
      },
    });

    // Send notification email
    if (vendorUser) {
      if (validatedData.status === "VERIFIED") {
        await sendCombinedNotification(
          {
            userId: vendor.userId,
            title: "Akun Anda Telah Diverifikasi",
            message: "Selamat! Akun Anda telah disetujui. Anda sekarang dapat mengikuti proses pengadaan.",
            type: "SUCCESS",
          },
          {
            to: vendorUser.email,
            subject: "Akun Vendor Telah Diverifikasi",
            template: "vendor-status-change",
            data: {
              vendorName: vendorUser.name,
              status: "DIVERIFIKASI",
            },
          }
        );
      } else {
        await sendCombinedNotification(
          {
            userId: vendor.userId,
            title: "Verifikasi Akun Ditolak",
            message: `Verifikasi akun Anda ditolak. Alasan: ${validatedData.rejectionReason}`,
            type: "ERROR",
          },
          {
            to: vendorUser.email,
            subject: "Verifikasi Akun Vendor Ditolak",
            template: "vendor-status-change",
            data: {
              vendorName: vendorUser.name,
              status: "DITOLAK",
              reason: validatedData.rejectionReason,
            },
          }
        );
      }
    }

    return createSuccessResponse(
      updatedVendor,
      `Vendor ${validatedData.status === "VERIFIED" ? "verified" : "rejected"} successfully`
    );
  } catch (error) {
    return handleApiError(error);
  }
}
