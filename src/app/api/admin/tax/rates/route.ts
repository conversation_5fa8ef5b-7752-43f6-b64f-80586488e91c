import { NextRequest } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { withAudit } from "@/lib/security/audit-middleware";
import { requirePermissions } from "@/lib/security/rbac-middleware";
import { createTaxRate, getTaxHistory } from "@/lib/tax/tax-service";

const createTaxRateSchema = z.object({
  taxTypeCode: z.string().min(1, "Tax type code is required"),
  rate: z.number().min(0).max(1, "Rate must be between 0 and 1"),
  description: z.string().optional(),
  effectiveFrom: z.string().refine((date) => !isNaN(Date.parse(date)), "Invalid date"),
  effectiveTo: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), "Invalid date"),
});

async function handlePOST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createTaxRateSchema.parse(body);

    const taxRate = await createTaxRate({
      taxTypeCode: validatedData.taxTypeCode,
      rate: validatedData.rate,
      effectiveFrom: new Date(validatedData.effectiveFrom),
      effectiveTo: validatedData.effectiveTo ? new Date(validatedData.effectiveTo) : undefined,
    });

    return createSuccessResponse(
      { id: taxRate.id },
      "Tax rate created successfully",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}

const getTaxHistorySchema = z.object({
  taxTypeCode: z.string().min(1, "Tax type code is required"),
});

async function handleGET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const taxTypeCode = searchParams.get("taxTypeCode");

    // Validate query parameters using schema
    const validatedParams = getTaxHistorySchema.parse({ taxTypeCode });

    const history = await getTaxHistory(validatedParams.taxTypeCode);

    return createSuccessResponse(history);
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply RBAC and audit logging
export const POST = requirePermissions([
  { resource: "tax", action: "create" }
])(
  withAudit({
    action: "CREATE",
    resource: "tax_rate",
    severity: "HIGH",
    category: "SYSTEM_CONFIGURATION",
    captureRequest: true,
  })(handlePOST)
);

export const GET = requirePermissions([
  { resource: "tax", action: "read" }
])(handleGET);
