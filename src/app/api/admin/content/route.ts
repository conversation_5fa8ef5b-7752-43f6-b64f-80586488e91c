import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { requireRoles } from "@/lib/security/rbac-middleware";

const createContentSchema = z.object({
  type: z.string().min(1, "Type is required"),
  title: z.string().min(1, "Title is required"),
  content: z.string().optional(),
  excerpt: z.string().optional(),
  imageUrl: z.string().optional(),
  fileUrl: z.string().optional(),
  status: z.enum(["DRAFT", "PUBLISHED"]),
  metadata: z.record(z.any()).optional(),
});

// GET /api/admin/content - List all content
const getHandler = requireRoles(["ADMIN"])(async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const where: any = {};
    if (type) where.type = type;

    const [content, totalCount] = await Promise.all([
      prisma.content.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.content.count({ where }),
    ]);

    return NextResponse.json({
      content,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Admin content API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// POST /api/admin/content - Create new content
const postHandler = requireRoles(["ADMIN"])(async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const validatedData = createContentSchema.parse(body);

    // Generate slug from title for certain content types
    let slug: string | undefined;
    if (["news", "announcement", "page"].includes(validatedData.type)) {
      slug = validatedData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .replace(/^-+|-+$/g, "");

      // Ensure slug is unique
      let uniqueSlug = slug;
      let counter = 1;
      while (await prisma.content.findFirst({ where: { slug: uniqueSlug, type: validatedData.type } })) {
        uniqueSlug = `${slug}-${counter}`;
        counter++;
      }
      slug = uniqueSlug;
    }

    const contentData: any = {
      type: validatedData.type,
      title: validatedData.title,
      slug,
      content: validatedData.content,
      excerpt: validatedData.excerpt,
      imageUrl: validatedData.imageUrl,
      fileUrl: validatedData.fileUrl,
      status: validatedData.status,
      authorId: user.id,
      metadata: validatedData.metadata || {},
    };

    // Set publishedAt if status is PUBLISHED
    if (validatedData.status === "PUBLISHED") {
      contentData.publishedAt = new Date();
    }

    const content = await prisma.content.create({
      data: contentData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(content, { status: 201 });
  } catch (error) {
    console.error("Create content error:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

export const GET = getHandler;
export const POST = postHandler;