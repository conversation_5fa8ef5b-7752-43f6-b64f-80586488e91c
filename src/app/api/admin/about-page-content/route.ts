import { NextRequest } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createSuccessResponse, handleApiError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

const updateAboutPageContentSchema = z.object({
  heroTitle: z.string().optional(),
  heroSubtitle: z.string().optional(),
  heroDescription: z.string().optional(),
  aboutTitle: z.string().optional(),
  aboutDescription: z.string().optional(),
  companyTitle: z.string().optional(),
  companyDescription: z.string().optional(),
  companyName: z.string().optional(),
  companyFullName: z.string().optional(),
  operationalArea: z.string().optional(),
  vision: z.string().optional(),
  features: z.array(z.string()).optional(),
  contactTitle: z.string().optional(),
  contactDescription: z.string().optional(),
  address: z.string().optional(),
  phones: z.array(z.string()).optional(),
  emails: z.array(z.string()).optional(),
  ctaTitle: z.string().optional(),
  ctaDescription: z.string().optional(),
});

// GET /api/admin/about-page-content - Get about page content
const getHandler = requireRoles(["ADMIN"])(async function GET(request: NextRequest) {
  try {
    // Get or create the about page content (singleton)
    let content = await prisma.aboutPageContent.findFirst({
      include: {
        updatedBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // If no content exists, create default content
    if (!content) {
      const user = await getCurrentUser(request);
      content = await prisma.aboutPageContent.create({
        data: {
          updatedById: user.id,
        },
        include: {
          updatedBy: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    }

    // Parse JSON fields
    const response = {
      ...content,
      features: Array.isArray(content.features) ? content.features : JSON.parse(content.features as string),
      phones: Array.isArray(content.phones) ? content.phones : JSON.parse(content.phones as string),
      emails: Array.isArray(content.emails) ? content.emails : JSON.parse(content.emails as string),
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// PUT /api/admin/about-page-content - Update about page content
const putHandler = requireRoles(["ADMIN"])(async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();
    const validatedData = updateAboutPageContentSchema.parse(body);

    // Get or create the about page content (singleton)
    let content = await prisma.aboutPageContent.findFirst();

    if (!content) {
      // Create new content with provided data
      content = await prisma.aboutPageContent.create({
        data: {
          ...validatedData,
          updatedById: user.id,
        },
        include: {
          updatedBy: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    } else {
      // Update existing content
      content = await prisma.aboutPageContent.update({
        where: { id: content.id },
        data: {
          ...validatedData,
          updatedById: user.id,
        },
        include: {
          updatedBy: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    }

    // Parse JSON fields for response
    const response = {
      ...content,
      features: Array.isArray(content.features) ? content.features : JSON.parse(content.features as string),
      phones: Array.isArray(content.phones) ? content.phones : JSON.parse(content.phones as string),
      emails: Array.isArray(content.emails) ? content.emails : JSON.parse(content.emails as string),
    };

    return createSuccessResponse(response, "About page content updated successfully");
  } catch (error) {
    return handleApiError(error);
  }
});

export const GET = getHandler;
export const PUT = putHandler;
