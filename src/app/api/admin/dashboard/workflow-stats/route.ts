import { ApprovalInstanceStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get workflow statistics
    const [
      totalWorkflows,
      activeWorkflows,
      totalTemplates,
      activeTemplates,
      totalSchedules,
      activeSchedules,
      totalRequirements,
      activeRequirements,
    ] = await Promise.all([
      prisma.approvalWorkflow.count(),
      prisma.approvalWorkflow.count({ where: { isActive: true } }),
      prisma.procurementWorkflowTemplate.count(),
      prisma.procurementWorkflowTemplate.count({ where: { isActive: true } }),
      prisma.procurementScheduleTemplate.count(),
      prisma.procurementScheduleTemplate.count({ where: { isActive: true } }),
      prisma.vendorRequirementTemplate.count(),
      prisma.vendorRequirementTemplate.count({ where: { isActive: true } }),
    ]);

    // Calculate average processing time (in days)
    const completedInstances = await prisma.approvalInstance.findMany({
      where: {
        status: ApprovalInstanceStatus.APPROVED,
        completedAt: { not: null },
      },
      select: {
        startedAt: true,
        completedAt: true,
      },
    });

    let avgProcessingTime = 0;
    if (completedInstances.length > 0) {
      const totalProcessingTime = completedInstances.reduce((total, instance) => {
        const processingTime = instance.completedAt!.getTime() - instance.startedAt.getTime();
        return total + processingTime;
      }, 0);
      
      avgProcessingTime = Math.round(
        totalProcessingTime / completedInstances.length / (1000 * 60 * 60 * 24)
      );
    }

    // Calculate completion rate
    const totalInstances = await prisma.approvalInstance.count();
    const completedCount = completedInstances.length;
    const completionRate = totalInstances > 0 
      ? Math.round((completedCount / totalInstances) * 100)
      : 0;

    // Count overdue items
    const now = new Date();
    const overdueCount = await prisma.approvalInstance.count({
      where: {
        status: { in: [ApprovalInstanceStatus.PENDING, ApprovalInstanceStatus.IN_PROGRESS] },
        dueDate: { lt: now },
      },
    });

    // Count upcoming deadlines (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    
    const upcomingDeadlines = await prisma.approvalInstance.count({
      where: {
        status: { in: [ApprovalInstanceStatus.PENDING, ApprovalInstanceStatus.IN_PROGRESS] },
        dueDate: {
          gte: now,
          lte: nextWeek,
        },
      },
    });

    const stats = {
      totalWorkflows,
      activeWorkflows,
      totalTemplates,
      activeTemplates,
      totalSchedules,
      activeSchedules,
      totalRequirements,
      activeRequirements,
      avgProcessingTime,
      completionRate,
      overdueCount,
      upcomingDeadlines,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching workflow stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow stats' },
      { status: 500 }
    );
  }
}
