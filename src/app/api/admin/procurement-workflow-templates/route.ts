import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';

// Validation schemas
const createWorkflowTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  type: z.enum(['TENDER', 'RFQ', 'DIRECT_PURCHASE', 'FRAMEWORK']),
  category: z.enum(['GOODS', 'SERVICES', 'CONSTRUCTION']),
  isDefault: z.boolean().default(false),
  config: z.object({
    stages: z.array(z.object({
      name: z.string(),
      type: z.string(),
      sequence: z.number(),
      duration: z.number().optional(),
      requirements: z.array(z.string()).optional(),
    })),
    rules: z.object({
      minValue: z.number().optional(),
      maxValue: z.number().optional(),
      requiresCommittee: z.boolean().default(false),
      autoApproval: z.boolean().default(false),
    }).optional(),
    conditions: z.array(z.object({
      field: z.string(),
      operator: z.string(),
      value: z.any(),
    })).optional(),
  }),
});

const updateWorkflowTemplateSchema = createWorkflowTemplateSchema.partial().extend({
  id: z.string(),
});

// GET /api/admin/procurement-workflow-templates
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const isActive = searchParams.get('isActive');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (type) where.type = type;
    if (category) where.category = category;
    if (isActive !== null) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [templates, total] = await Promise.all([
      prisma.procurementWorkflowTemplate.findMany({
        where,
        include: {
          creator: {
            select: { id: true, name: true, email: true },
          },
          stages: {
            orderBy: { sequence: 'asc' },
          },
          vendorRequirements: {
            where: { isActive: true },
          },
          scheduleTemplates: {
            where: { isActive: true },
          },
          _count: {
            select: {
              procurements: true,
              stages: true,
              vendorRequirements: true,
            },
          },
        },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' },
        ],
        skip,
        take: limit,
      }),
      prisma.procurementWorkflowTemplate.count({ where }),
    ]);

    return NextResponse.json({
      templates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching workflow templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow templates' },
      { status: 500 }
    );
  }
}

// POST /api/admin/procurement-workflow-templates
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createWorkflowTemplateSchema.parse(body);

    // If setting as default, unset other defaults for the same type/category
    if (validatedData.isDefault) {
      await prisma.procurementWorkflowTemplate.updateMany({
        where: {
          type: validatedData.type,
          category: validatedData.category,
          isDefault: true,
        },
        data: { isDefault: false },
      });
    }

    const template = await prisma.procurementWorkflowTemplate.create({
      data: {
        ...validatedData,
        createdBy: user.id,
      },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        stages: true,
        vendorRequirements: true,
        scheduleTemplates: true,
      },
    });

    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating workflow template:', error);
    return NextResponse.json(
      { error: 'Failed to create workflow template' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/procurement-workflow-templates - Update workflow template
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateWorkflowTemplateSchema.parse(body);

    // Update workflow template by ID
    const { id, ...updateData } = validatedData;
    if (!id) {
      return NextResponse.json({ error: 'Workflow template ID is required' }, { status: 400 });
    }

    const updatedTemplate = await prisma.procurementWorkflowTemplate.update({
      where: { id },
      data: {
        ...updateData,
        updatedById: user.id,
      },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true },
        },
        updatedBy: {
          select: { id: true, name: true, email: true },
        },
        stages: {
          include: {
            approvers: {
              select: { id: true, name: true, email: true },
            },
            documents: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedTemplate,
    });

  } catch (error) {
    console.error('Error updating workflow template:', error);
    return NextResponse.json(
      { error: 'Failed to update workflow template' },
      { status: 500 }
    );
  }
}
