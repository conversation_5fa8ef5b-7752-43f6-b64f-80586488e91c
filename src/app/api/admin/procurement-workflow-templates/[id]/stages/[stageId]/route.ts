import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getCurrentUser } from '@/lib/auth';
// import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

const updateStageSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  sequence: z.number().min(1).optional(),
  type: z.enum(['ANNOUNCEMENT', 'SUBMISSION', 'EVALUATION', 'AWARD', 'CONTRACT']).optional(),
  isRequired: z.boolean().optional(),
  config: z.object({
    rules: z.array(z.object({
      type: z.string(),
      condition: z.string(),
      value: z.any(),
    })).optional(),
    validations: z.array(z.object({
      field: z.string(),
      rule: z.string(),
      message: z.string(),
    })).optional(),
    notifications: z.object({
      onStart: z.boolean().default(false),
      onComplete: z.boolean().default(false),
      recipients: z.array(z.string()).optional(),
    }).optional(),
  }).optional(),
  minDuration: z.number().optional(),
  maxDuration: z.number().optional(),
  dependencies: z.array(z.string()).optional(),
  requiresApproval: z.boolean().optional(),
  approvalConfig: z.object({
    approverType: z.string(),
    approvers: z.array(z.string()).optional(),
    requiredCount: z.number().default(1),
    timeoutHours: z.number().optional(),
  }).optional(),
  requiredDocuments: z.array(z.object({
    name: z.string(),
    type: z.string(),
    required: z.boolean().default(true),
    template: z.string().optional(),
  })).optional(),
});

// GET /api/admin/procurement-workflow-templates/[id]/stages/[stageId]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; stageId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const stage = await prisma.procurementWorkflowStageTemplate.findUnique({
      where: { id: params.stageId },
      include: {
        template: {
          select: { id: true, name: true, type: true, category: true },
        },
      },
    });

    if (!stage || stage.templateId !== params.id) {
      return NextResponse.json(
        { error: 'Workflow stage not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(stage);
  } catch (error) {
    console.error('Error fetching workflow stage:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow stage' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/procurement-workflow-templates/[id]/stages/[stageId]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; stageId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateStageSchema.parse(body);

    // Check if stage exists
    const existingStage = await prisma.procurementWorkflowStageTemplate.findUnique({
      where: { id: params.stageId },
    });

    if (!existingStage || existingStage.templateId !== params.id) {
      return NextResponse.json(
        { error: 'Workflow stage not found' },
        { status: 404 }
      );
    }

    // If updating sequence, check for conflicts
    if (validatedData.sequence && validatedData.sequence !== existingStage.sequence) {
      const conflictingStage = await prisma.procurementWorkflowStageTemplate.findFirst({
        where: {
          templateId: params.id,
          sequence: validatedData.sequence,
          id: { not: params.stageId },
        },
      });

      if (conflictingStage) {
        return NextResponse.json(
          { error: 'Stage with this sequence already exists' },
          { status: 400 }
        );
      }
    }

    const stage = await prisma.procurementWorkflowStageTemplate.update({
      where: { id: params.stageId },
      data: validatedData,
      include: {
        template: {
          select: { id: true, name: true, type: true, category: true },
        },
      },
    });

    return NextResponse.json(stage);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating workflow stage:', error);
    return NextResponse.json(
      { error: 'Failed to update workflow stage' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/procurement-workflow-templates/[id]/stages/[stageId]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; stageId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if stage exists
    const stage = await prisma.procurementWorkflowStageTemplate.findUnique({
      where: { id: params.stageId },
    });

    if (!stage || stage.templateId !== params.id) {
      return NextResponse.json(
        { error: 'Workflow stage not found' },
        { status: 404 }
      );
    }

    // Check if this is a required stage
    if (stage.isRequired) {
      const stageCount = await prisma.procurementWorkflowStageTemplate.count({
        where: { templateId: params.id },
      });

      if (stageCount <= 1) {
        return NextResponse.json(
          { error: 'Cannot delete the last required stage' },
          { status: 400 }
        );
      }
    }

    await prisma.procurementWorkflowStageTemplate.delete({
      where: { id: params.stageId },
    });

    // Reorder remaining stages to fill gaps
    const remainingStages = await prisma.procurementWorkflowStageTemplate.findMany({
      where: { templateId: params.id },
      orderBy: { sequence: 'asc' },
    });

    await prisma.$transaction(
      remainingStages.map((stage, index) =>
        prisma.procurementWorkflowStageTemplate.update({
          where: { id: stage.id },
          data: { sequence: index + 1 },
        })
      )
    );

    return NextResponse.json({ message: 'Workflow stage deleted successfully' });
  } catch (error) {
    console.error('Error deleting workflow stage:', error);
    return NextResponse.json(
      { error: 'Failed to delete workflow stage' },
      { status: 500 }
    );
  }
}
