import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    // For now, return mock pending approvals since we don't have an approvals table
    // In a real application, you would fetch from an approvals or workflow_items table
    const mockApprovals = [
      {
        id: "1",
        type: "VENDOR_VERIFICATION",
        title: "PT. Technology Corporation - Vendor Verification",
        requestedBy: "Procurement Officer",
        requestedAt: new Date(Date.now() - 3600000).toISOString(),
        priority: "HIGH",
      },
      {
        id: "2",
        type: "PROCUREMENT_APPROVAL", 
        title: "Office Equipment Procurement - Phase 2",
        requestedBy: "Budi Santoso",
        requestedAt: new Date(Date.now() - 7200000).toISOString(),
        priority: "MEDIUM",
      },
      {
        id: "3",
        type: "CONTRACT_APPROVAL",
        title: "IT Services Contract Amendment",
        requestedBy: "<PERSON><PERSON>hay<PERSON>",
        requestedAt: new Date(Date.now() - 14400000).toISOString(),
        priority: "LOW",
      },
      {
        id: "4",
        type: "BUDGET_APPROVAL",
        title: "Q4 Budget Allocation Request",
        requestedBy: "Finance Department",
        requestedAt: new Date(Date.now() - 86400000).toISOString(),
        priority: "URGENT",
      },
    ];

    return NextResponse.json({
      success: true,
      data: mockApprovals,
    });

  } catch (error) {
    console.error("Error fetching pending approvals:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch pending approvals",
      },
      { status: 500 }
    );
  }
}