import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");

    // Fetch pending approvals from workflow items and approval requests
    const [workflowItems, vendorApprovals] = await Promise.all([
      prisma.workflowItem.findMany({
        where: { status: 'PENDING' },
        take: Math.ceil(limit / 2),
        orderBy: { createdAt: 'desc' },
        include: {
          assignedTo: { select: { name: true } },
          createdBy: { select: { name: true } }
        }
      }),
      prisma.vendor.findMany({
        where: { verificationStatus: 'PENDING' },
        take: Math.ceil(limit / 2),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          companyName: true,
          createdAt: true,
          createdBy: { select: { name: true } }
        }
      })
    ]);

    // Transform to unified approval format
    const approvals = [
      ...workflowItems.map(item => ({
        id: item.id,
        type: 'WORKFLOW_APPROVAL',
        title: `${item.entityType} - ${item.action}`,
        requestedBy: item.createdBy?.name || 'System',
        requestedAt: item.createdAt.toISOString(),
        priority: item.priority || 'MEDIUM',
        assignedTo: item.assignedTo?.name
      })),
      ...vendorApprovals.map(vendor => ({
        id: vendor.id,
        type: 'VENDOR_VERIFICATION',
        title: `${vendor.companyName} - Vendor Verification`,
        requestedBy: vendor.createdBy?.name || 'System',
        requestedAt: vendor.createdAt.toISOString(),
        priority: 'HIGH'
      }))
    ].slice(0, limit);

    // Add mock data if no real approvals exist
    const mockApprovals = approvals.length === 0 ? [
      {
        id: "1",
        type: "VENDOR_VERIFICATION",
        title: "PT. Technology Corporation - Vendor Verification",
        requestedBy: "Procurement Officer",
        requestedAt: new Date(Date.now() - 3600000).toISOString(),
        priority: "HIGH",
      },
      {
        id: "2",
        type: "PROCUREMENT_APPROVAL", 
        title: "Office Equipment Procurement - Phase 2",
        requestedBy: "Budi Santoso",
        requestedAt: new Date(Date.now() - 7200000).toISOString(),
        priority: "MEDIUM",
      },
      {
        id: "3",
        type: "CONTRACT_APPROVAL",
        title: "IT Services Contract Amendment",
        requestedBy: "Siti Rahayu",
        requestedAt: new Date(Date.now() - 14400000).toISOString(),
        priority: "LOW",
      },
      {
        id: "4",
        type: "BUDGET_APPROVAL",
        title: "Q4 Budget Allocation Request",
        requestedBy: "Finance Department",
        requestedAt: new Date(Date.now() - 86400000).toISOString(),
        priority: "URGENT",
      },
    ] : [];

    // Return real approvals or mock data if no real data exists
    const finalApprovals = approvals.length > 0 ? approvals : mockApprovals;

    return NextResponse.json({
      success: true,
      data: finalApprovals,
    });

  } catch (error) {
    console.error("Error fetching pending approvals:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch pending approvals",
      },
      { status: 500 }
    );
  }
}