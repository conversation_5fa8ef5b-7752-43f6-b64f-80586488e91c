import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");

    // Fetch pending approvals from vendor approvals (simplified approach)
    const vendorApprovals = await prisma.vendor.findMany({
      where: { verificationStatus: 'PENDING' },
      take: limit,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        companyName: true,
        createdAt: true,
      }
    });

    // Transform to unified approval format
    const approvals = vendorApprovals.map(vendor => ({
      id: vendor.id,
      type: 'VENDOR_VERIFICATION',
      title: `${vendor.companyName} - Vendor Verification`,
      requestedBy: 'System',
      requestedAt: vendor.createdAt.toISOString(),
      priority: 'HIGH'
    }));

    // Add mock data if no real approvals exist
    const mockApprovals = approvals.length === 0 ? [
      {
        id: "1",
        type: "VENDOR_VERIFICATION",
        title: "PT. Technology Corporation - Vendor Verification",
        requestedBy: "Procurement Officer",
        requestedAt: new Date(Date.now() - 3600000).toISOString(),
        priority: "HIGH",
      },
      {
        id: "2",
        type: "PROCUREMENT_APPROVAL", 
        title: "Office Equipment Procurement - Phase 2",
        requestedBy: "Budi Santoso",
        requestedAt: new Date(Date.now() - 7200000).toISOString(),
        priority: "MEDIUM",
      },
      {
        id: "3",
        type: "CONTRACT_APPROVAL",
        title: "IT Services Contract Amendment",
        requestedBy: "Siti Rahayu",
        requestedAt: new Date(Date.now() - 14400000).toISOString(),
        priority: "LOW",
      },
      {
        id: "4",
        type: "BUDGET_APPROVAL",
        title: "Q4 Budget Allocation Request",
        requestedBy: "Finance Department",
        requestedAt: new Date(Date.now() - 86400000).toISOString(),
        priority: "URGENT",
      },
    ] : [];

    // Return real approvals or mock data if no real data exists
    const finalApprovals = approvals.length > 0 ? approvals : mockApprovals;

    return NextResponse.json({
      success: true,
      data: finalApprovals,
    });

  } catch (error) {
    console.error("Error fetching pending approvals:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch pending approvals",
      },
      { status: 500 }
    );
  }
}