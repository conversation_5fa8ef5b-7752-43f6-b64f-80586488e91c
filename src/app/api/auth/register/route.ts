import { NextRequest } from "next/server";

import { hashPassword } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { ConflictError, handleApiError, createSuccessResponse } from "@/lib/errors";
import { sendCombinedNotification } from "@/lib/notifications/notification-service";
import { vendorRegistrationSchema } from "@/lib/validations/auth";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = vendorRegistrationSchema.parse(body);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      throw new ConflictError("User with this email already exists");
    }

    // Check if tax ID already exists (using taxId instead of npwpNumber)
    const existingVendor = await prisma.vendor.findUnique({
      where: { taxId: validatedData.npwpNumber },
    });

    if (existingVendor) {
      throw new ConflictError("Vendor with this tax ID already exists");
    }

    // Check against sanctioned individuals
    const sanctionedIndividual = await prisma.sanctionedIndividual.findFirst({
      where: {
        OR: [
          { name: { contains: validatedData.picName, mode: "insensitive" } },
          { identityNumber: validatedData.identityNumber || "" },
        ],
      },
    });

    if (sanctionedIndividual) {
      // Create high-priority security alert
      const adminUsers = await prisma.user.findMany({
        where: {
          roles: {
            has: "ADMIN",
          },
        },
        select: {
          id: true,
        },
      });

      // Create security alert notifications for all admins
      await Promise.all(
        adminUsers.map(admin =>
          prisma.notification.create({
            data: {
              userId: admin.id,
              type: "ERROR",
              title: "SECURITY ALERT: Sanctioned Individual Registration Attempt",
              message: `Sanctioned individual "${validatedData.picName}" (ID: ${validatedData.identityNumber || "N/A"}) attempted to register. Company: ${validatedData.companyName}. Email: ${validatedData.email}`,
              metadata: {
                entityType: "SECURITY_ALERT",
                alertType: "SANCTIONED_INDIVIDUAL_REGISTRATION",
                sanctionedIndividualId: sanctionedIndividual.id,
                attemptedRegistration: {
                  picName: validatedData.picName,
                  identityNumber: validatedData.identityNumber,
                  companyName: validatedData.companyName,
                  email: validatedData.email,
                  attemptedAt: new Date().toISOString(),
                },
                severity: "CRITICAL",
              },
            },
          })
        )
      );

      throw new Error("Registration denied. Please contact administrator for assistance.");
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);

    // Create user and vendor in transaction
    const result = await prisma.$transaction(async (tx: any) => {
      // Create user
      const user = await tx.user.create({
        data: {
          email: validatedData.email,
          name: validatedData.picName,
          password: hashedPassword,
          roles: ["VENDOR"],
        },
      });

      // Create vendor
      const vendor = await tx.vendor.create({
        data: {
          userId: user.id,
          companyName: validatedData.companyName,
          companyType: validatedData.companyType || "PT",
          businessLicense: validatedData.businessLicense || validatedData.npwpNumber,
          taxId: validatedData.npwpNumber,
          address: validatedData.address,
          city: validatedData.city || "Jakarta",
          province: validatedData.province || "DKI Jakarta",
          postalCode: validatedData.postalCode || "10000",
          contactPerson: validatedData.picName,
          contactPhone: validatedData.picPhone || validatedData.phone,
          contactEmail: validatedData.picEmail,
          businessCategory: validatedData.businessCategory || "General",
          status: "PENDING_VERIFICATION",
        },
      });

      // Create documents if provided
      if (validatedData.documents && validatedData.documents.length > 0) {
        await tx.document.createMany({
          data: validatedData.documents.map((doc: any) => ({
            fileName: doc.fileName,
            fileUrl: doc.fileUrl,
            fileType: doc.fileType,
            description: doc.documentType,
            vendorId: vendor.id,
          })),
        });
      }

      return { user, vendor };
    });

    // Send verification email notification
    await sendCombinedNotification(
      {
        userId: result.user.id,
        title: "Registrasi Berhasil",
        message: "Akun Anda telah dibuat dan sedang menunggu verifikasi dari admin.",
        type: "INFO",
      },
      {
        to: result.user.email,
        subject: "Registrasi Berhasil - E-Procurement System",
        template: "account-verification",
        data: {
          userName: result.user.name,
          verificationUrl: `${process.env.NEXTAUTH_URL || "http://localhost:3000"}/login`,
        },
      }
    );

    return createSuccessResponse(
      {
        userId: result.user.id,
        vendorId: result.vendor.id,
        message: "Registration successful. Please wait for verification.",
      },
      "Registration successful",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}
