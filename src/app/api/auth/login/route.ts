import { NextRequest } from "next/server";

import { verifyPassword, generateToken, setAuth<PERSON><PERSON>ie } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { UnauthorizedError, handleApiError, createSuccessResponse } from "@/lib/errors";
import { loginSchema } from "@/lib/validations/auth";

export async function POST(request: NextRequest) {
  try {
    console.log("🔐 Login API called");
    
    const body = await request.json();
    console.log("📧 Login attempt for email:", body.email);
    
    const validatedData = loginSchema.parse(body);
    console.log("✅ Data validation passed");

    // Find user by email
    console.log("🔍 Searching for user in database...");
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      include: {
        vendor: true,
      },
    });

    console.log("👤 User found:", user ? "Yes" : "No");
    if (user) {
      console.log("🎭 User roles:", user.roles);
    }

    if (!user) {
      console.log("❌ User not found for email:", validatedData.email);
      throw new UnauthorizedError("Invalid email or password");
    }

    // Verify password
    const isPasswordValid = await verifyPassword(validatedData.password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedError("Invalid email or password");
    }

    // Check if vendor is verified (for vendor users)
    if (user.roles.includes("VENDOR") && user.vendor?.status !== "VERIFIED") {
      throw new UnauthorizedError("Your account is pending verification");
    }

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      roles: user.roles,
    });

    // Set auth cookie
    await setAuthCookie(token);

    console.log("🎉 Login successful for user:", user.email);
    console.log("🍪 Auth cookie set successfully");
    
    return createSuccessResponse(
      {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          roles: user.roles,
          vendor: user.vendor ? {
            id: user.vendor.id,
            companyName: user.vendor.companyName,
            status: user.vendor.status,
          } : null,
        },
        token,
      },
      "Login successful"
    );
  } catch (error) {
    console.error("❌ Login API Error:", error);
    console.error("❌ Error type:", error instanceof Error ? error.constructor.name : typeof error);
    console.error("❌ Error message:", error instanceof Error ? error.message : String(error));
    return handleApiError(error);
  }
}
