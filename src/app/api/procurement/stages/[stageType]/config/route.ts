import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { procurementDocumentIntegration } from '@/lib/documents/procurement-integration';

export async function GET(
  request: NextRequest,
  { params }: { params: { stageType: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get document configuration for stage
    const config = await procurementDocumentIntegration.getStageDocumentConfig(params.stageType);

    return NextResponse.json(config);
  } catch (error) {
    console.error('Error fetching stage document config:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stage document configuration' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { stageType: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const documentType = formData.get('documentType') as string;

    if (!file || !documentType) {
      return NextResponse.json(
        { error: 'Missing required fields: file, documentType' },
        { status: 400 }
      );
    }

    // Validate document against stage requirements
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    const validation = await procurementDocumentIntegration.validateStageDocument(
      params.stageType,
      documentType,
      {
        buffer: fileBuffer,
        originalname: file.name,
        mimetype: file.type,
      }
    );

    return NextResponse.json(validation);
  } catch (error) {
    console.error('Error validating stage document:', error);
    return NextResponse.json(
      { error: 'Failed to validate stage document' },
      { status: 500 }
    );
  }
}
