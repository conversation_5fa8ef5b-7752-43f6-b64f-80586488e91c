import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { procurementDocumentIntegration } from '@/lib/documents/procurement-integration';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const stageId = searchParams.get('stageId');

    if (!stageId) {
      return NextResponse.json(
        { error: 'stageId parameter is required' },
        { status: 400 }
      );
    }

    // Get documents for procurement stage
    const documents = await procurementDocumentIntegration.getStageDocuments(
      params.id,
      stageId
    );

    return NextResponse.json(documents);
  } catch (error) {
    console.error('Error fetching procurement documents:', error);
    return NextResponse.json(
      { error: 'Failed to fetch procurement documents' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const { stageId, stageType, procurementData, vendorData, additionalData } = body;

    if (!stageId || !stageType) {
      return NextResponse.json(
        { error: 'Missing required fields: stageId, stageType' },
        { status: 400 }
      );
    }

    // Generate stage documents
    const generatedDocuments = await procurementDocumentIntegration.generateStageDocuments(
      {
        procurementId: params.id,
        stageId,
        stageType,
        procurementData,
        vendorData,
        additionalData,
      },
      user.id
    );

    return NextResponse.json({
      success: true,
      documents: generatedDocuments,
    });
  } catch (error) {
    console.error('Error generating procurement documents:', error);
    return NextResponse.json(
      { error: 'Failed to generate procurement documents' },
      { status: 500 }
    );
  }
}
