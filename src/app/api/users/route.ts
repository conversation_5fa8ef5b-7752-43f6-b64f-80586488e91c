import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return handleApiError(new Error("Unauthorized"));
    }

    // Check if user has permission to view users
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER")) {
      return handleApiError(new Error("Unauthorized to view users"));
    }

    const { searchParams } = new URL(request.url);
    const rolesParam = searchParams.get("roles");
    const search = searchParams.get("search");

    // Build where clause
    const where: any = {};
    
    // Filter by roles if provided
    if (rolesParam) {
      const roles = rolesParam.split(",");
      where.roles = {
        hasSome: roles,
      };
    }
    
    // Search functionality
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ];
    }

    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        email: true,
        roles: true,
        createdAt: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return createSuccessResponse(users);
  } catch (error) {
    return handleApiError(error);
  }
}
