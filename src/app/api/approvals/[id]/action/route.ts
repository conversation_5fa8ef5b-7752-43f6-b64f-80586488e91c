import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { documentApprovalWorkflow, ApprovalAction } from '@/lib/documents/approval-workflow';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const { stepId, action, comments } = body;

    if (!stepId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: stepId, action' },
        { status: 400 }
      );
    }

    // Validate action
    const validActions = Object.values(ApprovalAction);
    if (!validActions.includes(action)) {
      return NextResponse.json(
        { error: `Invalid action. Must be one of: ${validActions.join(', ')}` },
        { status: 400 }
      );
    }

    // Process approval action
    const approvalRequest = await documentApprovalWorkflow.processApprovalAction(
      params.id,
      stepId,
      action,
      user.id,
      comments
    );

    return NextResponse.json({
      success: true,
      approvalRequest,
    });
  } catch (error) {
    console.error('Error processing approval action:', error);
    
    if (error instanceof Error && error.message === 'Approval step not found') {
      return NextResponse.json({ error: 'Approval step not found' }, { status: 404 });
    }
    
    if (error instanceof Error && error.message === 'User not authorized to approve this step') {
      return NextResponse.json({ error: 'Not authorized to approve this step' }, { status: 403 });
    }

    return NextResponse.json(
      { error: 'Failed to process approval action' },
      { status: 500 }
    );
  }
}
