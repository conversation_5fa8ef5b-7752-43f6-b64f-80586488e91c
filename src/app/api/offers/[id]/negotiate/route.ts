import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { sendCombinedNotification } from "@/lib/notifications/notification-service";
import { negotiationSchema } from "@/lib/validations/procurement";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to negotiate
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER") && !user.roles.includes("APPROVER")) {
      return handleApiError(new Error("Unauthorized to negotiate offers"));
    }

    const body = await request.json();
    const validatedData = negotiationSchema.parse(body);

    // Find the offer
    const offer = await prisma.vendorOffer.findUnique({
      where: { id: params.id },
      include: {
        procurement: {
          include: {
            committee: {
              where: { userId: user.id },
            },
          },
        },
        vendor: {
          select: {
            id: true,
            companyName: true,
            userId: true,
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!offer) {
      throw new NotFoundError("Offer not found");
    }

    // Check if user is part of the procurement committee (unless admin)
    if (!user.roles.includes("ADMIN") && offer.procurement.committee.length === 0) {
      return handleApiError(new Error("You are not authorized to negotiate this offer"));
    }

    // Check if offer is in negotiation status
    if (offer.status !== "NEGOTIATING") {
      return handleApiError(new Error("Offer is not in negotiation status"));
    }

    // Update offer with negotiated price
    const updatedOffer = await prisma.vendorOffer.update({
      where: { id: params.id },
      data: {
        negotiatedPrice: validatedData.negotiatedPrice, // Now available in schema
        negotiationComments: validatedData.comments,
      },
      include: {
        vendor: {
          select: {
            id: true,
            companyName: true,
          },
        },
        procurement: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    // Send notification to vendor about negotiation
    await sendCombinedNotification(
      {
        userId: offer.vendor.userId,
        title: "Negosiasi Harga",
        message: `Harga penawaran Anda untuk ${offer.procurement.title} telah dinegosiasi.`,
        type: "INFO",
      },
      {
        to: offer.vendor.user.email,
        subject: "Negosiasi Harga Penawaran",
        template: "offer-evaluation",
        data: {
          vendorName: offer.vendor.user.name,
          procurementTitle: offer.procurement.title,
          status: "NEGOSIASI",
          feedback: validatedData.comments,
        },
      }
    );

    return createSuccessResponse(
      updatedOffer,
      "Price negotiation updated successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
