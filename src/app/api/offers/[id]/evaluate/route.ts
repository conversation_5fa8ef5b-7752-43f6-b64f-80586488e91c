import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { sendCombinedNotification } from "@/lib/notifications/notification-service";
import { evaluationSchema } from "@/lib/validations/procurement";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to evaluate offers
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER") && !user.roles.includes("APPROVER")) {
      return handleApiError(new Error("Unauthorized to evaluate offers"));
    }

    const body = await request.json();
    const validatedData = evaluationSchema.parse(body);

    // Find the offer
    const offer = await prisma.vendorOffer.findUnique({
      where: { id: params.id },
      include: {
        procurement: {
          include: {
            committee: {
              where: { userId: user.id },
            },
          },
        },
        vendor: {
          select: {
            id: true,
            companyName: true,
            userId: true,
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!offer) {
      throw new NotFoundError("Offer not found");
    }

    // Check if user is part of the procurement committee (unless admin)
    if (!user.roles.includes("ADMIN") && offer.procurement.committee.length === 0) {
      return handleApiError(new Error("You are not authorized to evaluate this offer"));
    }

    // Check if procurement is in evaluation phase
    if (!["EVALUATION", "NEGOTIATION"].includes(offer.procurement.status)) {
      return handleApiError(new Error("Procurement is not in evaluation phase"));
    }

    // Update offer evaluation
    const updatedOffer = await prisma.vendorOffer.update({
      where: { id: params.id },
      data: {
        status: validatedData.status,
        evaluationComments: validatedData.comments,
        adminScore: validatedData.scores?.adminScore,
        techScore: validatedData.scores?.techScore,
        priceScore: validatedData.scores?.priceScore,
        evaluatedAt: new Date(),
        evaluatedBy: user.id,
      },
      include: {
        vendor: {
          select: {
            id: true,
            companyName: true,
          },
        },
        procurement: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    // Send notification to vendor about evaluation result
    if (validatedData.status.includes("FAILED")) {
      await sendCombinedNotification(
        {
          userId: offer.vendor.userId,
          title: "Evaluasi Penawaran",
          message: `Penawaran Anda untuk ${offer.procurement.title} tidak lolos evaluasi.`,
          type: "WARNING",
        },
        {
          to: offer.vendor.user.email,
          subject: "Hasil Evaluasi Penawaran",
          template: "offer-evaluation",
          data: {
            vendorName: offer.vendor.user.name,
            procurementTitle: offer.procurement.title,
            status: "TIDAK LOLOS",
            feedback: validatedData.comments,
          },
        }
      );
    } else if (validatedData.status === "WINNER") {
      await sendCombinedNotification(
        {
          userId: offer.vendor.userId,
          title: "Selamat! Anda Memenangkan Pengadaan",
          message: `Penawaran Anda untuk ${offer.procurement.title} terpilih sebagai pemenang.`,
          type: "SUCCESS",
        },
        {
          to: offer.vendor.user.email,
          subject: "Selamat! Penawaran Anda Menang",
          template: "offer-evaluation",
          data: {
            vendorName: offer.vendor.user.name,
            procurementTitle: offer.procurement.title,
            status: "PEMENANG",
            score: `${validatedData.scores?.adminScore || 0} + ${validatedData.scores?.techScore || 0} + ${validatedData.scores?.priceScore || 0}`,
          },
        }
      );
    }

    return createSuccessResponse(
      updatedOffer,
      "Offer evaluation updated successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
