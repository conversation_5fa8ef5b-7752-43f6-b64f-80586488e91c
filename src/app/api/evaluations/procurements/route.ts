import { Prisma } from "@prisma/client";
import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    // Check if user is committee member or admin
    if (!user.roles.includes("ADMIN") && !user.roles.includes("COMMITTEE")) {
      return handleApiError(new Error("Access denied. Only committee members can access evaluations."));
    }

    // Get procurements where user is committee member or user is admin
    const whereClause: Prisma.ProcurementWhereInput = {
      status: {
        in: ["EVALUATION", "NEGOTIATION", "WINNER_ANNOUNCEMENT"],
      },
    };

    // If not admin, filter by committee membership
    if (!user.roles.includes("ADMIN")) {
      whereClause.committee = {
        some: {
          userId: user.id,
        },
      };
    }

    const procurements = await prisma.procurement.findMany({
      where: whereClause,
      include: {
        _count: {
          select: {
            offers: true,
          },
        },
        committee: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return createSuccessResponse(procurements);
  } catch (error) {
    return handleApiError(error);
  }
}
