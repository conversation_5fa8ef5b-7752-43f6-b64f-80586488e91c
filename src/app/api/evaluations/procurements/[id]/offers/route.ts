import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const procurementId = params.id;

    // Check if user is committee member or admin
    if (!user.roles.includes("ADMIN") && !user.roles.includes("COMMITTEE")) {
      return handleApiError(new Error("Access denied. Only committee members can access evaluations."));
    }

    // Check if procurement exists
    const procurement = await prisma.procurement.findUnique({
      where: { id: procurementId },
      include: {
        committee: {
          where: {
            userId: user.id,
          },
        },
      },
    });

    if (!procurement) {
      throw new NotFoundError("Procurement not found");
    }

    // If not admin, check if user is committee member
    if (!user.roles.includes("ADMIN") && procurement.committee.length === 0) {
      return handleApiError(new Error("Access denied. You are not a committee member for this procurement."));
    }

    // Get all offers for this procurement
    const offers = await prisma.vendorOffer.findMany({
      where: {
        procurementId,
        status: "SUBMITTED", // Now available in extended enum
      },
      include: {
        vendor: {
          select: {
            id: true,
            companyName: true,
            picName: true, // Now available in schema
          },
        },
        items: {
          include: {
            item: {
              select: {
                id: true,
                name: true,
                quantity: true,
                unit: true,
                ownerEstimate: true, // Now available in schema
              },
            },
          },
          orderBy: {
            item: {
              name: "asc",
            },
          },
        },
        documents: {
          select: {
            id: true,
            fileName: true,
            fileUrl: true,
            documentType: true, // Now available in schema
          },
        },
        evaluations: {
          include: {
            evaluatedBy: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            evaluatedAt: "desc",
          },
        },
      },
      orderBy: {
        submittedAt: "asc", // Using correct field name
      },
    });

    return createSuccessResponse(offers);
  } catch (error) {
    return handleApiError(error);
  }
}
