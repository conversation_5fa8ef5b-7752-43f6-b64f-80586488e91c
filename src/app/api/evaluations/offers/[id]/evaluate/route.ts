import { NextRequest } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";

const evaluationSchema = z.object({
  technicalScore: z.number().min(0).max(100),
  commercialScore: z.number().min(0).max(100),
  notes: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const offerId = params.id;
    const body = await request.json();
    const validatedData = evaluationSchema.parse(body);

    // Check if user is committee member or admin
    if (!user.roles.includes("ADMIN") && !user.roles.includes("COMMITTEE")) {
      return handleApiError(new Error("Access denied. Only committee members can evaluate offers."));
    }

    // Check if offer exists and get procurement info
    const offer = await prisma.vendorOffer.findUnique({
      where: { id: offerId },
      include: {
        procurement: {
          include: {
            committee: {
              where: {
                userId: user.id,
              },
            },
          },
        },
      },
    });

    if (!offer) {
      throw new NotFoundError("Offer not found");
    }

    // If not admin, check if user is committee member
    if (!user.roles.includes("ADMIN") && offer.procurement.committee.length === 0) {
      return handleApiError(new Error("Access denied. You are not a committee member for this procurement."));
    }

    // Check if procurement is in evaluation phase
    if (!["EVALUATION", "NEGOTIATION"].includes(offer.procurement.status)) {
      return handleApiError(new Error("Procurement is not in evaluation phase"));
    }

    // Check if user has already evaluated this offer
    const existingEvaluation = await prisma.offerEvaluation.findFirst({
      where: {
        offerId,
        evaluatedById: user.id,
      },
    });

    if (existingEvaluation) {
      return handleApiError(new Error("You have already evaluated this offer"));
    }

    // Calculate total score (weighted: 70% technical, 30% commercial)
    const totalScore = (validatedData.technicalScore * 0.7) + (validatedData.commercialScore * 0.3);

    // Create evaluation
    const evaluation = await prisma.offerEvaluation.create({
      data: {
        offerId,
        evaluatedById: user.id,
        technicalScore: validatedData.technicalScore,
        commercialScore: validatedData.commercialScore,
        totalScore,
        notes: validatedData.notes,
      },
      include: {
        evaluatedBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Log the evaluation
    await prisma.auditLog.create({
      data: {
        action: "CREATE",
        resource: "offer_evaluation",
        resourceId: evaluation.id,
        userId: user.id,
        metadata: {
          offerId,
          procurementId: offer.procurementId,
          technicalScore: validatedData.technicalScore,
          commercialScore: validatedData.commercialScore,
          totalScore,
          notes: validatedData.notes,
        },
        ipAddress: request.headers.get("x-forwarded-for") || 
                   request.headers.get("x-real-ip") || 
                   "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
      },
    });

    return createSuccessResponse(
      evaluation,
      "Evaluation submitted successfully",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}
