import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { publicAssetStorage } from "@/lib/storage/public-assets-storage";

// List public assets (Admin only)
const listHandler = requireRoles(["ADMIN"])(async function GET(request: NextRequest) {
  try {
    console.log("🔍 PUBLIC-ASSETS API: Admin listing endpoint called");
    console.log("📍 URL:", request.url);
    console.log("🔑 Headers:", Object.fromEntries(request.headers.entries()));
    
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category") || undefined;
    
    console.log("📂 Category filter:", category);

    const assets = await publicAssetStorage.listPublicAssets(category);
    
    console.log("✅ Assets found:", assets.length);

    return createSuccessResponse({
      assets,
      count: assets.length,
    });
  } catch (error) {
    console.error("❌ PUBLIC-ASSETS API Error:", error);
    return handleApiError(error);
  }
});

export { listHandler as GET };
