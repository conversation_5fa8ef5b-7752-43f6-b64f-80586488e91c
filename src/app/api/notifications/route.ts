import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/db";
import {
  handleApiError,
  createSuccessResponse,
  ValidationError,
  UnauthorizedError,
} from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import {
  getUserNotifications,
  markAllNotificationsAsRead,
} from "@/lib/notifications/notification-service";

export async function GET(request: NextRequest) {
  try {
    // Temporarily disable authentication for notifications to avoid blocking dashboard
    // TODO: Fix authentication token handling for client-side requests
    let user;
    try {
      user = await getCurrentUser(request);
    } catch (authError) {
      console.log("⚠️ Notifications API: Authentication failed, returning empty notifications");
      // Return empty notifications instead of failing
      return NextResponse.json({
        success: true,
        data: {
          notifications: [],
          unreadCount: 0,
          totalCount: 0,
        },
      });
    }
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const unreadOnly = searchParams.get("unreadOnly") === "true";
    const type = searchParams.get("type");
    const category = searchParams.get("category");

    // Build where clause
    const where: any = {
      userId: user.id,
    };

    if (unreadOnly) {
      where.read = false;
    }

    if (type) {
      where.type = type;
    }

    if (category) {
      where.notificationType = {
        contains: category.toUpperCase(),
      };
    }

    const [notifications, total, unreadCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.notification.count({ where }),
      prisma.notification.count({
        where: {
          userId: user.id,
          read: false,
        },
      }),
    ]);

    // Group notifications by date
    const groupedNotifications = notifications.reduce(
      (groups: any, notification: any) => {
        const date = notification.createdAt.toDateString();
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(notification);
        return groups;
      },
      {}
    );

    return createSuccessResponse({
      notifications,
      groupedNotifications,
      unreadCount,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    // Check if user has permission to send notifications
    if (!user.roles.includes("ADMIN")) {
      return handleApiError(
        new UnauthorizedError("Unauthorized to send notifications")
      );
    }

    const body = await request.json();
    const {
      recipients,
      title,
      message,
      type = "INFO",
      notificationType = "MANUAL",
      metadata = {},
      channels = ["IN_APP"],
    } = body;

    if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
      return handleApiError(new ValidationError("Recipients are required"));
    }

    if (!title || !message) {
      return handleApiError(
        new ValidationError("Title and message are required")
      );
    }

    // Resolve recipients
    const resolvedRecipients = [];

    for (const recipient of recipients) {
      if (recipient.type === "USER") {
        const targetUser = await prisma.user.findUnique({
          where: { id: recipient.value },
          select: { id: true, email: true, name: true },
        });
        if (targetUser) resolvedRecipients.push(targetUser);
      } else if (recipient.type === "ROLE") {
        const roleUsers = await prisma.user.findMany({
          where: {
            roles: { has: recipient.value },
          },
          select: { id: true, email: true, name: true },
        });
        resolvedRecipients.push(...roleUsers);
      } else if (recipient.type === "DEPARTMENT") {
        // Since department relation doesn't exist, skip this for now
        // In a real implementation, you would add department support
        console.warn(
          `Department-based notifications not supported: ${recipient.value}`
        );
      }
    }

    // Remove duplicates
    const uniqueRecipients = resolvedRecipients.filter(
      (recipient: any, index: any, self: any) =>
        index === self.findIndex((r: any) => r.id === recipient.id)
    );

    // Create notifications
    const notifications = [];

    for (const recipient of uniqueRecipients) {
      // Create in-app notification
      if (channels.includes("IN_APP")) {
        const notification = await prisma.notification.create({
          data: {
            userId: recipient.id,
            title,
            message,
            type,
            notificationType,
            metadata: {
              ...metadata,
              sentBy: user.id,
              sentAt: new Date().toISOString(),
            },
          },
        });
        notifications.push(notification);
      }

      // Queue email notification using Redis
      if (channels.includes("EMAIL")) {
        const { emailQueue } = await import("../../../lib/queue/email-queue");
        await emailQueue.addEmail(
          {
            to: recipient.email,
            subject: title,
            body: message,
          },
          {
            priority: "MEDIUM",
          }
        );
      }

      // Queue SMS notification using Redis
      if (channels.includes("SMS")) {
        const { smsQueue } = await import("../../../lib/queue/sms-queue");
        // Get user's phone number
        const userWithPhone = await prisma.user.findUnique({
          where: { id: recipient.id },
          select: { phone: true },
        });

        if (userWithPhone?.phone) {
          await smsQueue.addSms(
            {
              to: userWithPhone.phone,
              message: `${title}\n\n${message}`,
            },
            {
              priority: "MEDIUM",
            }
          );
        }
      }

      // Queue push notification using Redis
      if (channels.includes("PUSH")) {
        const { pushQueue } = await import("../../../lib/queue/push-queue");
        await pushQueue.addPushNotification(
          {
            userId: recipient.id,
            title,
            body: message,
          },
          {
            priority: "MEDIUM",
          }
        );
      }
    }

    return createSuccessResponse(
      {
        notifications,
        recipientCount: uniqueRecipients.length,
        channels,
      },
      "Notifications sent successfully",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();

    if (body.action === "markAllAsRead") {
      await markAllNotificationsAsRead(user.id);
      return createSuccessResponse(
        { message: "All notifications marked as read" },
        "All notifications marked as read"
      );
    }

    return handleApiError(new ValidationError("Invalid action"));
  } catch (error) {
    return handleApiError(error);
  }
}
