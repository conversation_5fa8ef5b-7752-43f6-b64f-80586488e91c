import { NextRequest } from "next/server";

import { auditRead, auditUpdate } from "@/lib/audit/audit-middleware";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { purchaseRequisitionSchema } from "@/lib/validations/purchase-requisition";

// Get purchase requisition by ID
const getHandler = requireRoles(["ADMIN", "PROCUREMENT_USER", "APPROVER"])(
  auditRead("purchase_requisitions")(async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const prId = params.id;

      const purchaseRequisition = await prisma.purchaseRequisition.findUnique({
        where: { id: prId },
        include: {
          requester: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: {
            include: {
              itemMaster: {
                select: {
                  id: true,
                  name: true,
                  category: true,
                  unit: true,
                },
              },
            },
          },
          sourceContract: {
            select: {
              id: true,
              contractNumber: true,
              title: true,
              vendor: {
                select: {
                  id: true,
                  companyName: true,
                },
              },
            },
          },
          documents: {
            select: {
              id: true,
              fileName: true,
              fileUrl: true,
              fileType: true,
              uploadedAt: true,
            },
          },
          approvalInstance: {
            include: {
              stepInstances: {
                include: {
                  step: {
                    select: {
                      name: true,
                      sequence: true,
                    },
                  },
                  approvals: {
                    include: {
                      performedBy: {
                        select: {
                          id: true,
                          name: true,
                          email: true,
                        },
                      },
                    },
                  },
                },
                orderBy: { sequence: "asc" },
              },
            },
          },
        },
      });

      if (!purchaseRequisition) {
        throw new NotFoundError("Purchase requisition not found");
      }

      // Check access permissions
      if (!user.roles.includes("ADMIN") && 
          !user.roles.includes("APPROVER") && 
          purchaseRequisition.requesterId !== user.id) {
        throw new Error("Access denied. You can only view your own purchase requisitions.");
      }

      // Calculate total amount
      const totalAmount = purchaseRequisition.items.reduce(
        (sum: any, item: any) => sum + (item.quantity * item.estimatedPrice),
        0
      );

      // Format approval status
      let approvalStatus = null;
      if (purchaseRequisition.approvalInstance) {
        const allSteps = purchaseRequisition.approvalInstance.stepInstances;
        const currentStep = allSteps.find((step: any) => step.status === "IN_PROGRESS");
        
        approvalStatus = {
          workflowName: "Purchase Requisition Approval",
          status: purchaseRequisition.approvalInstance.status,
          currentStep: currentStep?.step.name,
          allSteps: allSteps.map((step: any) => ({
            id: step.id,
            name: step.step.name,
            sequence: step.sequence,
            status: step.status,
            approvals: step.approvals,
          })),
        };
      }

      return createSuccessResponse({
        ...purchaseRequisition,
        totalAmount,
        approvalStatus,
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Update purchase requisition
const updateHandler = requireRoles(["PROCUREMENT_USER"])(
  auditUpdate("purchase_requisitions", {
    description: "Update purchase requisition",
    severity: "MEDIUM",
    category: "PROCUREMENT",
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const prId = params.id;
      const body = await request.json();
      
      // Validate request data
      const validatedData = purchaseRequisitionSchema.parse(body);

      // Check if PR exists and user has permission
      const existingPR = await prisma.purchaseRequisition.findUnique({
        where: { id: prId },
        include: { items: true },
      });

      if (!existingPR) {
        throw new NotFoundError("Purchase requisition not found");
      }

      if (!user.roles.includes("ADMIN") && existingPR.requesterId !== user.id) {
        throw new Error("Access denied. You can only update your own purchase requisitions.");
      }

      // Check if PR can be updated (only DRAFT and REJECTED can be updated)
      if (!["DRAFT", "REJECTED"].includes(existingPR.status)) {
        throw new Error("Purchase requisition cannot be updated in current status");
      }

      // Update PR and items in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Update the purchase requisition
        const updatedPR = await tx.purchaseRequisition.update({
          where: { id: prId },
          data: {
            title: validatedData.title,
            type: validatedData.type,
            sourceContractId: validatedData.sourceContractId,
            status: "DRAFT", // Reset to draft when updated
          },
        });

        // Delete existing items
        await tx.purchaseRequisitionItem.deleteMany({
          where: { prId },
        });

        // Create new items
        const items = await Promise.all(
          validatedData.items.map((item: any) =>
            tx.purchaseRequisitionItem.create({
              data: {
                prId,
                itemMasterId: item.itemMasterId,
                name: item.name,
                description: item.description,
                quantity: item.quantity,
                unit: item.unit,
                estimatedPrice: item.estimatedPrice,
              },
            })
          )
        );

        return {
          ...updatedPR,
          items,
        };
      });

      return createSuccessResponse(result, "Purchase requisition updated successfully");
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { getHandler as GET, updateHandler as PUT };
