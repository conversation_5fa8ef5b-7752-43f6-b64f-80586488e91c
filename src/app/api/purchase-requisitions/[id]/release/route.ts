import { NextRequest } from "next/server";

import { enhancedWorkflowEngine } from "@/lib/approval/enhanced-workflow-engine";
import { auditCreate } from "@/lib/audit/audit-middleware";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Release purchase requisition for approval
const releaseHandler = requireRoles(["PROCUREMENT_USER"])(
  auditCreate("purchase_requisition_release", {
    description: "Release purchase requisition for approval",
    severity: "HIGH",
    category: "PROCUREMENT",
  })(async function POST(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const prId = params.id;

      // Get PR with items
      const purchaseRequisition = await prisma.purchaseRequisition.findUnique({
        where: { id: prId },
        include: {
          items: true,
          requester: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!purchaseRequisition) {
        throw new NotFoundError("Purchase requisition not found");
      }

      // Check ownership
      if (!user.roles.includes("ADMIN") && purchaseRequisition.requesterId !== user.id) {
        throw new Error("Access denied. You can only release your own purchase requisitions.");
      }

      // Validate PR status
      if (!["DRAFT", "REJECTED"].includes(purchaseRequisition.status)) {
        throw new Error("Purchase requisition cannot be released. Only draft or rejected PRs can be released for approval.");
      }

      // Validate PR has items
      if (purchaseRequisition.items.length === 0) {
        throw new Error("Purchase requisition must have at least one item before release");
      }

      // Calculate total amount for workflow conditions
      const totalAmount = purchaseRequisition.items.reduce(
        (sum: any, item: any) => sum + (item.quantity * item.estimatedPrice),
        0
      );

      // Prepare entity data for workflow engine
      const entityData = {
        id: purchaseRequisition.id,
        prNumber: purchaseRequisition.prNumber,
        title: purchaseRequisition.title,
        type: purchaseRequisition.type,
        totalAmount,
        requesterId: purchaseRequisition.requesterId,
        requesterName: purchaseRequisition.requester.name,
        itemCount: purchaseRequisition.items.length,
        createdAt: purchaseRequisition.createdAt,
      };

      // Start approval workflow and update PR status in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Update PR status to pending approval
        const updatedPR = await tx.purchaseRequisition.update({
          where: { id: prId },
          data: {
            status: "PENDING_APPROVAL",
            submittedAt: new Date(),
          },
        });

        // Start approval workflow using enhanced workflow engine
        const approvalInstance = await enhancedWorkflowEngine.startStageApproval({
          entityType: "PURCHASE_REQUISITION",
          entityId: prId,
          stage: "rfq_creation", // PR approval stage
          entityData,
          initiatedById: user.id,
          title: `PR Approval: ${purchaseRequisition.prNumber}`,
          description: `Approval request for purchase requisition: ${purchaseRequisition.title}`,
          priority: totalAmount > 100000000 ? "HIGH" : totalAmount > 50000000 ? "NORMAL" : "LOW", // 100M and 50M IDR thresholds
        });

        // Get the first step instance for notifications
        const stepInstances = await tx.approvalStepInstance.findMany({
          where: { instanceId: approvalInstance.id },
          orderBy: { sequence: 'asc' },
          take: 1,
        });

        const firstStep = stepInstances.length > 0 ? stepInstances[0] : null;
        if (firstStep) {
          // Get approver user IDs from the first step
          const approverIds = await tx.approvalStepInstance.findUnique({
            where: { id: firstStep.id },
            select: { assignedTo: true },
          });

          if (approverIds?.assignedTo) {
            // Create notifications for each approver
            await Promise.all(
              (approverIds.assignedTo as string[]).map(approverId =>
                tx.notification.create({
                  data: {
                    userId: approverId,
                    type: "APPROVAL_REQUEST",
                    title: "Purchase Requisition Approval Required",
                    message: `Purchase requisition ${purchaseRequisition.prNumber} requires your approval. Total amount: ${totalAmount.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}`,
                    metadata: {
                      entityType: "PURCHASE_REQUISITION",
                      entityId: prId,
                      approvalInstanceId: approvalInstance.id,
                      stepInstanceId: firstStep.id,
                      prNumber: purchaseRequisition.prNumber,
                      totalAmount,
                      requesterName: purchaseRequisition.requester.name,
                    },
                  },
                })
              )
            );
          }
        }

        return {
          purchaseRequisition: updatedPR,
          approvalInstance,
        };
      });

      return createSuccessResponse(
        {
          id: result.purchaseRequisition.id,
          prNumber: result.purchaseRequisition.prNumber,
          status: result.purchaseRequisition.status,
          submittedAt: result.purchaseRequisition.submittedAt,
          approvalInstanceId: result.approvalInstance.id,
          totalAmount,
        },
        "Purchase requisition released for approval successfully"
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { releaseHandler as POST };
