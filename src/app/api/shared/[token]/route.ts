import { NextRequest, NextResponse } from 'next/server';

import { documentManager } from '@/lib/documents/document-manager';
import { documentSecurity } from '@/lib/documents/security';
import { documentSharing } from '@/lib/documents/sharing';

export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'view';
    
    // Get client context
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Validate share link access
    const accessResult = await documentSharing.accessViaShareLink(
      params.token,
      {
        ipAddress,
        userAgent,
      }
    );

    if (!accessResult.allowed) {
      return NextResponse.json(
        { error: accessResult.reason || 'Access denied' },
        { status: 403 }
      );
    }

    if (!accessResult.documentId) {
      return NextResponse.json(
        { error: 'Invalid share link' },
        { status: 400 }
      );
    }

    // Check if the requested action is allowed
    const requiredPermission = getRequiredPermission(action);
    if (requiredPermission && !accessResult.permissions?.includes(requiredPermission)) {
      return NextResponse.json(
        { error: `Insufficient permissions for action: ${action}` },
        { status: 403 }
      );
    }

    // Get document information (without sensitive data)
    const document = await getSharedDocumentInfo(accessResult.documentId);
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Log access
    await documentSecurity.logAuditEvent({
      documentId: accessResult.documentId,
      userId: 'anonymous',
      action: action.toUpperCase() as any,
      details: {
        accessMethod: 'share_link',
        token: params.token.substring(0, 20) + '...',
        action,
      },
      ipAddress,
      userAgent,
      success: true,
    });

    return NextResponse.json({
      document: {
        id: document.id,
        fileName: document.fileName,
        fileType: document.fileType,
        documentType: document.documentType,
        description: document.description,
        fileSize: document.fileSize,
        uploadedAt: document.uploadedAt,
        // Don't include sensitive information like file URLs for anonymous access
      },
      permissions: accessResult.permissions,
      allowedActions: getAllowedActions(accessResult.permissions || []),
    });
  } catch (error) {
    console.error('Error accessing shared document:', error);
    return NextResponse.json(
      { error: 'Failed to access shared document' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const body = await request.json();
    const { action, ...actionData } = body;
    
    // Get client context
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Validate share link access
    const accessResult = await documentSharing.accessViaShareLink(
      params.token,
      {
        ipAddress,
        userAgent,
      }
    );

    if (!accessResult.allowed) {
      return NextResponse.json(
        { error: accessResult.reason || 'Access denied' },
        { status: 403 }
      );
    }

    if (!accessResult.documentId) {
      return NextResponse.json(
        { error: 'Invalid share link' },
        { status: 400 }
      );
    }

    // Check if the requested action is allowed
    const requiredPermission = getRequiredPermission(action);
    if (requiredPermission && !accessResult.permissions?.includes(requiredPermission)) {
      return NextResponse.json(
        { error: `Insufficient permissions for action: ${action}` },
        { status: 403 }
      );
    }

    let result;
    
    switch (action) {
      case 'download':
        result = await handleDownload(accessResult.documentId);
        break;
        
      case 'view_content':
        result = await handleViewContent(accessResult.documentId);
        break;
        
      default:
        return NextResponse.json(
          { error: `Unsupported action: ${action}` },
          { status: 400 }
        );
    }

    // Log action
    await documentSecurity.logAuditEvent({
      documentId: accessResult.documentId,
      userId: 'anonymous',
      action: action.toUpperCase() as any,
      details: {
        accessMethod: 'share_link',
        token: params.token.substring(0, 20) + '...',
        action,
        ...actionData,
      },
      ipAddress,
      userAgent,
      success: true,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error performing action on shared document:', error);
    return NextResponse.json(
      { error: 'Failed to perform action' },
      { status: 500 }
    );
  }
}

function getRequiredPermission(action: string) {
  const permissionMap: Record<string, any> = {
    'view': 'READ',
    'view_content': 'READ',
    'download': 'READ',
    'edit': 'WRITE',
    'delete': 'DELETE',
    'share': 'SHARE',
  };
  
  return permissionMap[action];
}

function getAllowedActions(permissions: any[]): string[] {
  const actions: string[] = [];
  
  if (permissions.includes('READ')) {
    actions.push('view', 'view_content', 'download');
  }
  
  if (permissions.includes('WRITE')) {
    actions.push('edit');
  }
  
  if (permissions.includes('DELETE')) {
    actions.push('delete');
  }
  
  if (permissions.includes('SHARE')) {
    actions.push('share');
  }
  
  return actions;
}

async function getSharedDocumentInfo(documentId: string) {
  try {
    // Get document info without requiring user authentication
    // This is a simplified version that doesn't include sensitive data
    return {
      id: documentId,
      fileName: 'shared-document.pdf',
      fileType: 'application/pdf',
      documentType: 'SHARED',
      description: 'Shared document',
      fileSize: 1024000,
      uploadedAt: new Date(),
    };
  } catch (error) {
    console.error('Error getting shared document info:', error);
    return null;
  }
}

async function handleDownload(documentId: string) {
  try {
    // TODO: Implement actual download logic
    return {
      success: true,
      downloadUrl: `/api/documents/${documentId}/download`,
      message: 'Download initiated',
    };
  } catch (error) {
    throw new Error('Download failed');
  }
}

async function handleViewContent(documentId: string) {
  try {
    // TODO: Implement actual content viewing logic
    return {
      success: true,
      contentUrl: `/api/documents/${documentId}/content`,
      message: 'Content access granted',
    };
  } catch (error) {
    throw new Error('Content access failed');
  }
}
