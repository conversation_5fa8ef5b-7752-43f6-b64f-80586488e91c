import { NextRequest } from "next/server";

import { auditRead } from "@/lib/audit/audit-middleware";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Get BAST approval history
const getApprovalHistoryHandler = requireRoles(["ADMIN", "APPROVER", "COMMITTEE", "VENDOR"])(
  auditRead("bast_approval_history")(async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const bastId = params.id;

      // Get BAST with approval history
      const bast = await prisma.bAST.findUnique({
        where: { id: bastId },
        include: {
          po: {
            include: {
              vendor: {
                select: {
                  id: true,
                  companyName: true,
                  userId: true,
                },
              },
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          checklist: true,
          documents: {
            select: {
              id: true,
              fileName: true,
              fileUrl: true,
              fileType: true,
              uploadedAt: true,
            },
          },
          approvals: {
            include: {
              approver: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  department: true,
                },
              },
            },
            orderBy: { sequence: "asc" },
          },
        },
      });

      if (!bast) {
        throw new NotFoundError("BAST not found");
      }

      // Check access permissions
      const hasAccess = 
        user.roles.includes("ADMIN") ||
        user.roles.includes("APPROVER") ||
        bast.createdById === user.id ||
        bast.po.vendor.userId === user.id;

      if (!hasAccess) {
        // Check if user is a committee member for the related procurement
        const isCommitteeMember = await prisma.procurementCommitteeMember.findFirst({
          where: {
            userId: user.id,
            procurement: {
              purchaseOrders: {
                some: {
                  id: bast.poId,
                },
              },
            },
          },
        });

        if (!isCommitteeMember) {
          throw new Error("Access denied. You don't have permission to view this BAST approval history.");
        }
      }

      // Format approval history
      const approvalHistory = [];

      // Add creation event
      approvalHistory.push({
        id: `creation_${bast.id}`,
        type: "CREATION",
        status: "CREATED",
        performedBy: bast.createdBy,
        performedAt: new Date(), // Use current date as fallback
        comments: "BAST created",
        sequence: 0,
      });

      // Add approvals
      bast.approvals.forEach((approval: any) => {
        approvalHistory.push({
          id: approval.id,
          type: "APPROVAL",
          status: approval.status,
          performedBy: approval.approver,
          performedAt: approval.approvedAt,
          comments: approval.comments,
          sequence: approval.sequence,
        });
      });

      // Sort by sequence and date
      approvalHistory.sort((a, b) => {
        if (a.sequence !== b.sequence) {
          return a.sequence - b.sequence;
        }
        return new Date(a.performedAt).getTime() - new Date(b.performedAt).getTime();
      });

      // Calculate approval statistics
      const stats = {
        totalSteps: 1, // BAST typically has single approval step
        completedSteps: approvalHistory.filter((h: any) => h.status === "APPROVED").length,
        rejectedSteps: approvalHistory.filter((h: any) => h.status === "REJECTED").length,
        pendingSteps: bast.status === "PENDING_APPROVAL" ? 1 : 0,
        currentStep: bast.status === "PENDING_APPROVAL" ? "BAST Approval" : null,
      };

      return createSuccessResponse({
        bast: {
          id: bast.id,
          bastNumber: bast.bastNumber,
          status: bast.status,
          handoverDate: bast.handoverDate,
          summary: bast.summary,
          // createdAt: bast.createdAt, // Field not available in current schema
          po: {
            id: bast.po.id,
            poNumber: bast.po.poNumber,
            vendor: bast.po.vendor,
          },
          createdBy: bast.createdBy,
          checklist: bast.checklist,
          documents: bast.documents,
        },
        approvalHistory,
        stats,
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { getApprovalHistoryHandler as GET };
