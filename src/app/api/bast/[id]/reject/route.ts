import { NextRequest } from "next/server";

import { enhancedWorkflowEngine } from "@/lib/approval/enhanced-workflow-engine";
import { auditCreate } from "@/lib/audit/audit-middleware";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError, ValidationError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Reject BAST
const rejectHandler = requireRoles(["ADMIN", "APPROVER"])(
  auditCreate("bast_reject", {
    description: "Reject BAST",
    severity: "HIGH",
    category: "PROCUREMENT",
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const bastId = params.id;
      const body = await request.json();
      const { comments } = body;

      if (!comments || comments.trim().length === 0) {
        throw new ValidationError("Rejection reason is required");
      }

      // Get BAST with related data
      const bast = await prisma.bAST.findUnique({
        where: { id: bastId },
        include: {
          po: {
            include: {
              vendor: {
                include: {
                  user: true,
                },
              },
            },
          },
        },
      });

      if (!bast) {
        throw new NotFoundError("BAST not found");
      }

      if (bast.status !== "PENDING_APPROVAL") {
        throw new ValidationError("BAST is not pending approval");
      }

      // Get approval instance for this BAST
      const approvalInstance = await prisma.approvalInstance.findFirst({
        where: {
          entityType: "BAST",
          entityId: bastId,
        },
        include: {
          stepInstances: {
            include: {
              step: true,
              approvals: true,
            },
            orderBy: { sequence: "asc" },
          },
        },
      });

      if (!approvalInstance) {
        throw new ValidationError("No approval workflow found for this BAST");
      }

      // Find current pending step that user can approve
      const currentStep = approvalInstance.stepInstances.find(
        (step: any) => step.status === "IN_PROGRESS" || step.status === "PENDING"
      );

      if (!currentStep) {
        throw new ValidationError("No pending approval step found");
      }

      // Check if user is assigned to approve this step
      const assignedUsers = Array.isArray(currentStep.assignedTo) 
        ? currentStep.assignedTo 
        : currentStep.assignedTo ? [currentStep.assignedTo] : [];
      const userCanApprove = assignedUsers.includes(user.id);

      if (!userCanApprove) {
        throw new ValidationError("You are not authorized to reject this step");
      }

      // Process rejection action through workflow engine
      const workflowResult = await enhancedWorkflowEngine.processApprovalAction({
        instanceId: approvalInstance.id,
        stepInstanceId: currentStep.id,
        userId: user.id,
        action: "REJECT",
        comments,
        metadata: {
          bastId: bast.id,
          bastNumber: bast.bastNumber,
          poNumber: bast.po.poNumber,
        },
      });

      // Update BAST status and create notifications in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Update BAST status to rejected
        const updatedBast = await tx.bAST.update({
          where: { id: bastId },
          data: {
            status: "REJECTED",
            rejectedAt: new Date(),
          },
        });

        // Create notification for vendor about rejection
        await tx.notification.create({
          data: {
            userId: bast.po.vendor.user.id,
            type: "ERROR",
            title: "BAST Rejected",
            message: `Your BAST ${bast.bastNumber} for PO ${bast.po.poNumber} has been rejected. Reason: ${comments}`,
            metadata: {
              entityType: "BAST",
              entityId: bast.id,
              bastNumber: bast.bastNumber,
              poNumber: bast.po.poNumber,
              status: "REJECTED",
              rejectionReason: comments,
            },
          },
        });

        // Create notification for procurement team about rejection
        const procurementTeamUsers = await tx.user.findMany({
          where: {
            roles: {
              hasSome: ["PROCUREMENT_USER", "ADMIN"],
            },
          },
        });

        await Promise.all(
          procurementTeamUsers.map((user: any) =>
            tx.notification.create({
              data: {
                userId: user.id,
                type: "WARNING",
                title: "BAST Rejected",
                message: `BAST ${bast.bastNumber} for PO ${bast.po.poNumber} has been rejected and requires attention.`,
                metadata: {
                  entityType: "BAST",
                  entityId: bast.id,
                  bastNumber: bast.bastNumber,
                  poNumber: bast.po.poNumber,
                  status: "REJECTED",
                  rejectedBy: user.name,
                  rejectionReason: comments,
                },
              },
            })
          )
        );

        return updatedBast;
      });

      return createSuccessResponse(
        {
          id: result.id,
          bastNumber: result.bastNumber,
          status: result.status,
          rejectedAt: result.rejectedAt,
          rejectionReason: comments,
        },
        "BAST rejected successfully"
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { rejectHandler as PUT };
