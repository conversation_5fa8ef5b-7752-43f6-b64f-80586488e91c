import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export default function TendersPage() {
  // Mock data for tenders (to be replaced with actual data fetching)
  const tenders = [
    { id: 1, title: "Office Supplies Procurement", status: "Open", dueDate: "2025-06-20", bids: 5 },
    { id: 2, title: "IT Equipment Tender", status: "Closed", dueDate: "2025-05-15", bids: 8 },
    { id: 3, title: "Construction Services", status: "Open", dueDate: "2025-07-01", bids: 3 },
  ];

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Tender Listings</h1>
      <div className="mb-6">
        <Link href="/tenders/create">
          <Button>Create New Tender</Button>
        </Link>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Active Tenders</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Bids Received</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tenders.map((tender) => (
                <TableRow key={tender.id}>
                  <TableCell>{tender.title}</TableCell>
                  <TableCell>{tender.status}</TableCell>
                  <TableCell>{tender.dueDate}</TableCell>
                  <TableCell>{tender.bids}</TableCell>
                  <TableCell>
                    <Link href={`/tenders/${tender.id}`}>
                      <Button variant="outline" size="sm">View Details</Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
