"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

// Define the Zod schema for tender creation
const tenderSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().min(1, { message: "Description is required" }),
  dueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, { message: "Invalid date format (YYYY-MM-DD)" }),
  budget: z.string().regex(/^\d+(\.\d{1,2})?$/, { message: "Invalid budget format" }),
});

type TenderFormData = z.infer<typeof tenderSchema>;

// Mock API call for creating a tender (to be replaced with actual API endpoint)
const createTender = async (data: TenderFormData): Promise<{ message: string }> => {
  console.log("Creating tender with data:", data);
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ message: "Tender created successfully." });
    }, 1000);
  });
};

export default function CreateTenderPage() {
  const router = useRouter();
  const form = useForm<TenderFormData>({
    resolver: zodResolver(tenderSchema),
    defaultValues: {
      title: "",
      description: "",
      dueDate: "",
      budget: "",
    },
  });

  const mutation = useMutation<{ message: string }, Error, TenderFormData>({
    mutationFn: createTender,
    onSuccess: (data) => {
      toast.success("Success", {
        description: data.message,
      });
      router.push("/tenders");
    },
    onError: (error) => {
      toast.error("Error", {
        description: error.message || "Failed to create tender. Please try again.",
      });
    },
  });

  const onSubmit = (data: TenderFormData) => {
    mutation.mutate(data);
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Create New Tender</h1>
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle>Tender Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                {...form.register("title")}
                placeholder="Enter tender title"
              />
              {form.formState.errors.title && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.title.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register("description")}
                placeholder="Enter detailed description of the tender"
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.description.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                type="date"
                {...form.register("dueDate")}
              />
              {form.formState.errors.dueDate && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.dueDate.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="budget">Budget (IDR)</Label>
              <Input
                id="budget"
                {...form.register("budget")}
                placeholder="Enter budget amount"
              />
              {form.formState.errors.budget && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.budget.message}
                </p>
              )}
            </div>
            <div className="flex justify-end space-x-2">
              <Link href="/tenders">
                <Button variant="outline">Cancel</Button>
              </Link>
              <Button
                type="submit"
                disabled={!form.formState.isValid || mutation.isPending}
              >
                {mutation.isPending ? "Creating..." : "Create Tender"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
