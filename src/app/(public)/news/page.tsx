"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Search, Filter, Calendar, User, ArrowRight } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface NewsArticle {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage?: string;
  status: string;
  publishedAt: string;
  author: {
    name: string;
  };
  category: {
    name: string;
    slug: string;
  };
}

interface NewsFilters {
  search: string;
  category: string;
  sortBy: string;
}

interface NewsCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
}

async function fetchNewsCategories(): Promise<NewsCategory[]> {
  const response = await fetch("/api/public/news-categories");
  if (!response.ok) {
    console.error("❌ [DEBUG] Failed to fetch categories:", response.status, response.statusText);
    return [];
  }

  const result = await response.json();
  return result.data?.categories || [];
}

async function fetchNews(
  filters: NewsFilters,
  page: number = 1,
  limit: number = 12
): Promise<{
  articles: NewsArticle[];
  totalCount: number;
  totalPages: number;
}> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    status: "published",
    ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value)),
  });

  console.log("🔍 [DEBUG] Fetching news with params:", params.toString());

  const response = await fetch(`/api/public/news?${params}`);
  if (!response.ok) {
    console.error("❌ [DEBUG] API response not ok:", response.status, response.statusText);
    throw new Error("Failed to fetch news");
  }

  const result = await response.json();
  console.log("📦 [DEBUG] Raw API response:", result);
  console.log("🎯 [DEBUG] Result.data:", result.data);
  console.log("📰 [DEBUG] Result.data.data:", result.data?.data);
  console.log("📰 [DEBUG] Articles:", result.data?.data?.articles);

  // Fix: The API returns { success: true, data: { data: { articles, pagination } } }
  // But we need { articles, totalCount, totalPages }
  const apiData = result.data?.data;
  if (!apiData) {
    console.error("❌ [DEBUG] No data.data found in API response");
    return { articles: [], totalCount: 0, totalPages: 0 };
  }

  return {
    articles: apiData.articles || [],
    totalCount: apiData.pagination?.totalCount || 0,
    totalPages: apiData.pagination?.totalPages || 0,
  };
}

export default function NewsPage() {
  const [filters, setFilters] = useState<NewsFilters>({
    search: "",
    category: "all",
    sortBy: "newest",
  });
  const [currentPage, setCurrentPage] = useState(1);

  const { data, isLoading, error } = useQuery({
    queryKey: ["news", filters, currentPage],
    queryFn: () => fetchNews(filters, currentPage),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: categories = [] } = useQuery({
    queryKey: ["news-categories"],
    queryFn: fetchNewsCategories,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const handleFilterChange = (key: keyof NewsFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Berita & Pengumuman
          </h1>
          <p className="text-gray-600">
            Informasi terbaru dari PT Bank BPD Sulteng
          </p>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter & Pencarian
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Input
                  placeholder="Cari berita..."
                  value={filters.search}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleFilterChange("search", e.target.value)}
                  className="w-full"
                />
              </div>

              <Select value={filters.category} onValueChange={(value: string) => handleFilterChange("category", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kategori</SelectItem>
                  {categories.filter(cat => cat.isActive).map((category) => (
                    <SelectItem key={category.id} value={category.slug}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filters.sortBy} onValueChange={(value: string) => handleFilterChange("sortBy", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Urutkan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Terbaru</SelectItem>
                  <SelectItem value="oldest">Terlama</SelectItem>
                  <SelectItem value="title">Judul A-Z</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-32 bg-gray-200 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-16 bg-gray-200 rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-red-500 mb-2">Error loading news</div>
              <p className="text-gray-600">Please try again later</p>
            </CardContent>
          </Card>
        ) : data?.articles.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Tidak ada berita ditemukan
              </h3>
              <p className="text-gray-600">
                Coba ubah filter pencarian atau periksa kembali nanti
              </p>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Results Count */}
            <div className="mb-6">
              <p className="text-gray-600">
                Menampilkan {data?.articles.length} dari {data?.totalCount} berita
              </p>
            </div>

            {/* News Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {data?.articles.map((article) => (
                <Card key={article.id} className="hover:shadow-lg transition-shadow overflow-hidden">
                  {article.featuredImage && (
                    <div className="aspect-video overflow-hidden">
                      <img
                        src={article.featuredImage}
                        alt={article.title}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  )}
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">{article.category.name}</Badge>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {format(new Date(article.publishedAt), "dd MMM yyyy", { locale: id })}
                        </span>
                      </div>
                    </div>
                    <CardTitle className="text-lg line-clamp-2">
                      {article.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <p className="text-sm text-gray-600 line-clamp-3">
                        {article.excerpt}
                      </p>

                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <User className="h-3 w-3" />
                        <span>{article.author.name}</span>
                      </div>

                      <Link href={`/news/${article.slug}`}>
                        <Button variant="outline" className="w-full group">
                          Baca Selengkapnya
                          <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {data && data.totalPages > 1 && (
              <div className="flex justify-center gap-2">
                <Button
                  variant="outline"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => prev - 1)}
                >
                  Sebelumnya
                </Button>

                {[...Array(Math.min(5, data.totalPages))].map((_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  );
                })}

                <Button
                  variant="outline"
                  disabled={currentPage === data.totalPages}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                >
                  Selanjutnya
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}