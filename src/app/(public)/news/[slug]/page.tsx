"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { ArrowLeft, Calendar, User, Tag } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface NewsArticle {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage?: string;
  status: string;
  publishedAt: string;
  author: {
    id: string;
    name: string;
  };
  category: {
    id: string;
    name: string;
    slug: string;
  };
}

async function fetchArticle(slug: string): Promise<NewsArticle> {
  const response = await fetch(`/api/public/news/${slug}`);
  
  if (!response.ok) {
    if (response.status === 404) {
      throw new Error("Article not found");
    }
    throw new Error("Failed to fetch article");
  }
  
  const result = await response.json();
  
  // Handle both response formats for backward compatibility
  if (result.success && result.data?.article) {
    return result.data.article;
  } else if (result.data?.article) {
    return result.data.article;
  } else if (result.article) {
    return result.article;
  } else {
    console.error("Unexpected API response format:", result);
    throw new Error("Article data not found in response");
  }
}

export default function NewsArticlePage() {
  const params = useParams();
  const slug = params.slug as string;

  const { data: article, isLoading, error } = useQuery({
    queryKey: ["news-article", slug],
    queryFn: () => fetchArticle(slug),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded mb-6"></div>
            <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-red-500 mb-4">
                {error.message === "Article not found" ? "Artikel tidak ditemukan" : "Error loading article"}
              </div>
              <p className="text-gray-600 mb-4">
                {error.message === "Article not found" 
                  ? "Artikel yang Anda cari tidak dapat ditemukan atau mungkin telah dihapus."
                  : "Silakan coba lagi nanti"}
              </p>
              <Link href="/news">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Kembali ke Berita
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!article) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Back Button */}
        <div className="mb-6">
          <Link href="/news">
            <Button variant="outline" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Kembali ke Berita
            </Button>
          </Link>
        </div>

        {/* Article */}
        <Card className="overflow-hidden">
          {/* Featured Image */}
          {article.featuredImage && (
            <div className="aspect-video w-full overflow-hidden">
              <img
                src={article.featuredImage}
                alt={article.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <CardContent className="p-8">
            {/* Article Meta */}
            <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-gray-600">
              <Badge variant="outline" className="gap-1">
                <Tag className="h-3 w-3" />
                {article.category.name}
              </Badge>
              
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>
                  {format(new Date(article.publishedAt), "dd MMMM yyyy", { locale: id })}
                </span>
              </div>
              
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>{article.author.name}</span>
              </div>
            </div>

            {/* Article Title */}
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
              {article.title}
            </h1>

            {/* Article Excerpt */}
            {article.excerpt && (
              <div className="text-xl text-gray-600 mb-8 leading-relaxed font-medium border-l-4 border-blue-500 pl-6">
                {article.excerpt}
              </div>
            )}

            {/* Article Content */}
            <div 
              className="prose prose-lg max-w-none prose-gray prose-headings:text-gray-900 prose-a:text-blue-600 prose-strong:text-gray-900"
              dangerouslySetInnerHTML={{ __html: article.content }}
            />
          </CardContent>
        </Card>

        {/* Related Articles Section */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Artikel Lainnya</h2>
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">Lihat artikel dan pengumuman lainnya</p>
            <Link href="/news">
              <Button>Lihat Semua Berita</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}