import { Search, Calendar, FileText, Users, TrendingUp } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";

import { ActiveProcurements } from "@/components/public/active-procurements";
import { DynamicBanner } from "@/components/public/dynamic-banner";
import { LatestNews } from "@/components/public/latest-news";
import { ProcurementStats } from "@/components/public/procurement-stats";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function PublicHomePage() {
  return (
    <div className="min-h-screen">
      {/* Dynamic Hero Banner */}
      <DynamicBanner
        fallbackContent={
          <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
            <div className="container mx-auto px-4">
              <div className="max-w-4xl mx-auto text-center">
                <h1 className="text-4xl md:text-6xl font-bold mb-6">
                  Portal E-Procurement
                </h1>
                <h2 className="text-xl md:text-2xl mb-8 text-blue-100">
                  PT Bank BPD Sulteng
                </h2>
                <p className="text-lg md:text-xl mb-8 text-blue-100">
                  Sistem pengadaan elektronik yang transparan, efisien, dan terpercaya
                  untuk mendukung operasional perbankan yang berkualitas.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/procurements">
                    <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                      <Search className="mr-2 h-5 w-5" />
                      Lihat Pengadaan Aktif
                    </Button>
                  </Link>
                  <Link href="/register">
                    <Button size="lg" variant="outline" className="w-full sm:w-auto text-white border-white hover:bg-white hover:text-blue-600">
                      <Users className="mr-2 h-5 w-5" />
                      Daftar Sebagai Vendor
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>
        }
      />

      {/* Quick Stats */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <Suspense fallback={<div className="animate-pulse h-32 bg-gray-200 rounded"></div>}>
            <ProcurementStats />
          </Suspense>
        </div>
      </section>

      {/* Active Procurements */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Pengadaan Aktif
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Temukan peluang bisnis terbaru dengan PT Bank BPD Sulteng.
              Proses pengadaan yang transparan dan kompetitif.
            </p>
          </div>

          <Suspense fallback={
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                    <div className="h-8 bg-gray-200 rounded w-full"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          }>
            <ActiveProcurements />
          </Suspense>

          <div className="text-center mt-12">
            <Link href="/procurements">
              <Button size="lg">
                Lihat Semua Pengadaan
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Latest News */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Berita & Pengumuman
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Informasi terbaru seputar kebijakan pengadaan, peraturan,
              dan pengumuman penting lainnya.
            </p>
          </div>

          <Suspense fallback={
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                    <div className="h-20 bg-gray-200 rounded w-full"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          }>
            <LatestNews />
          </Suspense>

          <div className="text-center mt-12">
            <Link href="/news">
              <Button variant="outline" size="lg">
                <FileText className="mr-2 h-5 w-5" />
                Lihat Semua Berita
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Mengapa Memilih E-Procurement Kami?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Search className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Transparan
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-gray-600 text-sm">
                  Proses pengadaan yang terbuka dan dapat diakses oleh semua vendor yang memenuhi syarat.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Efisien
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-gray-600 text-sm">
                  Sistem digital yang mempercepat proses pengadaan dari pendaftaran hingga kontrak.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <FileText className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle className="text-lg">
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    Terdokumentasi
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-gray-600 text-sm">
                  Semua proses tercatat dengan baik untuk audit trail dan kepatuhan regulasi.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle className="text-lg">
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                    Kompetitif
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-gray-600 text-sm">
                  Persaingan yang sehat menghasilkan nilai terbaik untuk bank dan vendor.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Siap Bergabung dengan Mitra Bisnis Kami?
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            Daftarkan perusahaan Anda dan mulai berpartisipasi dalam pengadaan PT Bank BPD Sulteng.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button size="lg" variant="secondary">
                Daftar Sekarang
              </Button>
            </Link>
            <Link href="/login">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600">
                Masuk ke Akun
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
