import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { documentService } from '@/lib/documents/document-service';
import { templateEngine } from '@/lib/documents/template-engine';
import { documentApprovalWorkflow } from '@/lib/documents/approval-workflow';
import { procurementDocumentIntegration } from '@/lib/documents/procurement-integration';
import { workflowAutomation } from '@/lib/documents/workflow-automation';
import { documentSecurity, DocumentPermission, AccessLevel } from '@/lib/documents/security';
import { documentSharing } from '@/lib/documents/sharing';

describe('Document Workflow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any test data
  });

  describe('Complete Document Lifecycle', () => {
    it('should handle complete document lifecycle from creation to approval', async () => {
      const userId = 'test-user-1';
      
      // 1. Create a template
      const template = await templateEngine.createTemplate(userId, {
        name: 'Integration Test Contract',
        description: 'Test template for integration',
        type: 'CONTRACT' as any,
        category: 'Legal',
        layout: {
          body: [
            {
              id: 'header',
              type: 'text',
              content: 'CONTRACT\n\nNumber: {{contractNumber}}\nVendor: {{vendor.name}}',
            },
          ],
        },
        variables: [
          {
            name: 'contractNumber',
            type: 'string',
            description: 'Contract number',
            required: true,
          },
          {
            name: 'vendor',
            type: 'object',
            description: 'Vendor information',
            required: true,
          },
        ],
        isActive: true,
      });

      expect(template).toBeDefined();
      expect(template.name).toBe('Integration Test Contract');

      // 2. Generate document from template
      const generatedDoc = await documentService.generateDocument(userId, {
        templateId: template.id!,
        name: 'Generated Integration Contract',
        data: {
          contractNumber: 'CONT-INT-001',
          vendor: {
            name: 'Integration Test Vendor',
            address: '123 Test Street',
          },
        },
        entityType: 'PROCUREMENT',
        entityId: 'proc-int-001',
        generatePdf: true,
      });

      expect(generatedDoc).toBeDefined();
      expect(generatedDoc.content).toContain('CONT-INT-001');
      expect(generatedDoc.content).toContain('Integration Test Vendor');

      // 3. Upload the generated document
      const uploadedDoc = await documentService.uploadDocument(
        userId,
        {
          buffer: Buffer.from(JSON.stringify(generatedDoc.content)),
          originalname: 'integration-contract.json',
          mimetype: 'application/json',
        },
        {
          documentType: 'CONTRACT',
          description: 'Integration test contract',
          requiresApproval: true,
          tags: ['integration', 'test'],
        }
      );

      expect(uploadedDoc).toBeDefined();
      expect(uploadedDoc.documentType).toBe('CONTRACT');

      // 4. Start approval workflow
      const approvalRequest = await documentApprovalWorkflow.startApprovalWorkflow(
        uploadedDoc.id,
        userId,
        'standard-document-approval',
        {
          documentType: 'CONTRACT',
          estimatedValue: 1000000,
        }
      );

      expect(approvalRequest).toBeDefined();
      expect(approvalRequest.status).toBe('IN_REVIEW');

      // 5. Process approval action
      const approvedRequest = await documentApprovalWorkflow.processApprovalAction(
        approvalRequest.id,
        approvalRequest.steps[0].stepId,
        'APPROVE' as any,
        'approver-user-1',
        'Approved for integration test'
      );

      expect(approvedRequest).toBeDefined();

      // 6. Share the approved document
      const shareResult = await documentSharing.shareDocument({
        documentId: uploadedDoc.id,
        sharedBy: userId,
        sharedWith: [
          {
            type: 'USER',
            identifier: 'recipient-user-1',
            name: 'Test Recipient',
          },
        ],
        permissions: [DocumentPermission.READ, DocumentPermission.DOWNLOAD],
        accessLevel: AccessLevel.INTERNAL,
        message: 'Sharing for integration test',
      });

      expect(shareResult.success).toBe(true);
      expect(shareResult.notifications).toHaveLength(1);
    });

    it('should handle procurement stage document generation', async () => {
      const userId = 'test-user-1';
      const procurementId = 'proc-stage-test';
      const stageId = 'stage-announcement';
      const stageType = 'ANNOUNCEMENT';

      // 1. Process workflow event for stage start
      await workflowAutomation.processWorkflowEvent({
        type: 'STAGE_STARTED',
        procurementId,
        stageId,
        stageType,
        userId,
        metadata: {
          procurementData: {
            title: 'Integration Test Procurement',
            estimatedValue: 5000000,
          },
        },
      });

      // 2. Generate stage documents
      const generatedDocs = await procurementDocumentIntegration.generateStageDocuments(
        {
          procurementId,
          stageId,
          stageType,
          procurementData: {
            title: 'Integration Test Procurement',
            estimatedValue: 5000000,
          },
        },
        userId
      );

      expect(Array.isArray(generatedDocs)).toBe(true);

      // 3. Get stage configuration
      const stageConfig = await procurementDocumentIntegration.getStageDocumentConfig(stageType);
      
      expect(stageConfig).toBeDefined();
      expect(stageConfig.stageType).toBe(stageType);
      expect(Array.isArray(stageConfig.requiredDocuments)).toBe(true);
    });

    it('should handle document security and access control', async () => {
      const userId = 'test-user-1';
      const documentId = 'security-test-doc';

      // 1. Check initial permissions
      const initialPermission = await documentSecurity.checkPermission(
        documentId,
        userId,
        DocumentPermission.READ
      );

      expect(typeof initialPermission.allowed).toBe('boolean');

      // 2. Grant permissions
      await documentSecurity.grantPermission(
        documentId,
        'USER',
        'test-user-2',
        [DocumentPermission.READ, DocumentPermission.WRITE],
        AccessLevel.INTERNAL,
        userId
      );

      // 3. Check granted permissions
      const grantedPermission = await documentSecurity.checkPermission(
        documentId,
        'test-user-2',
        DocumentPermission.READ
      );

      expect(typeof grantedPermission.allowed).toBe('boolean');

      // 4. Create share link
      const shareLink = await documentSharing.createShareLink(
        documentId,
        userId,
        [DocumentPermission.READ],
        {
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
          maxUses: 10,
        }
      );

      expect(shareLink).toBeDefined();
      expect(shareLink.token).toBeDefined();

      // 5. Access via share link
      const linkAccess = await documentSharing.accessViaShareLink(shareLink.token);
      
      expect(linkAccess.allowed).toBe(true);
      expect(linkAccess.documentId).toBe(documentId);

      // 6. Get security summary
      const securitySummary = await documentSecurity.getSecuritySummary(documentId);
      
      expect(securitySummary).toBeDefined();
      expect(typeof securitySummary.sharedWith).toBe('number');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle template generation with missing data', async () => {
      const userId = 'test-user-1';

      await expect(
        documentService.generateDocument(userId, {
          templateId: 'non-existent-template',
          name: 'Invalid Document',
          data: {},
          entityType: 'PROCUREMENT',
          entityId: 'proc-1',
        })
      ).rejects.toThrow();
    });

    it('should handle invalid file uploads', async () => {
      const userId = 'test-user-1';

      await expect(
        documentService.uploadDocument(
          userId,
          {
            buffer: Buffer.from('malicious content'),
            originalname: 'virus.exe',
            mimetype: 'application/x-executable',
          },
          {
            documentType: 'CONTRACT',
            description: 'Malicious file',
          }
        )
      ).rejects.toThrow('File type not allowed');
    });

    it('should handle unauthorized approval actions', async () => {
      const approvalRequestId = 'test-approval-request';
      const stepId = 'test-step';

      await expect(
        documentApprovalWorkflow.processApprovalAction(
          approvalRequestId,
          stepId,
          'APPROVE' as any,
          'unauthorized-user',
          'Unauthorized approval attempt'
        )
      ).rejects.toThrow();
    });

    it('should handle expired share links', async () => {
      const documentId = 'test-doc';
      const userId = 'test-user-1';

      // Create expired share link
      const expiredShareLink = await documentSharing.createShareLink(
        documentId,
        userId,
        [DocumentPermission.READ],
        {
          expiresAt: new Date(Date.now() - 1000), // Expired 1 second ago
        }
      );

      const linkAccess = await documentSharing.accessViaShareLink(expiredShareLink.token);
      
      expect(linkAccess.allowed).toBe(false);
      expect(linkAccess.reason).toContain('expired');
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle bulk document operations efficiently', async () => {
      const userId = 'test-user-1';
      const fileCount = 10;

      const mockFiles = Array.from({ length: fileCount }, (_, i) => ({
        buffer: Buffer.from(`Test content ${i}`),
        originalname: `test-${i}.pdf`,
        mimetype: 'application/pdf',
      }));

      const startTime = Date.now();
      
      const result = await documentService.bulkUpload(userId, mockFiles, {
        documentType: 'CONTRACT',
        description: 'Bulk upload performance test',
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result.successful).toHaveLength(fileCount);
      expect(result.failed).toHaveLength(0);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent document access', async () => {
      const documentId = 'concurrent-test-doc';
      const userCount = 5;

      const concurrentAccess = Array.from({ length: userCount }, (_, i) =>
        documentSecurity.checkPermission(
          documentId,
          `user-${i}`,
          DocumentPermission.READ
        )
      );

      const results = await Promise.all(concurrentAccess);
      
      expect(results).toHaveLength(userCount);
      results.forEach(result => {
        expect(typeof result.allowed).toBe('boolean');
      });
    });
  });

  describe('Data Consistency and Integrity', () => {
    it('should maintain audit trail consistency', async () => {
      const documentId = 'audit-test-doc';
      const userId = 'test-user-1';

      // Perform multiple actions
      await documentSecurity.logAuditEvent({
        documentId,
        userId,
        action: 'VIEW',
        details: { test: 'action1' },
        success: true,
      });

      await documentSecurity.logAuditEvent({
        documentId,
        userId,
        action: 'DOWNLOAD',
        details: { test: 'action2' },
        success: true,
      });

      const auditTrail = await documentSecurity.getAuditTrail(documentId);
      
      expect(Array.isArray(auditTrail)).toBe(true);
      // In a real implementation, we would verify the audit entries
    });

    it('should handle document version consistency', async () => {
      const userId = 'test-user-1';
      const documentId = 'version-test-doc';

      // Create multiple versions
      const version1 = await documentService.createDocumentVersion(
        documentId,
        userId,
        'Version 1 content',
        'Initial version'
      );

      const version2 = await documentService.createDocumentVersion(
        documentId,
        userId,
        'Version 2 content',
        'Updated content'
      );

      const versions = await documentService.getDocumentVersions(documentId, userId);
      
      expect(Array.isArray(versions)).toBe(true);
      // Versions should be ordered correctly
    });
  });
});
