import { describe, it, expect, beforeEach } from '@jest/globals';
import { templateEngine } from '@/lib/documents/template-engine';

describe('DocumentTemplateEngine', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createTemplate', () => {
    it('should create a new template', async () => {
      const templateOptions = {
        name: 'Test Contract Template',
        description: 'A test contract template',
        type: 'CONTRACT' as any,
        category: 'Legal',
        layout: {
          body: [
            {
              id: 'header',
              type: 'text' as any,
              content: 'CONTRACT AGREEMENT\n\nContract Number: {{contractNumber}}',
            },
            {
              id: 'parties',
              type: 'text' as any,
              content: 'Between {{vendor.name}} and {{organization.name}}',
            },
          ],
        },
        variables: [
          {
            name: 'contractNumber',
            type: 'string' as any,
            description: 'Contract number',
            required: true,
          },
          {
            name: 'vendor',
            type: 'object' as any,
            description: 'Vendor information',
            required: true,
          },
        ],
        isActive: true,
      };

      const result = await templateEngine.createTemplate('user-1', templateOptions);

      expect(result).toBeDefined();
      expect(result.name).toBe('Test Contract Template');
      expect(result.type).toBe('CONTRACT');
      expect(result.variables).toHaveLength(2);
    });

    it('should create template with inheritance', async () => {
      const templateOptions = {
        name: 'Extended Contract Template',
        description: 'Extended from base template',
        type: 'CONTRACT' as any,
        category: 'Legal',
        layout: {
          body: [
            {
              id: 'additional',
              type: 'text' as any,
              content: 'Additional terms: {{additionalTerms}}',
            },
          ],
        },
        variables: [
          {
            name: 'additionalTerms',
            type: 'string' as any,
            description: 'Additional contract terms',
            required: false,
          },
        ],
        isActive: true,
        parentTemplateId: 'base-template-id',
        overrides: {
          components: [
            {
              id: 'header',
              content: 'EXTENDED CONTRACT AGREEMENT\n\nContract Number: {{contractNumber}}',
            },
          ],
        },
      };

      const result = await templateEngine.createTemplate('user-1', templateOptions);

      expect(result).toBeDefined();
      expect(result.parentTemplateId).toBe('base-template-id');
    });
  });

  describe('generateDocument', () => {
    it('should generate document from template', async () => {
      const generateOptions = {
        templateId: 'template-1',
        name: 'Generated Contract',
        data: {
          contractNumber: 'CONT-2024-001',
          vendor: {
            name: 'Test Vendor Ltd.',
            address: '123 Test Street',
          },
          organization: {
            name: 'Test Organization',
            address: '456 Org Avenue',
          },
          amount: 1000000,
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        },
        entityType: 'PROCUREMENT',
        entityId: 'proc-1',
        generatePdf: true,
      };

      const result = await templateEngine.generateDocument('user-1', generateOptions);

      expect(result).toBeDefined();
      expect(result.name).toBe('Generated Contract');
      expect(result.content).toContain('CONT-2024-001');
      expect(result.content).toContain('Test Vendor Ltd.');
    });

    it('should validate required variables', async () => {
      const generateOptions = {
        templateId: 'template-1',
        name: 'Invalid Contract',
        data: {
          // Missing required contractNumber
          vendor: { name: 'Test Vendor' },
        },
        entityType: 'PROCUREMENT',
        entityId: 'proc-1',
      };

      await expect(
        templateEngine.generateDocument('user-1', generateOptions)
      ).rejects.toThrow('Missing required variables');
    });

    it('should handle conditional content', async () => {
      const generateOptions = {
        templateId: 'conditional-template',
        name: 'Conditional Document',
        data: {
          showOptionalSection: true,
          optionalContent: 'This is optional content',
          amount: 500000,
        },
        entityType: 'PROCUREMENT',
        entityId: 'proc-1',
      };

      const result = await templateEngine.generateDocument('user-1', generateOptions);

      expect(result.content).toContain('This is optional content');
    });
  });

  describe('searchTemplates', () => {
    it('should search templates with filters', async () => {
      const result = await templateEngine.searchTemplates({
        query: 'contract',
        type: 'CONTRACT' as any,
        category: 'Legal',
        status: 'ACTIVE' as any,
        isActive: true,
        limit: 10,
        offset: 0,
      });

      expect(result).toBeDefined();
      expect(Array.isArray(result.templates)).toBe(true);
      expect(result.pagination).toBeDefined();
    });
  });

  describe('validateTemplateData', () => {
    it('should validate template data against schema', async () => {
      const templateId = 'template-1';
      const validData = {
        contractNumber: 'CONT-2024-001',
        vendor: { name: 'Test Vendor' },
        amount: 1000000,
      };

      await expect(
        templateEngine.validateTemplateData(templateId, validData)
      ).resolves.not.toThrow();
    });

    it('should reject invalid data types', async () => {
      const templateId = 'template-1';
      const invalidData = {
        contractNumber: 123, // Should be string
        vendor: 'Invalid', // Should be object
        amount: 'invalid', // Should be number
      };

      await expect(
        templateEngine.validateTemplateData(templateId, invalidData)
      ).rejects.toThrow('Invalid data type');
    });
  });

  describe('getTemplateSuggestions', () => {
    it('should return template suggestions for procurement type', async () => {
      const suggestions = await templateEngine.getTemplateSuggestions(
        'RFQ',
        'ANNOUNCEMENT',
        'Procurement'
      );

      expect(Array.isArray(suggestions)).toBe(true);
      expect(suggestions.length).toBeGreaterThan(0);
      
      const rfqSuggestion = suggestions.find(s => s.type === 'RFQ');
      expect(rfqSuggestion).toBeDefined();
      expect(rfqSuggestion?.isRecommended).toBe(true);
    });

    it('should return contract suggestions for contract stage', async () => {
      const suggestions = await templateEngine.getTemplateSuggestions(
        '',
        'CONTRACT',
        'Legal'
      );

      const contractSuggestion = suggestions.find(s => s.type === 'CONTRACT');
      expect(contractSuggestion).toBeDefined();
      expect(contractSuggestion?.isRecommended).toBe(true);
    });
  });

  describe('Handlebars helpers', () => {
    it('should register and use string manipulation helpers', async () => {
      const generateOptions = {
        templateId: 'helper-test-template',
        name: 'Helper Test',
        data: {
          testString: 'hello world',
        },
        entityType: 'TEST',
        entityId: 'test-1',
      };

      // Mock template with helper usage
      const mockTemplate = {
        content: {
          body: [
            {
              id: 'test',
              type: 'text',
              content: '{{uppercase testString}} - {{capitalize testString}}',
            },
          ],
        },
      };

      const result = await templateEngine.generateDocument('user-1', generateOptions);
      
      // The helpers should transform the text
      expect(result.content).toContain('HELLO WORLD');
      expect(result.content).toContain('Hello world');
    });

    it('should use math helpers', async () => {
      const generateOptions = {
        templateId: 'math-template',
        name: 'Math Test',
        data: {
          price: 1000000,
          tax: 100000,
          total: 1100000,
        },
        entityType: 'TEST',
        entityId: 'test-1',
      };

      const result = await templateEngine.generateDocument('user-1', generateOptions);
      
      // Math helpers should work in template
      expect(result).toBeDefined();
    });

    it('should use date helpers', async () => {
      const generateOptions = {
        templateId: 'date-template',
        name: 'Date Test',
        data: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
        },
        entityType: 'TEST',
        entityId: 'test-1',
      };

      const result = await templateEngine.generateDocument('user-1', generateOptions);
      
      expect(result).toBeDefined();
    });

    it('should use procurement-specific helpers', async () => {
      const generateOptions = {
        templateId: 'procurement-template',
        name: 'Procurement Test',
        data: {
          procurementNumber: '123',
          contractNumber: '456',
          invoiceNumber: '789',
        },
        entityType: 'TEST',
        entityId: 'test-1',
      };

      const result = await templateEngine.generateDocument('user-1', generateOptions);
      
      // Should format numbers with proper prefixes
      expect(result).toBeDefined();
    });
  });

  describe('template inheritance', () => {
    it('should merge parent and child layouts', async () => {
      // This would test the private mergeTemplateLayouts method
      // through the public createTemplate method with inheritance
      const childTemplate = {
        name: 'Child Template',
        description: 'Inherits from parent',
        type: 'CONTRACT' as any,
        category: 'Legal',
        layout: {
          body: [
            {
              id: 'child-content',
              type: 'text' as any,
              content: 'Child specific content',
            },
          ],
        },
        variables: [],
        isActive: true,
        parentTemplateId: 'parent-template',
      };

      const result = await templateEngine.createTemplate('user-1', childTemplate);
      
      expect(result).toBeDefined();
      expect(result.parentTemplateId).toBe('parent-template');
    });

    it('should merge parent and child variables', async () => {
      const childTemplate = {
        name: 'Child Template with Variables',
        description: 'Inherits variables from parent',
        type: 'CONTRACT' as any,
        category: 'Legal',
        layout: { body: [] },
        variables: [
          {
            name: 'childVariable',
            type: 'string' as any,
            description: 'Child specific variable',
            required: false,
          },
        ],
        isActive: true,
        parentTemplateId: 'parent-template',
      };

      const result = await templateEngine.createTemplate('user-1', childTemplate);
      
      expect(result).toBeDefined();
      expect(result.variables.length).toBeGreaterThan(0);
    });
  });
});
