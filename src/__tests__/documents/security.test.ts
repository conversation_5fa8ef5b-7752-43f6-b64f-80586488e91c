import { describe, it, expect, beforeEach } from '@jest/globals';
import { documentSecurity, DocumentPermission, AccessLevel } from '@/lib/documents/security';

describe('DocumentSecurity', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkPermission', () => {
    it('should allow access for users with correct permissions', async () => {
      const result = await documentSecurity.checkPermission(
        'doc-1',
        'user-1',
        DocumentPermission.READ
      );

      expect(result.allowed).toBe(true);
    });

    it('should deny access for users without permissions', async () => {
      const result = await documentSecurity.checkPermission(
        'doc-1',
        'unauthorized-user',
        DocumentPermission.DELETE
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Insufficient permissions');
    });

    it('should check time restrictions', async () => {
      const context = {
        ipAddress: '***********',
        userAgent: 'Test Browser',
      };

      const result = await documentSecurity.checkPermission(
        'doc-with-time-restriction',
        'user-1',
        DocumentPermission.READ,
        context
      );

      // Result depends on current time and restrictions
      expect(typeof result.allowed).toBe('boolean');
    });

    it('should check IP restrictions', async () => {
      const context = {
        ipAddress: '********', // Restricted IP
        userAgent: 'Test Browser',
      };

      const result = await documentSecurity.checkPermission(
        'doc-with-ip-restriction',
        'user-1',
        DocumentPermission.READ,
        context
      );

      // Should be denied if IP is not in allowed list
      expect(typeof result.allowed).toBe('boolean');
    });
  });

  describe('grantPermission', () => {
    it('should grant permission to user', async () => {
      await expect(
        documentSecurity.grantPermission(
          'doc-1',
          'USER',
          'user-2',
          [DocumentPermission.READ, DocumentPermission.WRITE],
          AccessLevel.INTERNAL,
          'admin-user'
        )
      ).resolves.not.toThrow();
    });

    it('should grant permission to role', async () => {
      await expect(
        documentSecurity.grantPermission(
          'doc-1',
          'ROLE',
          'procurement-team',
          [DocumentPermission.READ],
          AccessLevel.INTERNAL,
          'admin-user'
        )
      ).resolves.not.toThrow();
    });

    it('should grant permission with conditions', async () => {
      const conditions = {
        timeRestriction: {
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          allowedHours: { start: 9, end: 17 },
        },
        ipRestriction: ['***********/24'],
      };

      await expect(
        documentSecurity.grantPermission(
          'doc-1',
          'USER',
          'user-2',
          [DocumentPermission.READ],
          AccessLevel.CONFIDENTIAL,
          'admin-user',
          conditions
        )
      ).resolves.not.toThrow();
    });
  });

  describe('revokePermission', () => {
    it('should revoke permission from user', async () => {
      await expect(
        documentSecurity.revokePermission(
          'doc-1',
          'USER',
          'user-2',
          'admin-user'
        )
      ).resolves.not.toThrow();
    });

    it('should revoke permission from role', async () => {
      await expect(
        documentSecurity.revokePermission(
          'doc-1',
          'ROLE',
          'procurement-team',
          'admin-user'
        )
      ).resolves.not.toThrow();
    });
  });

  describe('encryptContent and decryptContent', () => {
    it('should encrypt and decrypt content correctly', () => {
      const originalContent = 'This is sensitive document content';
      
      const encrypted = documentSecurity.encryptContent(originalContent);
      expect(encrypted.encryptedContent).toBeDefined();
      expect(encrypted.iv).toBeDefined();
      expect(encrypted.encryptedContent).not.toBe(originalContent);

      const decrypted = documentSecurity.decryptContent(
        encrypted.encryptedContent,
        encrypted.iv
      );
      expect(decrypted).toBe(originalContent);
    });

    it('should produce different encrypted content for same input', () => {
      const content = 'Test content';
      
      const encrypted1 = documentSecurity.encryptContent(content);
      const encrypted2 = documentSecurity.encryptContent(content);
      
      // Should be different due to random IV
      expect(encrypted1.encryptedContent).not.toBe(encrypted2.encryptedContent);
      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      
      // But both should decrypt to same content
      const decrypted1 = documentSecurity.decryptContent(encrypted1.encryptedContent, encrypted1.iv);
      const decrypted2 = documentSecurity.decryptContent(encrypted2.encryptedContent, encrypted2.iv);
      
      expect(decrypted1).toBe(content);
      expect(decrypted2).toBe(content);
    });
  });

  describe('generateSharingToken and validateSharingToken', () => {
    it('should generate and validate sharing token', () => {
      const documentId = 'doc-1';
      const permissions = [DocumentPermission.READ, DocumentPermission.DOWNLOAD];
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      const token = documentSecurity.generateSharingToken(documentId, permissions, expiresAt);
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');

      const validation = documentSecurity.validateSharingToken(token);
      expect(validation.valid).toBe(true);
      expect(validation.documentId).toBe(documentId);
      expect(validation.permissions).toEqual(permissions);
      expect(validation.expired).toBeFalsy();
    });

    it('should detect expired tokens', () => {
      const documentId = 'doc-1';
      const permissions = [DocumentPermission.READ];
      const expiresAt = new Date(Date.now() - 1000); // Expired 1 second ago

      const token = documentSecurity.generateSharingToken(documentId, permissions, expiresAt);
      
      const validation = documentSecurity.validateSharingToken(token);
      expect(validation.valid).toBe(false);
      expect(validation.expired).toBe(true);
    });

    it('should reject invalid tokens', () => {
      const invalidToken = 'invalid-token';
      
      const validation = documentSecurity.validateSharingToken(invalidToken);
      expect(validation.valid).toBe(false);
    });

    it('should reject tampered tokens', () => {
      const documentId = 'doc-1';
      const permissions = [DocumentPermission.READ];
      
      const validToken = documentSecurity.generateSharingToken(documentId, permissions);
      
      // Tamper with the token
      const tamperedToken = validToken.slice(0, -5) + 'XXXXX';
      
      const validation = documentSecurity.validateSharingToken(tamperedToken);
      expect(validation.valid).toBe(false);
    });
  });

  describe('logAuditEvent', () => {
    it('should log audit events', async () => {
      const auditEvent = {
        documentId: 'doc-1',
        userId: 'user-1',
        action: 'VIEW' as const,
        details: { method: 'web' },
        ipAddress: '***********',
        userAgent: 'Test Browser',
        success: true,
      };

      await expect(
        documentSecurity.logAuditEvent(auditEvent)
      ).resolves.not.toThrow();
    });

    it('should handle audit logging errors gracefully', async () => {
      const auditEvent = {
        documentId: 'doc-1',
        userId: 'user-1',
        action: 'VIEW' as const,
        details: { method: 'web' },
        success: true,
      };

      // Should not throw even if logging fails
      await expect(
        documentSecurity.logAuditEvent(auditEvent)
      ).resolves.not.toThrow();
    });
  });

  describe('getAuditTrail', () => {
    it('should return audit trail for document', async () => {
      const auditTrail = await documentSecurity.getAuditTrail('doc-1');
      
      expect(Array.isArray(auditTrail)).toBe(true);
    });

    it('should filter audit trail by options', async () => {
      const options = {
        userId: 'user-1',
        action: 'VIEW' as const,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        limit: 10,
        offset: 0,
      };

      const auditTrail = await documentSecurity.getAuditTrail('doc-1', options);
      
      expect(Array.isArray(auditTrail)).toBe(true);
    });
  });

  describe('getSecuritySummary', () => {
    it('should return security summary for document', async () => {
      const summary = await documentSecurity.getSecuritySummary('doc-1');
      
      expect(summary).toBeDefined();
      expect(Object.values(AccessLevel)).toContain(summary.accessLevel);
      expect(typeof summary.isEncrypted).toBe('boolean');
      expect(typeof summary.hasTimeRestrictions).toBe('boolean');
      expect(typeof summary.hasIpRestrictions).toBe('boolean');
      expect(typeof summary.sharedWith).toBe('number');
      expect(typeof summary.auditLogCount).toBe('number');
    });
  });

  describe('access conditions validation', () => {
    it('should validate time restrictions correctly', async () => {
      // Test during allowed hours
      const now = new Date();
      const allowedHours = {
        start: now.getHours() - 1,
        end: now.getHours() + 1,
      };

      // This tests the private method through public interface
      const result = await documentSecurity.checkPermission(
        'doc-with-time-restriction',
        'user-1',
        DocumentPermission.READ
      );

      expect(typeof result.allowed).toBe('boolean');
    });

    it('should validate date restrictions correctly', async () => {
      const now = new Date();
      const startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Yesterday
      const endDate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Tomorrow

      // Test within allowed date range
      const result = await documentSecurity.checkPermission(
        'doc-with-date-restriction',
        'user-1',
        DocumentPermission.READ
      );

      expect(typeof result.allowed).toBe('boolean');
    });
  });

  describe('permission inheritance', () => {
    it('should check role-based permissions', async () => {
      const result = await documentSecurity.checkPermission(
        'doc-1',
        'user-with-role',
        DocumentPermission.READ
      );

      expect(typeof result.allowed).toBe('boolean');
    });

    it('should check document ownership', async () => {
      const result = await documentSecurity.checkPermission(
        'doc-1',
        'document-owner',
        DocumentPermission.ADMIN
      );

      expect(typeof result.allowed).toBe('boolean');
    });
  });
});
