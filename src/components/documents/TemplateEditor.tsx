'use client';

import { 
  Save, 
  Eye, 
  Plus, 
  Trash2, 
  Move, 
  Type, 
  Table, 
  List, 
  Image, 
  FileSignature,
  Settings,
  Code,
  X
} from 'lucide-react';
import React, { useState, useEffect } from 'react';

interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
}

interface TemplateComponent {
  id: string;
  type: 'text' | 'table' | 'list' | 'image' | 'signature' | 'conditional';
  content: string;
  variables?: string[];
  style?: {
    fontSize?: number;
    fontWeight?: 'normal' | 'bold';
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    color?: string;
    backgroundColor?: string;
  };
}

interface TemplateLayout {
  header?: TemplateComponent[];
  body: TemplateComponent[];
  footer?: TemplateComponent[];
  styles?: {
    pageSize: 'A4' | 'Letter';
    margins: { top: number; right: number; bottom: number; left: number };
    fonts: { primary: string; secondary: string };
    colors: { primary: string; secondary: string; text: string };
  };
}

interface Template {
  id?: string;
  name: string;
  description?: string;
  type: string;
  category: string;
  layout: TemplateLayout;
  variables: TemplateVariable[];
  isActive: boolean;
}

interface TemplateEditorProps {
  template?: Template;
  onSave: (template: Template) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  onSave,
  onCancel,
  isOpen,
}) => {
  const [currentTemplate, setCurrentTemplate] = useState<Template>({
    name: '',
    description: '',
    type: 'CUSTOM',
    category: 'General',
    layout: {
      body: [],
      styles: {
        pageSize: 'A4',
        margins: { top: 20, right: 20, bottom: 20, left: 20 },
        fonts: { primary: 'Arial', secondary: 'Times New Roman' },
        colors: { primary: '#000000', secondary: '#666666', text: '#333333' },
      },
    },
    variables: [],
    isActive: true,
  });

  const [activeTab, setActiveTab] = useState<'design' | 'variables' | 'settings' | 'preview'>('design');
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [showVariableModal, setShowVariableModal] = useState(false);
  const [editingVariable, setEditingVariable] = useState<TemplateVariable | null>(null);

  useEffect(() => {
    if (template) {
      setCurrentTemplate(template);
    }
  }, [template]);

  if (!isOpen) {
    return null;
  }

  const componentTypes = [
    { type: 'text', icon: Type, label: 'Text' },
    { type: 'table', icon: Table, label: 'Table' },
    { type: 'list', icon: List, label: 'List' },
    { type: 'image', icon: Image, label: 'Image' },
    { type: 'signature', icon: FileSignature, label: 'Signature' },
  ];

  const addComponent = (type: string) => {
    const newComponent: TemplateComponent = {
      id: `component-${Date.now()}`,
      type: type as any,
      content: getDefaultContent(type),
      style: {
        fontSize: 14,
        fontWeight: 'normal',
        textAlign: 'left',
        color: '#333333',
      },
    };

    setCurrentTemplate(prev => ({
      ...prev,
      layout: {
        ...prev.layout,
        body: [...prev.layout.body, newComponent],
      },
    }));
  };

  const getDefaultContent = (type: string): string => {
    switch (type) {
      case 'text':
        return 'Enter your text here. Use {{variableName}} for dynamic content.';
      case 'table':
        return `| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| {{item.col1}} | {{item.col2}} | {{item.col3}} |`;
      case 'list':
        return `{{#each items}}
- {{this.name}}
{{/each}}`;
      case 'image':
        return '{{imageUrl}}';
      case 'signature':
        return '{{signature}}';
      default:
        return '';
    }
  };

  const updateComponent = (componentId: string, updates: Partial<TemplateComponent>) => {
    setCurrentTemplate(prev => ({
      ...prev,
      layout: {
        ...prev.layout,
        body: prev.layout.body.map(comp =>
          comp.id === componentId ? { ...comp, ...updates } : comp
        ),
      },
    }));
  };

  const deleteComponent = (componentId: string) => {
    setCurrentTemplate(prev => ({
      ...prev,
      layout: {
        ...prev.layout,
        body: prev.layout.body.filter(comp => comp.id !== componentId),
      },
    }));
    setSelectedComponent(null);
  };

  const addVariable = (variable: TemplateVariable) => {
    setCurrentTemplate(prev => ({
      ...prev,
      variables: [...prev.variables, variable],
    }));
    setShowVariableModal(false);
    setEditingVariable(null);
  };

  const updateVariable = (index: number, variable: TemplateVariable) => {
    setCurrentTemplate(prev => ({
      ...prev,
      variables: prev.variables.map((v, i) => i === index ? variable : v),
    }));
    setShowVariableModal(false);
    setEditingVariable(null);
  };

  const deleteVariable = (index: number) => {
    setCurrentTemplate(prev => ({
      ...prev,
      variables: prev.variables.filter((_, i) => i !== index),
    }));
  };

  const handleSave = () => {
    onSave(currentTemplate);
  };

  const renderDesignTab = () => (
    <div className="flex h-full">
      {/* Component Palette */}
      <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-4">Components</h3>
        <div className="space-y-2">
          {componentTypes.map(({ type, icon: Icon, label }) => (
            <button
              key={type}
              onClick={() => addComponent(type)}
              className="w-full flex items-center space-x-2 p-2 text-left text-sm text-gray-700 hover:bg-gray-100 rounded-md"
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="max-w-2xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm min-h-[800px] p-8">
          {currentTemplate.layout.body.length === 0 ? (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <div className="text-center">
                <Type className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No components</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Add components from the palette to start building your template.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {currentTemplate.layout.body.map((component) => (
                <div
                  key={component.id}
                  className={`relative p-4 border rounded-md cursor-pointer ${
                    selectedComponent === component.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedComponent(component.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-gray-500 uppercase">
                      {component.type}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteComponent(component.id);
                      }}
                      className="text-gray-400 hover:text-red-500"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                  <div
                    style={{
                      fontSize: component.style?.fontSize || 14,
                      fontWeight: component.style?.fontWeight || 'normal',
                      textAlign: component.style?.textAlign || 'left',
                      color: component.style?.color || '#333333',
                    }}
                  >
                    {component.type === 'table' ? (
                      <pre className="text-xs whitespace-pre-wrap">{component.content}</pre>
                    ) : (
                      <div className="whitespace-pre-wrap">{component.content}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Properties Panel */}
      {selectedComponent && (
        <div className="w-80 bg-gray-50 border-l border-gray-200 p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Properties</h3>
          {(() => {
            const component = currentTemplate.layout.body.find(c => c.id === selectedComponent);
            if (!component) return null;

            return (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Content
                  </label>
                  <textarea
                    value={component.content}
                    onChange={(e) => updateComponent(component.id, { content: e.target.value })}
                    className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="Enter content..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Font Size
                  </label>
                  <input
                    type="number"
                    value={component.style?.fontSize || 14}
                    onChange={(e) => updateComponent(component.id, {
                      style: { ...component.style, fontSize: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    min="8"
                    max="72"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Font Weight
                  </label>
                  <select
                    value={component.style?.fontWeight || 'normal'}
                    onChange={(e) => updateComponent(component.id, {
                      style: { ...component.style, fontWeight: e.target.value as 'normal' | 'bold' }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="normal">Normal</option>
                    <option value="bold">Bold</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Text Align
                  </label>
                  <select
                    value={component.style?.textAlign || 'left'}
                    onChange={(e) => updateComponent(component.id, {
                      style: { ...component.style, textAlign: e.target.value as any }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                    <option value="justify">Justify</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Text Color
                  </label>
                  <input
                    type="color"
                    value={component.style?.color || '#333333'}
                    onChange={(e) => updateComponent(component.id, {
                      style: { ...component.style, color: e.target.value }
                    })}
                    className="w-full h-10 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );

  const renderVariablesTab = () => (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900">Template Variables</h3>
        <button
          onClick={() => {
            setEditingVariable(null);
            setShowVariableModal(true);
          }}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Variable
        </button>
      </div>

      {currentTemplate.variables.length === 0 ? (
        <div className="text-center py-8">
          <Code className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No variables</h3>
          <p className="mt-1 text-sm text-gray-500">
            Add variables to make your template dynamic.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {currentTemplate.variables.map((variable, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                      {`{{${variable.name}}}`}
                    </span>
                    <span className="text-xs text-gray-500 uppercase">{variable.type}</span>
                    {variable.required && (
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                        Required
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{variable.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setEditingVariable(variable);
                      setShowVariableModal(true);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <Settings className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => deleteVariable(index)}
                    className="text-gray-400 hover:text-red-600"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" />
      
      <div className="relative h-full flex flex-col bg-white">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-900">
              {template ? 'Edit Template' : 'Create Template'}
            </h2>
            <input
              type="text"
              value={currentTemplate.name}
              onChange={(e) => setCurrentTemplate(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Template name"
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            />
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleSave}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              Save
            </button>
            <button
              onClick={onCancel}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50"
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'design', label: 'Design', icon: Type },
              { id: 'variables', label: 'Variables', icon: Code },
              { id: 'settings', label: 'Settings', icon: Settings },
              { id: 'preview', label: 'Preview', icon: Eye },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'design' && renderDesignTab()}
          {activeTab === 'variables' && renderVariablesTab()}
          {activeTab === 'settings' && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-6">Template Settings</h3>
              {/* Add settings form here */}
            </div>
          )}
          {activeTab === 'preview' && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-6">Preview</h3>
              {/* Add preview here */}
            </div>
          )}
        </div>
      </div>

      {/* Variable Modal */}
      {showVariableModal && (
        <div className="fixed inset-0 z-60 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={() => setShowVariableModal(false)} />
            <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingVariable ? 'Edit Variable' : 'Add Variable'}
              </h3>
              {/* Add variable form here */}
              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => setShowVariableModal(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Handle save variable
                    setShowVariableModal(false);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateEditor;
