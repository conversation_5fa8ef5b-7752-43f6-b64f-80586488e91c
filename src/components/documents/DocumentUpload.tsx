'use client';

import { 
  Upload, 
  X, 
  FileText, 
  Image, 
  FileSpreadsheet, 
  AlertCircle, 
  CheckCircle,
  Tag,
  Lock,
  Eye
} from 'lucide-react';
import React, { useState, useCallback } from 'react';

interface DocumentUploadProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (files: FileList, metadata: DocumentMetadata) => Promise<void>;
  procurementId?: string;
  stageId?: string;
  allowedTypes?: string[];
  maxFileSize?: number;
}

interface DocumentMetadata {
  documentType: string;
  description: string;
  tags: string[];
  isConfidential: boolean;
  requiresApproval: boolean;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  isOpen,
  onClose,
  onUpload,
  procurementId,
  stageId,
  allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'],
  maxFileSize = 50 * 1024 * 1024, // 50MB
}) => {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [metadata, setMetadata] = useState<DocumentMetadata>({
    documentType: '',
    description: '',
    tags: [],
    isConfidential: false,
    requiresApproval: false,
  });
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  const documentTypes = [
    'RFQ',
    'CONTRACT',
    'PURCHASE_ORDER',
    'INVOICE',
    'BAST',
    'AANWIJZING',
    'EVALUATION_REPORT',
    'AWARD_LETTER',
    'PROPOSAL',
    'TECHNICAL_SPEC',
    'FINANCIAL_PROPOSAL',
    'COMPANY_PROFILE',
    'SPECIFICATION',
    'TERMS_CONDITIONS',
    'OTHER',
  ];

  const validateFiles = (files: FileList): string[] => {
    const validationErrors: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Check file size
      if (file.size > maxFileSize) {
        validationErrors.push(`${file.name}: File size exceeds ${formatFileSize(maxFileSize)}`);
      }

      // Check file type
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (extension && !allowedTypes.includes(extension)) {
        validationErrors.push(`${file.name}: File type .${extension} is not allowed`);
      }
    }

    return validationErrors;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (extension === 'pdf') return <FileText className="w-8 h-8 text-red-500" />;
    if (['jpg', 'jpeg', 'png', 'gif'].includes(extension || '')) return <Image className="w-8 h-8 text-blue-500" />;
    if (['xls', 'xlsx'].includes(extension || '')) return <FileSpreadsheet className="w-8 h-8 text-green-500" />;
    return <FileText className="w-8 h-8 text-gray-500" />;
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const files = e.dataTransfer.files;
      const validationErrors = validateFiles(files);
      
      if (validationErrors.length > 0) {
        setErrors(validationErrors);
      } else {
        setSelectedFiles(files);
        setErrors([]);
      }
    }
  }, [allowedTypes, maxFileSize]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const files = e.target.files;
      const validationErrors = validateFiles(files);
      
      if (validationErrors.length > 0) {
        setErrors(validationErrors);
      } else {
        setSelectedFiles(files);
        setErrors([]);
      }
    }
  };

  const addTag = () => {
    if (newTag.trim() && !metadata.tags.includes(newTag.trim())) {
      setMetadata(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setMetadata(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleUpload = async () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      setErrors(['Please select at least one file']);
      return;
    }

    if (!metadata.documentType) {
      setErrors(['Please select a document type']);
      return;
    }

    setUploading(true);
    try {
      await onUpload(selectedFiles, metadata);
      // Reset form
      setSelectedFiles(null);
      setMetadata({
        documentType: '',
        description: '',
        tags: [],
        isConfidential: false,
        requiresApproval: false,
      });
      onClose();
    } catch (error) {
      setErrors([error instanceof Error ? error.message : 'Upload failed']);
    } finally {
      setUploading(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Upload Documents</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {/* File Drop Zone */}
            <div
              className={`relative border-2 border-dashed rounded-lg p-8 text-center ${
                dragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                multiple
                accept={allowedTypes.map(type => `.${type}`).join(',')}
                onChange={handleFileSelect}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                Drop files here or click to browse
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Supported formats: {allowedTypes.join(', ')}
              </p>
              <p className="text-sm text-gray-500">
                Maximum file size: {formatFileSize(maxFileSize)}
              </p>
            </div>

            {/* Selected Files */}
            {selectedFiles && selectedFiles.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-900">Selected Files</h3>
                {Array.from(selectedFiles).map((file, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                    {getFileIcon(file.name)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                      <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                    </div>
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  </div>
                ))}
              </div>
            )}

            {/* Errors */}
            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <AlertCircle className="w-5 h-5 text-red-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Upload Errors</h3>
                    <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                      {errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Metadata Form */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900">Document Information</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Document Type *
                </label>
                <select
                  value={metadata.documentType}
                  onChange={(e) => setMetadata(prev => ({ ...prev, documentType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select document type</option>
                  {documentTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={metadata.description}
                  onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter document description..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <div className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    placeholder="Add tag..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={addTag}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                  >
                    Add
                  </button>
                </div>
                {metadata.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {metadata.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                      >
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-blue-600 hover:text-blue-800"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isConfidential"
                    checked={metadata.isConfidential}
                    onChange={(e) => setMetadata(prev => ({ ...prev, isConfidential: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isConfidential" className="ml-2 flex items-center text-sm text-gray-700">
                    <Lock className="w-4 h-4 mr-1 text-red-500" />
                    Mark as confidential
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="requiresApproval"
                    checked={metadata.requiresApproval}
                    onChange={(e) => setMetadata(prev => ({ ...prev, requiresApproval: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="requiresApproval" className="ml-2 flex items-center text-sm text-gray-700">
                    <Eye className="w-4 h-4 mr-1 text-yellow-500" />
                    Requires approval
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50"
              disabled={uploading}
            >
              Cancel
            </button>
            <button
              onClick={handleUpload}
              disabled={!selectedFiles || selectedFiles.length === 0 || uploading}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading...
                </div>
              ) : (
                'Upload Documents'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUpload;
