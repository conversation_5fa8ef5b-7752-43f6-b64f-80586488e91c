'use client';

import { useQuery } from '@tanstack/react-query';
import {
  Workflow,
  FileText,
  Calendar,
  Target,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowRight,
} from 'lucide-react';
import Link from 'next/link';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface WorkflowOverviewData {
  totalWorkflows: number;
  activeWorkflows: number;
  totalTemplates: number;
  activeTemplates: number;
  totalRequirements: number;
  activeRequirements: number;
  totalSchedules: number;
  activeSchedules: number;
  overdueCount: number;
  upcomingDeadlines: number;
  completionRate: number;
  avgProcessingTime: number;
}

async function fetchWorkflowOverview(): Promise<WorkflowOverviewData> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch('/api/admin/dashboard/workflow-stats', { headers });
  if (!response.ok) throw new Error('Failed to fetch workflow overview');
  return response.json();
}

export function WorkflowOverviewWidget() {
  const { data: overview, isLoading, error } = useQuery({
    queryKey: ['workflow-overview'],
    queryFn: fetchWorkflowOverview,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Workflow className="h-5 w-5" />
            Workflow Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading workflow data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Workflow className="h-5 w-5" />
            Workflow Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-600">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Failed to load workflow data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Workflow className="h-5 w-5" />
            Workflow Overview
          </div>
          <Link href="/admin/workflows">
            <Button variant="ghost" size="sm">
              View All
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {overview?.totalWorkflows || 0}
            </div>
            <div className="text-sm text-gray-600">Total Workflows</div>
            <div className="text-xs text-green-600">
              {overview?.activeWorkflows || 0} active
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {overview?.totalTemplates || 0}
            </div>
            <div className="text-sm text-gray-600">Templates</div>
            <div className="text-xs text-green-600">
              {overview?.activeTemplates || 0} active
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {overview?.totalRequirements || 0}
            </div>
            <div className="text-sm text-gray-600">Requirements</div>
            <div className="text-xs text-green-600">
              {overview?.activeRequirements || 0} active
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {overview?.totalSchedules || 0}
            </div>
            <div className="text-sm text-gray-600">Schedules</div>
            <div className="text-xs text-green-600">
              {overview?.activeSchedules || 0} active
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Completion Rate</span>
            </div>
            <div className="text-lg font-bold text-blue-600">
              {overview?.completionRate || 0}%
            </div>
          </div>

          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Avg Processing</span>
            </div>
            <div className="text-lg font-bold text-green-600">
              {overview?.avgProcessingTime || 0}d
            </div>
          </div>
        </div>

        {/* Alerts */}
        {((overview?.overdueCount || 0) > 0 || (overview?.upcomingDeadlines || 0) > 0) && (
          <div className="space-y-2">
            {(overview?.overdueCount || 0) > 0 && (
              <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">
                    Overdue Items
                  </span>
                </div>
                <Badge variant="destructive">
                  {overview?.overdueCount || 0}
                </Badge>
              </div>
            )}

            {(overview?.upcomingDeadlines || 0) > 0 && (
              <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    Upcoming Deadlines
                  </span>
                </div>
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                  {overview?.upcomingDeadlines || 0}
                </Badge>
              </div>
            )}
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-2">
          <Link href="/admin/workflows?tab=requirements">
            <Button variant="outline" size="sm" className="w-full">
              <Target className="h-4 w-4 mr-2" />
              Requirements
            </Button>
          </Link>

          <Link href="/admin/workflows?tab=schedules">
            <Button variant="outline" size="sm" className="w-full">
              <Calendar className="h-4 w-4 mr-2" />
              Schedules
            </Button>
          </Link>

          <Link href="/admin/workflows?tab=templates">
            <Button variant="outline" size="sm" className="w-full">
              <FileText className="h-4 w-4 mr-2" />
              Templates
            </Button>
          </Link>

          <Link href="/admin/workflows?tab=builder">
            <Button variant="outline" size="sm" className="w-full">
              <CheckCircle className="h-4 w-4 mr-2" />
              Builder
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
