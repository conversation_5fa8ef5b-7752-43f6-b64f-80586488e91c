"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Upload, Trash2, Eye, Download, Image, FileText } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface PublicAsset {
  url: string;
  key: string;
  filename: string;
  size: number;
  contentType: string;
}

interface AssetListResponse {
  assets: string[];
  count: number;
}

const ASSET_CATEGORIES = [
  { value: "news", label: "Berita", icon: FileText },
  { value: "announcements", label: "Pengumuman", icon: FileText },
  { value: "banners", label: "Banner", icon: Image },
  { value: "logos", label: "Logo", icon: Image },
  { value: "documents", label: "Dokumen", icon: FileText },
];

async function fetchPublicAssets(category?: string): Promise<AssetListResponse> {
  const params = new URLSearchParams();
  if (category) params.append("category", category);

  // Get auth token from localStorage
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/public-assets?${params}`, {
    headers,
  });
  if (!response.ok) {
    throw new Error("Failed to fetch public assets");
  }
  const result = await response.json();
  return result.data;
}

async function uploadPublicAsset(file: File, category: string): Promise<PublicAsset> {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("category", category);

  // Get auth token from localStorage
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/public-assets/upload", {
    method: "POST",
    headers,
    body: formData,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to upload asset");
  }

  const result = await response.json();
  return result.data;
}

async function deletePublicAsset(key: string): Promise<void> {
  const response = await fetch(`/api/public-assets/${encodeURIComponent(key)}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to delete asset");
  }
}

export function PublicAssetManager() {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [uploadCategory, setUploadCategory] = useState<string>("news");
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const queryClient = useQueryClient();

  const { data: assets, isLoading, error } = useQuery({
    queryKey: ["public-assets", selectedCategory === "all" ? undefined : selectedCategory],
    queryFn: () => fetchPublicAssets(selectedCategory === "all" ? undefined : selectedCategory),
  });

  const uploadMutation = useMutation({
    mutationFn: ({ file, category }: { file: File; category: string }) =>
      uploadPublicAsset(file, category),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["public-assets"] });
      setUploadDialogOpen(false);
      setUploadFile(null);
      toast.success("Asset berhasil diupload");
    },
    onError: (error: Error) => {
      toast.error(`Gagal upload asset: ${error.message}`);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deletePublicAsset,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["public-assets"] });
      toast.success("Asset berhasil dihapus");
    },
    onError: (error: Error) => {
      toast.error(`Gagal hapus asset: ${error.message}`);
    },
  });

  const handleUpload = () => {
    if (!uploadFile) {
      toast.error("Pilih file terlebih dahulu");
      return;
    }

    uploadMutation.mutate({
      file: uploadFile,
      category: uploadCategory,
    });
  };

  const handleDelete = (key: string) => {
    if (confirm("Apakah Anda yakin ingin menghapus asset ini?")) {
      deleteMutation.mutate(key);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getAssetCategory = (key: string) => {
    const parts = key.split("/");
    return parts[1] || "unknown";
  };

  const getAssetIcon = (contentType: string) => {
    if (contentType.startsWith("image/")) {
      return <Image className="h-4 w-4" />;
    }
    return <FileText className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Manajemen Asset Publik</h2>
          <p className="text-gray-600">
            Kelola asset untuk halaman landing dan konten publik
          </p>
        </div>
        
        <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
          <DialogTrigger asChild={true}>
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Upload Asset
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload Asset Publik</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="category">Kategori</Label>
                <Select value={uploadCategory} onValueChange={setUploadCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ASSET_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="file">File</Label>
                <Input
                  id="file"
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setUploadFile(e.target.files?.[0] || null)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maksimal 10MB. Format: JPG, PNG, WebP, SVG, GIF, PDF
                </p>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setUploadDialogOpen(false)}
                >
                  Batal
                </Button>
                <Button
                  onClick={handleUpload}
                  disabled={!uploadFile || uploadMutation.isPending}
                >
                  {uploadMutation.isPending ? "Uploading..." : "Upload"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList>
          <TabsTrigger value="all">Semua</TabsTrigger>
          {ASSET_CATEGORIES.map((category) => (
            <TabsTrigger key={category.value} value={category.value}>
              {category.label}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={selectedCategory} className="mt-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="h-32 bg-gray-200 rounded mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-red-500 mb-2">Error loading assets</div>
                <p className="text-gray-600">Please try again later</p>
              </CardContent>
            </Card>
          ) : assets?.assets.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Belum ada asset
                </h3>
                <p className="text-gray-600">
                  Upload asset pertama untuk kategori ini
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {assets?.assets.map((assetKey) => {
                const category = getAssetCategory(assetKey);
                const filename = assetKey.split("/").pop() || assetKey;
                const isImage = assetKey.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i);
                
                return (
                  <Card key={assetKey} className="overflow-hidden">
                    <CardContent className="p-4">
                      {isImage ? (
                        <div className="aspect-video bg-gray-100 rounded mb-3 overflow-hidden">
                          <img
                            src={`/assets/${assetKey}`}
                            alt={filename}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="aspect-video bg-gray-100 rounded mb-3 flex items-center justify-center">
                          <FileText className="h-12 w-12 text-gray-400" />
                        </div>
                      )}
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs">
                            {ASSET_CATEGORIES.find(c => c.value === category)?.label || category}
                          </Badge>
                        </div>
                        
                        <h4 className="font-medium text-sm truncate" title={filename}>
                          {filename}
                        </h4>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(`/assets/${assetKey}`, "_blank")}
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const link = document.createElement("a");
                                link.href = `/assets/${assetKey}`;
                                link.download = filename;
                                link.click();
                              }}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(assetKey)}
                              disabled={deleteMutation.isPending}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
