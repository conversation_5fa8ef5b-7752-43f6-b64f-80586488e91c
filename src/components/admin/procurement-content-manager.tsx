"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Plus, Edit, Trash2, Eye, Calendar, User, Package, Image, X } from "lucide-react";
import { useState, useCallback, memo } from "react";
import { toast } from "sonner";

import { AssetPicker } from "@/components/ui/asset-picker";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";


interface ProcurementContent {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  type: "GUIDELINE" | "ANNOUNCEMENT" | "PROCEDURE" | "FAQ" | "TERMS";
  status: "DRAFT" | "PUBLISHED";
  featuredImage?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
}

interface ProcurementContentFormData {
  title: string;
  excerpt: string;
  content: string;
  type: "GUIDELINE" | "ANNOUNCEMENT" | "PROCEDURE" | "FAQ" | "TERMS";
  status: "DRAFT" | "PUBLISHED";
  featuredImage?: string;
}

const CONTENT_TYPES = {
  GUIDELINE: "Panduan Pengadaan",
  ANNOUNCEMENT: "Pengumuman Pengadaan",
  PROCEDURE: "Prosedur & Tata Cara",
  FAQ: "FAQ Pengadaan",
  TERMS: "Syarat & Ketentuan"
};

async function fetchProcurementContent(): Promise<ProcurementContent[]> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/procurement-content", { headers });
  if (!response.ok) throw new Error("Failed to fetch procurement content");
  const data = await response.json();
  return data.content || [];
}

async function createProcurementContent(data: ProcurementContentFormData): Promise<ProcurementContent> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/procurement-content", {
    method: "POST",
    headers,
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error("Failed to create procurement content");
  return response.json();
}

async function updateProcurementContent(id: string, data: Partial<ProcurementContentFormData>): Promise<ProcurementContent> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/procurement-content/${id}`, {
    method: "PUT",
    headers,
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error("Failed to update procurement content");
  return response.json();
}

async function deleteProcurementContent(id: string): Promise<void> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/procurement-content/${id}`, {
    method: "DELETE",
    headers,
  });
  if (!response.ok) throw new Error("Failed to delete procurement content");
}

function getStatusBadge(status: string) {
  const statusConfig = {
    DRAFT: { label: "Draft", variant: "secondary" as const },
    PUBLISHED: { label: "Published", variant: "default" as const },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

function getTypeBadge(type: string) {
  const typeConfig = {
    GUIDELINE: { label: "Panduan", variant: "outline" as const },
    ANNOUNCEMENT: { label: "Pengumuman", variant: "default" as const },
    PROCEDURE: { label: "Prosedur", variant: "secondary" as const },
    FAQ: { label: "FAQ", variant: "outline" as const },
    TERMS: { label: "S&K", variant: "destructive" as const },
  };

  const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.GUIDELINE;
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

// Separate ContentForm component
interface ContentFormProps {
  isEdit?: boolean;
  formData: ProcurementContentFormData;
  onTitleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onExcerptChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onContentChange: (data: string) => void;
  onTypeChange: (value: "GUIDELINE" | "ANNOUNCEMENT" | "PROCEDURE" | "FAQ" | "TERMS") => void;
  onStatusChange: (value: "DRAFT" | "PUBLISHED") => void;
  onFeaturedImageChange: (imageUrl: string) => void;
  onFeaturedImageRemove: () => void;
  onFocus: () => void;
  onBlur: () => void;
  onCancel: () => void;
  onSubmit: () => void;
}

const ContentForm = memo(({
  isEdit = false,
  formData,
  onTitleChange,
  onExcerptChange,
  onContentChange,
  onTypeChange,
  onStatusChange,
  onFocus,
  onBlur,
  onCancel,
  onSubmit,
}: ContentFormProps) => {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="title">Judul Konten *</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={onTitleChange}
          placeholder="Masukkan judul konten pengadaan"
        />
      </div>

      <div>
        <Label htmlFor="excerpt">Ringkasan</Label>
        <Textarea
          id="excerpt"
          value={formData.excerpt}
          onChange={onExcerptChange}
          placeholder="Ringkasan singkat konten"
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="content">Konten *</Label>
        <RichTextEditor
          value={formData.content}
          onChange={onContentChange}
          placeholder="Tulis konten pengadaan yang informatif di sini. Anda dapat menambahkan panduan, prosedur, atau informasi penting lainnya."
          className="min-h-96"
          onFocus={onFocus}
          onBlur={onBlur}
        />
        <div className="mt-2 text-sm text-gray-600">
          <div className="flex items-center gap-4 flex-wrap">
            <div>📋 Editor konten lengkap dengan:</div>
            <div>📷 Upload gambar</div>
            <div>📄 Upload dokumen</div>
            <div>📊 Tabel dan formatting</div>
            <div>🎨 Text styling</div>
          </div>
        </div>
      </div>

      <div>
        <Label htmlFor="type">Jenis Konten *</Label>
        <Select
          value={formData.type}
          onValueChange={onTypeChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih jenis konten" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(CONTENT_TYPES).map(([key, label]) => (
              <SelectItem key={key} value={key}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="status">Status *</Label>
        <Select
          value={formData.status}
          onValueChange={onStatusChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="DRAFT">Draft</SelectItem>
            <SelectItem value="PUBLISHED">Published</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          onClick={onCancel}
        >
          Batal
        </Button>
        <Button
          onClick={onSubmit}
          disabled={!formData.title || !formData.content || !formData.type}
        >
          {isEdit ? "Perbarui" : "Buat"} Konten
        </Button>
      </div>
    </div>
  );
});

ContentForm.displayName = 'ContentForm';

export function ProcurementContentManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingContent, setEditingContent] = useState<ProcurementContent | null>(null);
  const [formData, setFormData] = useState<ProcurementContentFormData>({
    title: "",
    excerpt: "",
    content: "",
    type: "GUIDELINE",
    status: "DRAFT",
  });

  const queryClient = useQueryClient();

  const { data: content = [], isLoading: contentLoading } = useQuery({
    queryKey: ["admin-procurement-content"],
    queryFn: fetchProcurementContent,
  });

  const createMutation = useMutation({
    mutationFn: createProcurementContent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-procurement-content"] });
      setIsCreateDialogOpen(false);
      setFormData({
        title: "",
        excerpt: "",
        content: "",
        type: "GUIDELINE",
        status: "DRAFT",
      });
      toast.success("Konten pengadaan berhasil dibuat");
    },
    onError: () => {
      toast.error("Gagal membuat konten pengadaan");
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ProcurementContentFormData> }) =>
      updateProcurementContent(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-procurement-content"] });
      setIsEditDialogOpen(false);
      setEditingContent(null);
      toast.success("Konten pengadaan berhasil diperbarui");
    },
    onError: () => {
      toast.error("Gagal memperbarui konten pengadaan");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteProcurementContent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-procurement-content"] });
      toast.success("Konten pengadaan berhasil dihapus");
    },
    onError: () => {
      toast.error("Gagal menghapus konten pengadaan");
    },
  });

  const handleCreate = () => {
    createMutation.mutate(formData);
  };

  const handleEdit = (content: ProcurementContent) => {
    setEditingContent(content);
    setFormData({
      title: content.title,
      excerpt: content.excerpt || "",
      content: content.content,
      type: content.type,
      status: content.status,
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdate = () => {
    if (!editingContent) return;
    updateMutation.mutate({ id: editingContent.id, data: formData });
  };

  const handleDelete = (id: string) => {
    if (confirm("Apakah Anda yakin ingin menghapus konten ini?")) {
      deleteMutation.mutate(id);
    }
  };

  // Memoized callbacks
  const handleContentChange = useCallback((data: string) => {
    setFormData(prev => ({ ...prev, content: data }));
  }, []);

  const handleTitleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, title: e.target.value }));
  }, []);

  const handleExcerptChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, excerpt: e.target.value }));
  }, []);

  const handleTypeChange = useCallback((value: "GUIDELINE" | "ANNOUNCEMENT" | "PROCEDURE" | "FAQ" | "TERMS") => {
    setFormData(prev => ({ ...prev, type: value }));
  }, []);

  const handleStatusChange = useCallback((value: "DRAFT" | "PUBLISHED") => {
    setFormData(prev => ({ ...prev, status: value }));
  }, []);

  const handleFeaturedImageChange = useCallback((imageUrl: string) => {
    setFormData(prev => ({ ...prev, featuredImage: imageUrl }));
  }, []);

  const handleFeaturedImageRemove = useCallback(() => {
    setFormData(prev => ({ ...prev, featuredImage: undefined }));
  }, []);

  const handleFocus = useCallback(() => console.log("Procurement Editor focused"), []);
  const handleBlur = useCallback(() => console.log("Procurement Editor blurred"), []);

  const handleCreateCancel = useCallback(() => {
    setIsCreateDialogOpen(false);
  }, []);

  const handleEditCancel = useCallback(() => {
    setIsEditDialogOpen(false);
    setEditingContent(null);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Manajemen Konten Pengadaan</h2>
          <p className="text-gray-600">Kelola panduan, prosedur, dan informasi pengadaan</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buat Konten
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-5xl max-h-[100vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Buat Konten Pengadaan Baru</DialogTitle>
            </DialogHeader>
            <ContentForm
              formData={formData}
              onTitleChange={handleTitleChange}
              onExcerptChange={handleExcerptChange}
              onContentChange={handleContentChange}
              onTypeChange={handleTypeChange}
              onStatusChange={handleStatusChange}
              onFeaturedImageChange={handleFeaturedImageChange}
              onFeaturedImageRemove={handleFeaturedImageRemove}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onCancel={handleCreateCancel}
              onSubmit={handleCreate}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Daftar Konten Pengadaan
          </CardTitle>
        </CardHeader>
        <CardContent>
          {contentLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : content.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Belum ada konten pengadaan. Buat konten pertama Anda!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Judul</TableHead>
                  <TableHead>Jenis</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Penulis</TableHead>
                  <TableHead>Tanggal</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {content.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.title}</div>
                        {item.excerpt && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {item.excerpt}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getTypeBadge(item.type)}
                    </TableCell>
                    <TableCell>{getStatusBadge(item.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="text-sm">{item.author.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="text-sm">
                          {format(new Date(item.createdAt), "dd MMM yyyy", { locale: id })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(`/procurement-info/${item.slug}`, "_blank")}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Konten Pengadaan</DialogTitle>
          </DialogHeader>
          <ContentForm
            isEdit
            formData={formData}
            onTitleChange={handleTitleChange}
            onExcerptChange={handleExcerptChange}
            onContentChange={handleContentChange}
            onTypeChange={handleTypeChange}
            onStatusChange={handleStatusChange}
            onFeaturedImageChange={handleFeaturedImageChange}
            onFeaturedImageRemove={handleFeaturedImageRemove}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onCancel={handleEditCancel}
            onSubmit={handleUpdate}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
