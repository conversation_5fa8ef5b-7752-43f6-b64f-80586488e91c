"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Edit, Trash2, Users, Save, X } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";

interface TeamMember {
  id: string;
  userId: string;
  role: "LEAD" | "MEMBER" | "EVALUATOR" | "SECRETARY";
  user: {
    id: string;
    name: string;
    email: string;
  };
}

interface ProcurementTeamTemplate {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  members: TeamMember[];
}

interface User {
  id: string;
  name: string;
  email: string;
  roles: string[];
}

async function fetchTeamTemplates(): Promise<ProcurementTeamTemplate[]> {
  const response = await fetch("/api/admin/procurement-team-templates");
  if (!response.ok) throw new Error("Failed to fetch team templates");
  const result = await response.json();
  return result.data;
}

async function fetchUsers(): Promise<User[]> {
  const response = await fetch("/api/admin/users?roles=PROCUREMENT_USER,COMMITTEE,APPROVER");
  if (!response.ok) throw new Error("Failed to fetch users");
  const result = await response.json();
  return result.data;
}

async function createTeamTemplate(data: any) {
  const response = await fetch("/api/admin/procurement-team-templates", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to create team template");
  }
  
  return response.json();
}

async function updateTeamTemplate(id: string, data: any) {
  const response = await fetch(`/api/admin/procurement-team-templates/${id}`, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to update team template");
  }
  
  return response.json();
}

async function deleteTeamTemplate(id: string) {
  const response = await fetch(`/api/admin/procurement-team-templates/${id}`, {
    method: "DELETE",
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to delete team template");
  }
  
  return response.json();
}

export function ProcurementTeamTemplates() {
  const queryClient = useQueryClient();
  const [showDialog, setShowDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ProcurementTeamTemplate | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    members: [] as Array<{ userId: string; role: string }>,
  });

  const { data: templates, isLoading: templatesLoading } = useQuery({
    queryKey: ["procurement-team-templates"],
    queryFn: fetchTeamTemplates,
  });

  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ["users"],
    queryFn: fetchUsers,
  });

  const createMutation = useMutation({
    mutationFn: createTeamTemplate,
    onSuccess: () => {
      toast.success("Team template created successfully");
      queryClient.invalidateQueries({ queryKey: ["procurement-team-templates"] });
      handleCloseDialog();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateTeamTemplate(id, data),
    onSuccess: () => {
      toast.success("Team template updated successfully");
      queryClient.invalidateQueries({ queryKey: ["procurement-team-templates"] });
      handleCloseDialog();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteTeamTemplate,
    onSuccess: () => {
      toast.success("Team template deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["procurement-team-templates"] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const handleOpenDialog = (template?: ProcurementTeamTemplate) => {
    if (template) {
      setEditingTemplate(template);
      setFormData({
        name: template.name,
        description: template.description || "",
        members: template.members.map(m => ({
          userId: m.userId,
          role: m.role,
        })),
      });
    } else {
      setEditingTemplate(null);
      setFormData({
        name: "",
        description: "",
        members: [],
      });
    }
    setShowDialog(true);
  };

  const handleCloseDialog = () => {
    setShowDialog(false);
    setEditingTemplate(null);
    setFormData({
      name: "",
      description: "",
      members: [],
    });
  };

  const handleSubmit = () => {
    if (!formData.name.trim()) {
      toast.error("Template name is required");
      return;
    }

    if (formData.members.length === 0) {
      toast.error("At least one team member is required");
      return;
    }

    // Validate that there's at least one LEAD
    const hasLead = formData.members.some(m => m.role === "LEAD");
    if (!hasLead) {
      toast.error("At least one team lead is required");
      return;
    }

    if (editingTemplate) {
      updateMutation.mutate({
        id: editingTemplate.id,
        data: formData,
      });
    } else {
      createMutation.mutate(formData);
    }
  };

  const addMember = () => {
    setFormData(prev => ({
      ...prev,
      members: [...prev.members, { userId: "", role: "MEMBER" }],
    }));
  };

  const removeMember = (index: number) => {
    setFormData(prev => ({
      ...prev,
      members: prev.members.filter((_, i) => i !== index),
    }));
  };

  const updateMember = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      members: prev.members.map((member, i) => 
        i === index ? { ...member, [field]: value } : member
      ),
    }));
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      LEAD: { variant: "default" as const, label: "Lead" },
      MEMBER: { variant: "secondary" as const, label: "Member" },
      EVALUATOR: { variant: "outline" as const, label: "Evaluator" },
      SECRETARY: { variant: "outline" as const, label: "Secretary" },
    };

    const config = roleConfig[role as keyof typeof roleConfig] || {
      variant: "outline" as const,
      label: role,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (templatesLoading || usersLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Procurement Team Templates
            </CardTitle>
            <Button onClick={() => handleOpenDialog()}>
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Template Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Team Members</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates?.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No team templates found
                    </TableCell>
                  </TableRow>
                ) : (
                  templates?.map((template) => (
                    <TableRow key={template.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{template.name}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm text-gray-600">
                          {template.description || "No description"}
                        </p>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {template.members.map((member) => (
                            <div key={member.id} className="flex items-center gap-1 text-xs">
                              <span>{member.user.name}</span>
                              {getRoleBadge(member.role)}
                            </div>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={template.isActive ? "default" : "secondary"}>
                          {template.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {new Date(template.createdAt).toLocaleDateString('id-ID')}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenDialog(template)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              if (confirm("Are you sure you want to delete this template?")) {
                                deleteMutation.mutate(template.id);
                              }
                            }}
                            disabled={deleteMutation.isPending}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={showDialog} onOpenChange={handleCloseDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate ? "Edit" : "Create"} Team Template
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Template Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                  setFormData(prev => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter template name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => 
                  setFormData(prev => ({ ...prev, description: e.target.value }))
                }
                placeholder="Enter template description"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Team Members *</Label>
                <Button type="button" variant="outline" size="sm" onClick={addMember}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Member
                </Button>
              </div>
              
              <div className="space-y-2">
                {formData.members.map((member, index) => (
                  <div key={index} className="flex gap-2 items-center">
                    <Select
                      value={member.userId}
                      onValueChange={(value: string) => updateMember(index, "userId", value)}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select user" />
                      </SelectTrigger>
                      <SelectContent>
                        {users?.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Select
                      value={member.role}
                      onValueChange={(value: string) => updateMember(index, "role", value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="LEAD">Lead</SelectItem>
                        <SelectItem value="MEMBER">Member</SelectItem>
                        <SelectItem value="EVALUATOR">Evaluator</SelectItem>
                        <SelectItem value="SECRETARY">Secretary</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeMember(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCloseDialog}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              <Save className="h-4 w-4 mr-2" />
              {createMutation.isPending || updateMutation.isPending 
                ? "Saving..." 
                : editingTemplate 
                  ? "Update Template" 
                  : "Create Template"
              }
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
