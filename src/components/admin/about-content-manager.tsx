"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Save, Edit, Eye, Building2, Award, CheckCircle, MapPin, Phone, Mail, Plus, Trash2 } from "lucide-react";
import { useState, useCallback } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface AboutPageContent {
  id: string;
  // Hero Section
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;

  // About E-Procurement Section
  aboutTitle: string;
  aboutDescription: string;

  // Company Profile Section
  companyTitle: string;
  companyDescription: string;
  companyName: string;
  companyFullName: string;
  operationalArea: string;
  vision: string;

  // E-Procurement Features
  features: string[];

  // Contact Information
  contactTitle: string;
  contactDescription: string;
  address: string;
  phones: string[];
  emails: string[];

  // CTA Section
  ctaTitle: string;
  ctaDescription: string;

  updatedAt: string;
  updatedBy: {
    id: string;
    name: string;
  };
}

// Fetch about page content
async function fetchAboutContent(): Promise<AboutPageContent> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/about-page-content", { headers });
  if (!response.ok) {
    throw new Error("Failed to fetch about content");
  }
  return response.json();
}

// Update about page content
async function updateAboutContent(data: Partial<AboutPageContent>): Promise<AboutPageContent> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/about-page-content", {
    method: "PUT",
    headers,
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error("Failed to update about content");
  }
  return response.json();
}

export function AboutContentManager() {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<Partial<AboutPageContent>>({});
  const queryClient = useQueryClient();

  const { data: content, isLoading, error } = useQuery({
    queryKey: ["about-content"],
    queryFn: fetchAboutContent,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const updateMutation = useMutation({
    mutationFn: updateAboutContent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["about-content"] });
      setIsEditing(false);
      setEditData({});
      toast.success("Konten berhasil diperbarui");
    },
    onError: (error) => {
      toast.error("Gagal memperbarui konten: " + error.message);
    },
  });

  const handleEdit = useCallback(() => {
    if (content) {
      setEditData(content);
      setIsEditing(true);
    }
  }, [content]);

  const handleSave = useCallback(() => {
    updateMutation.mutate(editData);
  }, [editData, updateMutation]);

  const handleCancel = useCallback(() => {
    setIsEditing(false);
    setEditData({});
  }, []);

  const updateField = useCallback((field: keyof AboutPageContent, value: any) => {
    setEditData(prev => ({ ...prev, [field]: value }));
  }, []);

  const addArrayItem = useCallback((field: 'features' | 'phones' | 'emails', value: string) => {
    if (!value.trim()) return;
    setEditData(prev => ({
      ...prev,
      [field]: [...(prev[field] || []), value.trim()]
    }));
  }, []);

  const removeArrayItem = useCallback((field: 'features' | 'phones' | 'emails', index: number) => {
    setEditData(prev => ({
      ...prev,
      [field]: (prev[field] || []).filter((_, i) => i !== index)
    }));
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat konten...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">Gagal memuat konten halaman tentang</p>
        <Button onClick={() => window.location.reload()}>Coba Lagi</Button>
      </div>
    );
  }

  const currentData = isEditing ? editData : content;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Kelola Halaman Tentang</h1>
          <p className="text-gray-600">Kelola konten halaman tentang perusahaan dan e-procurement</p>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                Batal
              </Button>
              <Button
                onClick={handleSave}
                disabled={updateMutation.isPending}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {updateMutation.isPending ? "Menyimpan..." : "Simpan"}
              </Button>
            </>
          ) : (
            <Button onClick={handleEdit} className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Edit Konten
            </Button>
          )}
        </div>
      </div>

      {/* Content Tabs */}
      <Tabs defaultValue="hero" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="hero">Hero</TabsTrigger>
          <TabsTrigger value="about">Tentang E-Proc</TabsTrigger>
          <TabsTrigger value="company">Profil Perusahaan</TabsTrigger>
          <TabsTrigger value="contact">Kontak</TabsTrigger>
          <TabsTrigger value="cta">Call to Action</TabsTrigger>
        </TabsList>

        {/* Hero Section */}
        <TabsContent value="hero">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Hero Section
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="heroTitle">Judul Utama</Label>
                <Input
                  id="heroTitle"
                  value={currentData?.heroTitle || ""}
                  onChange={(e) => updateField("heroTitle", e.target.value)}
                  disabled={!isEditing}
                  placeholder="Tentang E-Procurement"
                />
              </div>
              <div>
                <Label htmlFor="heroSubtitle">Sub Judul</Label>
                <Input
                  id="heroSubtitle"
                  value={currentData?.heroSubtitle || ""}
                  onChange={(e) => updateField("heroSubtitle", e.target.value)}
                  disabled={!isEditing}
                  placeholder="PT Bank BPD Sulteng"
                />
              </div>
              <div>
                <Label htmlFor="heroDescription">Deskripsi</Label>
                <Textarea
                  id="heroDescription"
                  value={currentData?.heroDescription || ""}
                  onChange={(e) => updateField("heroDescription", e.target.value)}
                  disabled={!isEditing}
                  rows={3}
                  placeholder="Sistem pengadaan elektronik yang mendukung transparansi..."
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* About E-Procurement Section */}
        <TabsContent value="about">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Tentang E-Procurement
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="aboutTitle">Judul Section</Label>
                <Input
                  id="aboutTitle"
                  value={currentData?.aboutTitle || ""}
                  onChange={(e) => updateField("aboutTitle", e.target.value)}
                  disabled={!isEditing}
                  placeholder="Apa itu E-Procurement?"
                />
              </div>
              <div>
                <Label htmlFor="aboutDescription">Deskripsi</Label>
                <Textarea
                  id="aboutDescription"
                  value={currentData?.aboutDescription || ""}
                  onChange={(e) => updateField("aboutDescription", e.target.value)}
                  disabled={!isEditing}
                  rows={3}
                  placeholder="E-Procurement adalah sistem pengadaan elektronik..."
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Company Profile Section */}
        <TabsContent value="company">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Company Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Informasi Perusahaan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="companyTitle">Judul Section</Label>
                  <Input
                    id="companyTitle"
                    value={currentData?.companyTitle || ""}
                    onChange={(e) => updateField("companyTitle", e.target.value)}
                    disabled={!isEditing}
                    placeholder="Tentang PT Bank BPD Sulteng"
                  />
                </div>
                <div>
                  <Label htmlFor="companyDescription">Deskripsi</Label>
                  <Textarea
                    id="companyDescription"
                    value={currentData?.companyDescription || ""}
                    onChange={(e) => updateField("companyDescription", e.target.value)}
                    disabled={!isEditing}
                    rows={2}
                    placeholder="Bank Pembangunan Daerah Sulawesi Tengah..."
                  />
                </div>
                <div>
                  <Label htmlFor="companyName">Nama Perusahaan</Label>
                  <Input
                    id="companyName"
                    value={currentData?.companyName || ""}
                    onChange={(e) => updateField("companyName", e.target.value)}
                    disabled={!isEditing}
                    placeholder="PT Bank BPD Sulteng"
                  />
                </div>
                <div>
                  <Label htmlFor="companyFullName">Nama Lengkap</Label>
                  <Input
                    id="companyFullName"
                    value={currentData?.companyFullName || ""}
                    onChange={(e) => updateField("companyFullName", e.target.value)}
                    disabled={!isEditing}
                    placeholder="Bank Pembangunan Daerah Sulawesi Tengah"
                  />
                </div>
                <div>
                  <Label htmlFor="operationalArea">Wilayah Operasional</Label>
                  <Input
                    id="operationalArea"
                    value={currentData?.operationalArea || ""}
                    onChange={(e) => updateField("operationalArea", e.target.value)}
                    disabled={!isEditing}
                    placeholder="Sulawesi Tengah dan sekitarnya"
                  />
                </div>
                <div>
                  <Label htmlFor="vision">Visi</Label>
                  <Textarea
                    id="vision"
                    value={currentData?.vision || ""}
                    onChange={(e) => updateField("vision", e.target.value)}
                    disabled={!isEditing}
                    rows={3}
                    placeholder="Menjadi bank pilihan utama yang mendukung pembangunan ekonomi daerah"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Keunggulan E-Procurement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(currentData?.features || []).map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <span className="flex-1">{feature}</span>
                      {isEditing && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeArrayItem("features", index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Tambah keunggulan baru..."
                        onKeyPress={(e) => {
                          if (e.key === "Enter") {
                            addArrayItem("features", e.currentTarget.value);
                            e.currentTarget.value = "";
                          }
                        }}
                      />
                      <Button
                        size="sm"
                        onClick={(e) => {
                          const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                          addArrayItem("features", input.value);
                          input.value = "";
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Contact Section */}
        <TabsContent value="contact">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Informasi Kontak
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contactTitle">Judul Section</Label>
                  <Input
                    id="contactTitle"
                    value={currentData?.contactTitle || ""}
                    onChange={(e) => updateField("contactTitle", e.target.value)}
                    disabled={!isEditing}
                    placeholder="Informasi Kontak"
                  />
                </div>
                <div>
                  <Label htmlFor="contactDescription">Deskripsi</Label>
                  <Input
                    id="contactDescription"
                    value={currentData?.contactDescription || ""}
                    onChange={(e) => updateField("contactDescription", e.target.value)}
                    disabled={!isEditing}
                    placeholder="Hubungi kami untuk informasi lebih lanjut..."
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="address">Alamat</Label>
                <Textarea
                  id="address"
                  value={currentData?.address || ""}
                  onChange={(e) => updateField("address", e.target.value)}
                  disabled={!isEditing}
                  rows={3}
                  placeholder="Jl. Contoh No. 123, Palu, Sulawesi Tengah 94111"
                />
              </div>

              {/* Phone Numbers */}
              <div>
                <Label>Nomor Telepon</Label>
                <div className="space-y-2 mt-2">
                  {(currentData?.phones || []).map((phone, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      <span className="flex-1">{phone}</span>
                      {isEditing && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeArrayItem("phones", index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Tambah nomor telepon..."
                        onKeyPress={(e) => {
                          if (e.key === "Enter") {
                            addArrayItem("phones", e.currentTarget.value);
                            e.currentTarget.value = "";
                          }
                        }}
                      />
                      <Button
                        size="sm"
                        onClick={(e) => {
                          const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                          addArrayItem("phones", input.value);
                          input.value = "";
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Email Addresses */}
              <div>
                <Label>Email</Label>
                <div className="space-y-2 mt-2">
                  {(currentData?.emails || []).map((email, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      <span className="flex-1">{email}</span>
                      {isEditing && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeArrayItem("emails", index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Tambah email..."
                        type="email"
                        onKeyPress={(e) => {
                          if (e.key === "Enter") {
                            addArrayItem("emails", e.currentTarget.value);
                            e.currentTarget.value = "";
                          }
                        }}
                      />
                      <Button
                        size="sm"
                        onClick={(e) => {
                          const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                          addArrayItem("emails", input.value);
                          input.value = "";
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* CTA Section */}
        <TabsContent value="cta">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Call to Action
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="ctaTitle">Judul CTA</Label>
                <Input
                  id="ctaTitle"
                  value={currentData?.ctaTitle || ""}
                  onChange={(e) => updateField("ctaTitle", e.target.value)}
                  disabled={!isEditing}
                  placeholder="Siap Bermitra dengan Kami?"
                />
              </div>
              <div>
                <Label htmlFor="ctaDescription">Deskripsi CTA</Label>
                <Textarea
                  id="ctaDescription"
                  value={currentData?.ctaDescription || ""}
                  onChange={(e) => updateField("ctaDescription", e.target.value)}
                  disabled={!isEditing}
                  rows={2}
                  placeholder="Bergabunglah dengan vendor-vendor terpercaya lainnya..."
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Preview Button */}
      <div className="flex justify-center pt-6">
        <Button variant="outline" asChild>
          <a href="/about" target="_blank" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Preview Halaman
          </a>
        </Button>
      </div>
    </div>
  );
}
