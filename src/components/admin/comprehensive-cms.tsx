"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Plus, Edit, Trash2, Eye, Calendar, User, Tag, FileText, Image, Download, Settings } from "lucide-react";
import { useState, useCallback } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";


// Content Types for E-Procurement System
const CONTENT_TYPES = {
  NEWS: "news",
  ANNOUNCEMENT: "announcement",
  BANNER: "banner",
  LOGO: "logo",
  DOCUMENT: "document",
  PAGE: "page",
  HERO_SECTION: "hero_section",
  FOOTER_CONTENT: "footer_content",
} as const;

const CONTENT_TYPE_LABELS = {
  [CONTENT_TYPES.NEWS]: "Berita",
  [CONTENT_TYPES.ANNOUNCEMENT]: "Pengumuman",
  [CONTENT_TYPES.BANNER]: "Banner",
  [CONTENT_TYPES.LOGO]: "Logo",
  [CONTENT_TYPES.DOCUMENT]: "Dokumen",
  [CONTENT_TYPES.PAGE]: "Halaman",
  [CONTENT_TYPES.HERO_SECTION]: "Hero Section",
  [CONTENT_TYPES.FOOTER_CONTENT]: "Footer",
};

interface ContentItem {
  id: string;
  type: string;
  title: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  imageUrl?: string;
  fileUrl?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
  author: {
    id: string;
    name: string;
    email: string;
  };
}

interface ContentFormData {
  type: string;
  title: string;
  content?: string;
  excerpt?: string;
  imageUrl?: string;
  fileUrl?: string;
  status: "DRAFT" | "PUBLISHED";
  metadata?: Record<string, any>;
}

async function fetchContent(type?: string): Promise<ContentItem[]> {
  const params = new URLSearchParams();
  if (type) params.append("type", type);

  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/content?${params}`, { headers });
  if (!response.ok) throw new Error("Failed to fetch content");
  return response.json();
}

async function createContent(data: ContentFormData): Promise<ContentItem> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/content", {
    method: "POST",
    headers,
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error("Failed to create content");
  return response.json();
}

async function updateContent(id: string, data: Partial<ContentFormData>): Promise<ContentItem> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/content/${id}`, {
    method: "PUT",
    headers,
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error("Failed to update content");
  return response.json();
}

async function deleteContent(id: string): Promise<void> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/content/${id}`, {
    method: "DELETE",
    headers,
  });
  if (!response.ok) throw new Error("Failed to delete content");
}

function getStatusBadge(status: string) {
  const statusConfig = {
    DRAFT: { label: "Draft", variant: "secondary" as const },
    PUBLISHED: { label: "Published", variant: "default" as const },
    ARCHIVED: { label: "Archived", variant: "secondary" as const },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

function getContentIcon(type: string) {
  const icons = {
    [CONTENT_TYPES.NEWS]: FileText,
    [CONTENT_TYPES.ANNOUNCEMENT]: Tag,
    [CONTENT_TYPES.BANNER]: Image,
    [CONTENT_TYPES.LOGO]: Image,
    [CONTENT_TYPES.DOCUMENT]: Download,
    [CONTENT_TYPES.PAGE]: FileText,
    [CONTENT_TYPES.HERO_SECTION]: Settings,
    [CONTENT_TYPES.FOOTER_CONTENT]: Settings,
  };

  const Icon = icons[type as keyof typeof icons] || FileText;
  return <Icon className="h-4 w-4" />;
}

export function ComprehensiveCMS() {
  const [selectedType, setSelectedType] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingContent, setEditingContent] = useState<ContentItem | null>(null);
  const [formData, setFormData] = useState<ContentFormData>({
    type: CONTENT_TYPES.NEWS,
    title: "",
    content: "",
    excerpt: "",
    status: "DRAFT",
  });

  const queryClient = useQueryClient();

  const { data: content = [], isLoading } = useQuery({
    queryKey: ["admin-content", selectedType],
    queryFn: () => fetchContent(selectedType === "all" ? undefined : selectedType),
  });

  const createMutation = useMutation({
    mutationFn: createContent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-content"] });
      setIsCreateDialogOpen(false);
      resetForm();
      toast.success("Konten berhasil dibuat");
    },
    onError: () => {
      toast.error("Gagal membuat konten");
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ContentFormData> }) =>
      updateContent(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-content"] });
      setIsEditDialogOpen(false);
      setEditingContent(null);
      toast.success("Konten berhasil diperbarui");
    },
    onError: () => {
      toast.error("Gagal memperbarui konten");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteContent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-content"] });
      toast.success("Konten berhasil dihapus");
    },
    onError: () => {
      toast.error("Gagal menghapus konten");
    },
  });

  const resetForm = () => {
    setFormData({
      type: CONTENT_TYPES.NEWS,
      title: "",
      content: "",
      excerpt: "",
      status: "DRAFT",
    });
  };

  const handleCreate = () => {
    createMutation.mutate(formData);
  };

  const handleEdit = (item: ContentItem) => {
    setEditingContent(item);
    // Map item status to form status (only allow editable statuses)
    const editableStatus = ["DRAFT", "PUBLISHED"].includes(item.status)
      ? item.status as "DRAFT" | "PUBLISHED"
      : "DRAFT";

    setFormData({
      type: item.type,
      title: item.title,
      content: item.content || "",
      excerpt: item.excerpt || "",
      imageUrl: item.imageUrl || "",
      fileUrl: item.fileUrl || "",
      status: editableStatus,
      metadata: item.metadata,
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdate = () => {
    if (!editingContent) return;
    updateMutation.mutate({ id: editingContent.id, data: formData });
  };

  const handleDelete = (id: string) => {
    if (confirm("Apakah Anda yakin ingin menghapus konten ini?")) {
      deleteMutation.mutate(id);
    }
  };

  const ContentForm = ({ isEdit = false }: { isEdit?: boolean }) => (
    <div className="space-y-4 max-h-[70vh] overflow-y-auto">
      <div>
        <Label htmlFor="type">Jenis Konten *</Label>
        <Select
          value={formData.type}
          onValueChange={(value) => setFormData({ ...formData, type: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih jenis konten" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(CONTENT_TYPE_LABELS).map(([value, label]) => (
              <SelectItem key={value} value={value}>
                <div className="flex items-center gap-2">
                  {getContentIcon(value)}
                  {label}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="title">Judul *</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          placeholder="Masukkan judul konten"
        />
      </div>

      {(formData.type === CONTENT_TYPES.NEWS || formData.type === CONTENT_TYPES.ANNOUNCEMENT || formData.type === CONTENT_TYPES.PAGE) && (
        <div>
          <Label htmlFor="excerpt">Ringkasan</Label>
          <Textarea
            id="excerpt"
            value={formData.excerpt}
            onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
            placeholder="Ringkasan singkat konten"
            rows={3}
          />
        </div>
      )}

      {(formData.type === CONTENT_TYPES.NEWS || formData.type === CONTENT_TYPES.ANNOUNCEMENT || formData.type === CONTENT_TYPES.PAGE || formData.type === CONTENT_TYPES.HERO_SECTION) && (
        <div>
          <Label htmlFor="content">Konten *</Label>
          <RichTextEditor
            value={formData.content || ""}
            onChange={useCallback((data: string) => {
              console.log("🔍 DEBUG RICH EDITOR: Content updated in CMS");
              setFormData(prev => ({ ...prev, content: data }));
            }, [])}
            placeholder="Tulis konten lengkap di sini. Anda dapat menambahkan gambar, tabel, dan format teks yang kaya."
            className="min-h-96"
            onFocus={useCallback(() => console.log("🔍 DEBUG RICH EDITOR: CMS Editor focused"), [])}
            onBlur={useCallback(() => console.log("🔍 DEBUG RICH EDITOR: CMS Editor blurred"), [])}
          />
          <div className="mt-2 text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <div>✨ Rich text editor dengan dukungan:</div>
              <div>📷 Upload gambar</div>
              <div>📄 Upload dokumen</div>
              <div>📊 Tabel</div>
              <div>🎨 Format teks</div>
            </div>
          </div>
        </div>
      )}

      {(formData.type === CONTENT_TYPES.BANNER || formData.type === CONTENT_TYPES.LOGO) && (
        <div>
          <Label htmlFor="imageUrl">URL Gambar *</Label>
          <Input
            id="imageUrl"
            value={formData.imageUrl}
            onChange={(e) => setFormData({ ...formData, imageUrl: e.target.value })}
            placeholder="https://example.com/image.jpg"
          />
        </div>
      )}

      {formData.type === CONTENT_TYPES.DOCUMENT && (
        <div>
          <Label htmlFor="fileUrl">URL File *</Label>
          <Input
            id="fileUrl"
            value={formData.fileUrl}
            onChange={(e) => setFormData({ ...formData, fileUrl: e.target.value })}
            placeholder="https://example.com/document.pdf"
          />
        </div>
      )}

      <div>
        <Label htmlFor="status">Status *</Label>
        <Select
          value={formData.status}
          onValueChange={(value: "DRAFT" | "PUBLISHED") =>
            setFormData({ ...formData, status: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="DRAFT">Draft</SelectItem>
            <SelectItem value="PUBLISHED">Published</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          onClick={() => {
            if (isEdit) {
              setIsEditDialogOpen(false);
              setEditingContent(null);
            } else {
              setIsCreateDialogOpen(false);
            }
            resetForm();
          }}
        >
          Batal
        </Button>
        <Button
          onClick={isEdit ? handleUpdate : handleCreate}
          disabled={!formData.title || (formData.type === CONTENT_TYPES.NEWS && !formData.content)}
        >
          {isEdit ? "Perbarui" : "Buat"} Konten
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Manajemen Konten E-Procurement</h2>
          <p className="text-gray-600">Kelola semua konten untuk sistem e-procurement</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buat Konten
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Buat Konten Baru</DialogTitle>
            </DialogHeader>
            <ContentForm />
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex gap-4 items-center">
        <Label>Filter Jenis:</Label>
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Semua jenis" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua Jenis</SelectItem>
            {Object.entries(CONTENT_TYPE_LABELS).map(([value, label]) => (
              <SelectItem key={value} value={value}>
                <div className="flex items-center gap-2">
                  {getContentIcon(value)}
                  {label}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Daftar Konten
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : content.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Belum ada konten. Buat konten pertama Anda!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Jenis</TableHead>
                  <TableHead>Judul</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Penulis</TableHead>
                  <TableHead>Tanggal</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {content.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getContentIcon(item.type)}
                        <Badge variant="outline">
                          {CONTENT_TYPE_LABELS[item.type as keyof typeof CONTENT_TYPE_LABELS]}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.title}</div>
                        {item.excerpt && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {item.excerpt}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(item.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="text-sm">{item.author.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="text-sm">
                          {format(new Date(item.createdAt), "dd MMM yyyy", { locale: id })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {item.slug && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(`/${item.type}/${item.slug}`, "_blank")}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Konten</DialogTitle>
          </DialogHeader>
          <ContentForm isEdit />
        </DialogContent>
      </Dialog>
    </div>
  );
}