"use client";

import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Check, X, FileText, AlertCircle, CheckCircle } from "lucide-react";
import { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

interface Vendor {
   id: string;
   companyName: string;
   npwpNumber: string;
   address: string;
   picName: string;
   picEmail: string;
   picPhone: string;
   verificationStatus: string;
   user: {
      id: string;
      email: string;
      name: string;
   };
   documents: Array<{
      id: string;
      fileName: string;
      fileUrl: string;
      fileType: string;
      description?: string;
   }>;
}

interface VerificationStep {
   id: string;
   stepName: string;
   status: "PENDING" | "APPROVED" | "REJECTED";
   verifiedAt?: string;
   verifiedBy?: {
      id: string;
      name: string;
   };
   comments?: string;
}

interface VendorVerificationModalProps {
   vendor: Vendor;
   onClose: () => void;
   onVerificationComplete: () => void;
}

const verificationSteps = [
   {
      id: "ADMINISTRASI",
      name: "Administrasi",
      description: "Verifikasi dokumen administrasi perusahaan",
      requiredDocs: ["Akta Pendirian", "SIUP", "TDP"],
   },
   {
      id: "PAJAK",
      name: "Pajak",
      description: "Verifikasi dokumen perpajakan",
      requiredDocs: ["NPWP", "SKT", "Laporan Pajak"],
   },
   {
      id: "TEKNIS",
      name: "Teknis",
      description: "Verifikasi kemampuan teknis perusahaan",
      requiredDocs: ["Sertifikat ISO", "Portfolio", "Referensi"],
   },
   {
      id: "KEUANGAN",
      name: "Keuangan",
      description: "Verifikasi kondisi keuangan perusahaan",
      requiredDocs: ["Laporan Keuangan", "Bank Statement", "Surat Keterangan Bank"],
   },
   {
      id: "LEGALITAS",
      name: "Legalitas",
      description: "Verifikasi legalitas dan compliance perusahaan",
      requiredDocs: ["Domisili", "NIB", "Sertifikat Halal (jika diperlukan)"],
   },
];

async function fetchVerificationSteps(vendorId: string): Promise<VerificationStep[]> {
   const response = await fetch(`/api/admin/vendors/${vendorId}/verification-steps`);
   if (!response.ok) throw new Error("Failed to fetch verification steps");
   const result = await response.json();
   return result.data || [];
}

async function verifyStep(vendorId: string, stepName: string, status: "APPROVED" | "REJECTED", comments?: string) {
   const response = await fetch(`/api/admin/vendors/${vendorId}/verify-step`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ stepName, status, comments }),
   });

   if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to verify step");
   }

   return response.json();
}

export function VendorVerificationModal({ vendor, onClose, onVerificationComplete }: VendorVerificationModalProps) {
   const [activeTab, setActiveTab] = useState("ADMINISTRASI");
   const [comments, setComments] = useState<Record<string, string>>({});
   const { toast } = useToast();
   const queryClient = useQueryClient();

   const { data: verificationData, isLoading } = useQuery({
      queryKey: ["verification-steps", vendor.id],
      queryFn: () => fetchVerificationSteps(vendor.id),
   });

   const verifyMutation = useMutation({
      mutationFn: ({ stepName, status, comments }: { stepName: string; status: "APPROVED" | "REJECTED"; comments?: string }) =>
         verifyStep(vendor.id, stepName, status, comments),
      onSuccess: (data) => {
         toast({
            title: "Verification Updated",
            description: `Step ${data.data.stepName} has been ${data.data.status.toLowerCase()}`,
         });
         queryClient.invalidateQueries({ queryKey: ["verification-steps", vendor.id] });

         if (data.data.statusChanged) {
            onVerificationComplete();
         }
      },
      onError: (error: Error) => {
         toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
         });
      },
   });

   const handleStepVerification = (stepName: string, status: "APPROVED" | "REJECTED") => {
      const stepComments = comments[stepName]?.trim();

      if (status === "REJECTED" && !stepComments) {
         toast({
            title: "Error",
            description: "Comments are required when rejecting a step",
            variant: "destructive",
         });
         return;
      }

      verifyMutation.mutate({ stepName, status, comments: stepComments });
   };

   const getStepStatus = (stepName: string) => {
      return verificationData?.find(step => step.stepName === stepName);
   };

   const getStepBadge = (status?: string) => {
      switch (status) {
         case "APPROVED":
            return <Badge variant="default" className="bg-green-100 text-green-800">Approved</Badge>;
         case "REJECTED":
            return <Badge variant="destructive">Rejected</Badge>;
         default:
            return <Badge variant="secondary">Pending</Badge>;
      }
   };

   const openDocumentInNewTab = (url: string) => {
      window.open(url, "_blank");
   };

   if (isLoading) {
      return (
         <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
               <div className="flex items-center justify-center p-8">
                  Loading verification data...
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   return (
      <Dialog open={true} onOpenChange={onClose}>
         <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
               <DialogTitle>Vendor Verification - {vendor.companyName}</DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
               {/* Vendor Overview */}
               <Card>
                  <CardHeader>
                     <CardTitle className="text-lg">Vendor Overview</CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                           <Label className="font-medium">Company Name</Label>
                           <p>{vendor.companyName}</p>
                        </div>
                        <div>
                           <Label className="font-medium">NPWP</Label>
                           <p className="font-mono">{vendor.npwpNumber}</p>
                        </div>
                        <div>
                           <Label className="font-medium">PIC</Label>
                           <p>{vendor.picName} ({vendor.picEmail})</p>
                        </div>
                     </div>
                  </CardContent>
               </Card>

               {/* Verification Steps Tabs */}
               <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-5">
                     {verificationSteps.map((step) => (
                        <TabsTrigger key={step.id} value={step.id} className="relative">
                           {step.name}
                           {getStepStatus(step.id)?.status === "APPROVED" && (
                              <CheckCircle className="absolute -top-1 -right-1 h-4 w-4 text-green-500" />
                           )}
                           {getStepStatus(step.id)?.status === "REJECTED" && (
                              <AlertCircle className="absolute -top-1 -right-1 h-4 w-4 text-red-500" />
                           )}
                        </TabsTrigger>
                     ))}
                  </TabsList>

                  {verificationSteps.map((step) => {
                     const stepStatus = getStepStatus(step.id);

                     return (
                        <TabsContent key={step.id} value={step.id} className="space-y-4">
                           <Card>
                              <CardHeader>
                                 <div className="flex items-center justify-between">
                                    <div>
                                       <CardTitle>{step.name}</CardTitle>
                                       <p className="text-sm text-muted-foreground">{step.description}</p>
                                    </div>
                                    {getStepBadge(stepStatus?.status)}
                                 </div>
                              </CardHeader>

                              <CardContent className="space-y-4">
                                 {/* Required Documents */}
                                 <div>
                                    <Label className="font-medium">Required Documents:</Label>
                                    <div className="mt-2 grid grid-cols-1 gap-2">
                                       {step.requiredDocs.map((doc) => (
                                          <div key={doc} className="flex items-center gap-2 text-sm">
                                             <div className="h-2 w-2 rounded-full bg-gray-400" />
                                             {doc}
                                          </div>
                                       ))}
                                    </div>
                                 </div>

                                 {/* Uploaded Documents */}
                                 <div>
                                    <Label className="font-medium">Uploaded Documents:</Label>
                                    <div className="mt-2 space-y-2">
                                       {vendor.documents.map((doc) => (
                                          <div key={doc.id} className="flex items-center justify-between p-3 border rounded">
                                             <div className="flex items-center gap-3">
                                                <FileText className="h-4 w-4 text-gray-500" />
                                                <div>
                                                   <p className="text-sm font-medium">{doc.fileName}</p>
                                                   <p className="text-xs text-gray-600">{doc.description || doc.fileType}</p>
                                                </div>
                                             </div>
                                             <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => openDocumentInNewTab(doc.fileUrl)}
                                             >
                                                View
                                             </Button>
                                          </div>
                                       ))}
                                    </div>
                                 </div>

                                 {/* Previous Verification Info */}
                                 {stepStatus && (
                                    <Alert>
                                       <AlertCircle className="h-4 w-4" />
                                       <AlertDescription>
                                          <div className="space-y-1">
                                             <p><strong>Status:</strong> {stepStatus.status}</p>
                                             {stepStatus.verifiedAt && (
                                                <p><strong>Verified at:</strong> {new Date(stepStatus.verifiedAt).toLocaleString()}</p>
                                             )}
                                             {stepStatus.verifiedBy && (
                                                <p><strong>Verified by:</strong> {stepStatus.verifiedBy.name}</p>
                                             )}
                                             {stepStatus.comments && (
                                                <p><strong>Comments:</strong> {stepStatus.comments}</p>
                                             )}
                                          </div>
                                       </AlertDescription>
                                    </Alert>
                                 )}

                                 {/* Verification Comments */}
                                 <div>
                                    <Label htmlFor={`comments-${step.id}`}>Comments</Label>
                                    <Textarea
                                       id={`comments-${step.id}`}
                                       placeholder="Add your verification comments..."
                                       value={comments[step.id] || ""}
                                       onChange={(e) => setComments(prev => ({ ...prev, [step.id]: e.target.value }))}
                                       className="mt-1"
                                    />
                                 </div>

                                 {/* Verification Actions */}
                                 {vendor.verificationStatus === "PENDING_VERIFICATION" && (
                                    <div className="flex gap-2 pt-4 border-t">
                                       <Button
                                          variant="default"
                                          onClick={() => handleStepVerification(step.id, "APPROVED")}
                                          disabled={verifyMutation.isPending}
                                          className="bg-green-600 hover:bg-green-700"
                                       >
                                          <Check className="h-4 w-4 mr-2" />
                                          Approve Step
                                       </Button>
                                       <Button
                                          variant="destructive"
                                          onClick={() => handleStepVerification(step.id, "REJECTED")}
                                          disabled={verifyMutation.isPending}
                                       >
                                          <X className="h-4 w-4 mr-2" />
                                          Reject Step
                                       </Button>
                                    </div>
                                 )}
                              </CardContent>
                           </Card>
                        </TabsContent>
                     );
                  })}
               </Tabs>
            </div>

            <DialogFooter>
               <Button variant="outline" onClick={onClose}>
                  Close
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}