"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Eye, Check, X, Clock, CheckCircle, XCircle } from "lucide-react";
import { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface BAST {
  id: string;
  bastNumber: string;
  handoverDate: string;
  location: string;
  status: "PENDING_APPROVAL" | "APPROVED" | "REJECTED";
  qualityChecklist: Array<{
    item: string;
    status: "PASS" | "FAIL" | "N/A";
    notes?: string;
  }>;
  notes?: string;
  submittedAt: string;
  purchaseOrder: {
    id: string;
    poNumber: string;
    vendor: {
      id: string;
      companyName: string;
    };
  };
  items: Array<{
    id: string;
    deliveredQuantity: number;
    condition: "GOOD" | "DAMAGED" | "INCOMPLETE";
    notes?: string;
    purchaseOrderItem: {
      id: string;
      quantity: number;
      price: number;
      item: {
        id: string;
        name: string;
        unit: string;
      };
    };
  }>;
  submittedBy: {
    id: string;
    name: string;
  };
  approvals: Array<{
    id: string;
    sequence: number;
    status: string;
    comments?: string;
    processedAt?: string;
    approver: {
      id: string;
      name: string;
    };
  }>;
}

interface BASTsResponse {
  basts: BAST[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

async function fetchBASTs(status: string, page = 1): Promise<BASTsResponse> {
  const params = new URLSearchParams();
  params.append("status", status);
  params.append("page", page.toString());

  const response = await fetch(`/api/bast?${params}`);
  if (!response.ok) {
    throw new Error("Failed to fetch BASTs");
  }
  const result = await response.json();
  return result.data;
}

async function approveBAST(bastId: string, comments?: string) {
  const response = await fetch(`/api/bast/${bastId}/approve`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ comments }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to approve BAST");
  }

  return response.json();
}

async function rejectBAST(bastId: string, comments: string) {
  const response = await fetch(`/api/bast/${bastId}/reject`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ comments }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to reject BAST");
  }

  return response.json();
}

export function BASTApprovalDashboard() {
  const [activeTab, setActiveTab] = useState("PENDING_APPROVAL");
  const [selectedBAST, setSelectedBAST] = useState<BAST | null>(null);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [approvalAction, setApprovalAction] = useState<"APPROVE" | "REJECT">("APPROVE");
  const [comments, setComments] = useState("");
  const [error, setError] = useState<string | null>(null);

  const queryClient = useQueryClient();

  const { data, isLoading, error: fetchError } = useQuery({
    queryKey: ["bast", activeTab],
    queryFn: () => fetchBASTs(activeTab),
    staleTime: 30000,
  });

  const approvalMutation = useMutation({
    mutationFn: ({ bastId, action, comments }: {
      bastId: string;
      action: "APPROVE" | "REJECT";
      comments?: string;
    }) => {
      if (action === "APPROVE") {
        return approveBAST(bastId, comments);
      } else {
        return rejectBAST(bastId, comments!);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bast"] });
      setSelectedBAST(null);
      setShowApprovalDialog(false);
      setComments("");
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const handleApprovalAction = () => {
    if (!selectedBAST) return;

    if (approvalAction === "REJECT" && !comments.trim()) {
      setError("Komentar wajib diisi untuk penolakan");
      return;
    }

    approvalMutation.mutate({
      bastId: selectedBAST.id,
      action: approvalAction,
      comments: comments.trim() || undefined,
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING_APPROVAL: { variant: "secondary" as const, label: "Menunggu Persetujuan" },
      APPROVED: { variant: "default" as const, label: "Disetujui" },
      REJECTED: { variant: "destructive" as const, label: "Ditolak" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getConditionBadge = (condition: string) => {
    const conditionConfig = {
      GOOD: { variant: "default" as const, label: "Baik", icon: CheckCircle },
      DAMAGED: { variant: "destructive" as const, label: "Rusak", icon: XCircle },
      INCOMPLETE: { variant: "secondary" as const, label: "Tidak Lengkap", icon: Clock },
    };

    const config = conditionConfig[condition as keyof typeof conditionConfig] || {
      variant: "outline" as const,
      label: condition,
      icon: Clock,
    };

    const IconComponent = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getChecklistStatusIcon = (status: string) => {
    switch (status) {
      case "PASS":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "FAIL":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "N/A":
        return <Clock className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  if (fetchError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Error loading BASTs: {(fetchError as Error).message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Dashboard Persetujuan BAST</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="PENDING_APPROVAL">Menunggu Persetujuan</TabsTrigger>
              <TabsTrigger value="APPROVED">Disetujui</TabsTrigger>
              <TabsTrigger value="REJECTED">Ditolak</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nomor BAST</TableHead>
                      <TableHead>PO Number</TableHead>
                      <TableHead>Vendor</TableHead>
                      <TableHead>Tanggal Serah Terima</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Diajukan</TableHead>
                      <TableHead>Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          Loading...
                        </TableCell>
                      </TableRow>
                    ) : data?.basts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          Tidak ada BAST ditemukan
                        </TableCell>
                      </TableRow>
                    ) : (
                      data?.basts.map((bast) => (
                        <TableRow key={bast.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{bast.bastNumber}</p>
                              <p className="text-sm text-gray-600">{bast.location}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-mono text-sm">{bast.purchaseOrder.poNumber}</span>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium">{bast.purchaseOrder.vendor.companyName}</span>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">
                              {format(new Date(bast.handoverDate), "dd MMM yyyy HH:mm", {
                                locale: id,
                              })}
                            </span>
                          </TableCell>
                          <TableCell>{getStatusBadge(bast.status)}</TableCell>
                          <TableCell>
                            <div>
                              <p className="text-sm font-medium">{bast.submittedBy.name}</p>
                              <p className="text-xs text-gray-600">
                                {format(new Date(bast.submittedAt), "dd MMM yyyy", {
                                  locale: id,
                                })}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedBAST(bast)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                Review
                              </Button>
                              {bast.status === "PENDING_APPROVAL" && (
                                <>
                                  <Button
                                    variant="default"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedBAST(bast);
                                      setApprovalAction("APPROVE");
                                      setShowApprovalDialog(true);
                                    }}
                                    disabled={approvalMutation.isPending}
                                  >
                                    <Check className="h-4 w-4 mr-1" />
                                    Setujui
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedBAST(bast);
                                      setApprovalAction("REJECT");
                                      setShowApprovalDialog(true);
                                    }}
                                    disabled={approvalMutation.isPending}
                                  >
                                    <X className="h-4 w-4 mr-1" />
                                    Tolak
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* BAST Detail Dialog */}
      {selectedBAST && !showApprovalDialog && (
        <Dialog open={!!selectedBAST} onOpenChange={() => setSelectedBAST(null)}>
          <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Detail BAST - {selectedBAST.bastNumber}</DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Informasi Dasar</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Nomor BAST</Label>
                    <p className="text-sm font-mono">{selectedBAST.bastNumber}</p>
                  </div>
                  <div>
                    <Label>PO Number</Label>
                    <p className="text-sm font-mono">{selectedBAST.purchaseOrder.poNumber}</p>
                  </div>
                  <div>
                    <Label>Vendor</Label>
                    <p className="text-sm">{selectedBAST.purchaseOrder.vendor.companyName}</p>
                  </div>
                  <div>
                    <Label>Tanggal Serah Terima</Label>
                    <p className="text-sm">
                      {format(new Date(selectedBAST.handoverDate), "dd MMMM yyyy HH:mm", {
                        locale: id,
                      })}
                    </p>
                  </div>
                  <div>
                    <Label>Lokasi</Label>
                    <p className="text-sm">{selectedBAST.location}</p>
                  </div>
                  <div>
                    <Label>Status</Label>
                    {getStatusBadge(selectedBAST.status)}
                  </div>
                </div>
              </div>

              {/* Quality Checklist */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Checklist Kualitas</h3>
                <div className="space-y-2">
                  {selectedBAST.qualityChecklist.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        {getChecklistStatusIcon(item.status)}
                        <div>
                          <p className="font-medium">{item.item}</p>
                          {item.notes && <p className="text-sm text-gray-600">{item.notes}</p>}
                        </div>
                      </div>
                      <Badge variant={item.status === "PASS" ? "default" : item.status === "FAIL" ? "destructive" : "secondary"}>
                        {item.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Items */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Daftar Item</h3>
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item</TableHead>
                        <TableHead>Qty Dipesan</TableHead>
                        <TableHead>Qty Diserahkan</TableHead>
                        <TableHead>Unit</TableHead>
                        <TableHead>Kondisi</TableHead>
                        <TableHead>Nilai</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedBAST.items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{item.purchaseOrderItem.item.name}</p>
                              {item.notes && <p className="text-sm text-gray-600">{item.notes}</p>}
                            </div>
                          </TableCell>
                          <TableCell>{item.purchaseOrderItem.quantity}</TableCell>
                          <TableCell>{item.deliveredQuantity}</TableCell>
                          <TableCell>{item.purchaseOrderItem.item.unit}</TableCell>
                          <TableCell>{getConditionBadge(item.condition)}</TableCell>
                          <TableCell>
                            {formatCurrency(item.deliveredQuantity * item.purchaseOrderItem.price)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Notes */}
              {selectedBAST.notes && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Catatan</h3>
                  <p className="text-sm bg-gray-50 p-3 rounded border">
                    {selectedBAST.notes}
                  </p>
                </div>
              )}

              {/* Approval History */}
              {selectedBAST.approvals.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Riwayat Persetujuan</h3>
                  <div className="space-y-2">
                    {selectedBAST.approvals.map((approval) => (
                      <div key={approval.id} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium">{approval.approver.name}</p>
                          <p className="text-sm text-gray-600">Sequence: {approval.sequence}</p>
                          {approval.comments && <p className="text-sm text-gray-600">{approval.comments}</p>}
                        </div>
                        <div className="text-right">
                          <Badge variant={approval.status === "APPROVED" ? "default" : approval.status === "REJECTED" ? "destructive" : "secondary"}>
                            {approval.status}
                          </Badge>
                          {approval.processedAt && (
                            <p className="text-xs text-gray-600 mt-1">
                              {format(new Date(approval.processedAt), "dd MMM yyyy HH:mm", {
                                locale: id,
                              })}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedBAST(null)}>
                Tutup
              </Button>
              {selectedBAST.status === "PENDING_APPROVAL" && (
                <>
                  <Button
                    variant="default"
                    onClick={() => {
                      setApprovalAction("APPROVE");
                      setShowApprovalDialog(true);
                    }}
                    disabled={approvalMutation.isPending}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Setujui BAST
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => {
                      setApprovalAction("REJECT");
                      setShowApprovalDialog(true);
                    }}
                    disabled={approvalMutation.isPending}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Tolak BAST
                  </Button>
                </>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Approval Action Dialog */}
      {showApprovalDialog && selectedBAST && (
        <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {approvalAction === "APPROVE" ? "Setujui" : "Tolak"} BAST
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <p>
                Anda akan {approvalAction === "APPROVE" ? "menyetujui" : "menolak"} BAST{" "}
                <strong>{selectedBAST.bastNumber}</strong>
              </p>

              <div className="space-y-2">
                <Label htmlFor="comments">
                  Komentar {approvalAction === "REJECT" ? "*" : "(Opsional)"}
                </Label>
                <Textarea
                  id="comments"
                  placeholder={
                    approvalAction === "APPROVE"
                      ? "Komentar tambahan..."
                      : "Masukkan alasan penolakan..."
                  }
                  value={comments}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setComments(e.target.value)}
                  rows={4}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowApprovalDialog(false)}>
                Batal
              </Button>
              <Button
                variant={approvalAction === "APPROVE" ? "default" : "destructive"}
                onClick={handleApprovalAction}
                disabled={
                  approvalMutation.isPending ||
                  (approvalAction === "REJECT" && !comments.trim())
                }
              >
                {approvalMutation.isPending
                  ? "Memproses..."
                  : approvalAction === "APPROVE"
                    ? "Setujui"
                    : "Tolak"
                }
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
