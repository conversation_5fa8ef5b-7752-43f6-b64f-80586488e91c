'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Users,
  Building,
  Award,
  Settings,
} from 'lucide-react';
import React, { useState, useCallback } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';


interface VendorRequirement {
  id: string;
  name: string;
  description?: string;
  category: 'LEGAL' | 'FINANCIAL' | 'TECHNICAL' | 'EXPERIENCE';
  type: 'MANDATORY' | 'PREFERRED' | 'SCORING';
  templateId?: string;
  isActive: boolean;
  weight?: number;
  criteria: {
    description: string;
    requirements: Array<{
      item: string;
      description: string;
      required: boolean;
      scoringCriteria?: {
        minScore?: number;
        maxScore?: number;
        weightage?: number;
      };
    }>;
    evaluation: {
      method: 'PASS_FAIL' | 'SCORING' | 'RANKING';
      passingScore?: number;
      maxScore?: number;
    };
  };
  validationRules?: Array<{
    field: string;
    rule: string;
    value: any;
    message: string;
  }>;
  requiredDocuments?: Array<{
    name: string;
    type: string;
    required: boolean;
    format?: string[];
    maxSize?: number;
    description?: string;
  }>;
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
  template?: {
    id: string;
    name: string;
    type: string;
    category: string;
  };
  _count: {
    procurementRequirements: number;
  };
}

interface VendorRequirementFormData {
  name: string;
  description: string;
  category: 'LEGAL' | 'FINANCIAL' | 'TECHNICAL' | 'EXPERIENCE';
  type: 'MANDATORY' | 'PREFERRED' | 'SCORING';
  templateId?: string;
  weight?: number;
  criteria: {
    description: string;
    requirements: Array<{
      item: string;
      description: string;
      required: boolean;
      scoringCriteria?: {
        minScore?: number;
        maxScore?: number;
        weightage?: number;
      };
    }>;
    evaluation: {
      method: 'PASS_FAIL' | 'SCORING' | 'RANKING';
      passingScore?: number;
      maxScore?: number;
    };
  };
  requiredDocuments: Array<{
    name: string;
    type: string;
    required: boolean;
    format?: string[];
    maxSize?: number;
    description?: string;
  }>;
}

// API functions
async function fetchVendorRequirements(filters?: {
  category?: string;
  type?: string;
  templateId?: string;
  search?: string;
}) {
  const params = new URLSearchParams();
  if (filters?.category) params.append('category', filters.category);
  if (filters?.type) params.append('type', filters.type);
  if (filters?.templateId) params.append('templateId', filters.templateId);
  if (filters?.search) params.append('search', filters.search);

  const response = await fetch(`/api/admin/vendor-requirements?${params}`);
  if (!response.ok) throw new Error('Failed to fetch vendor requirements');
  return response.json();
}

async function createVendorRequirement(data: VendorRequirementFormData) {
  const response = await fetch('/api/admin/vendor-requirements', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Failed to create vendor requirement');
  return response.json();
}

async function updateVendorRequirement(id: string, data: Partial<VendorRequirementFormData>) {
  const response = await fetch(`/api/admin/vendor-requirements/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Failed to update vendor requirement');
  return response.json();
}

async function deleteVendorRequirement(id: string) {
  const response = await fetch(`/api/admin/vendor-requirements/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) throw new Error('Failed to delete vendor requirement');
  return response.json();
}

async function fetchWorkflowTemplates() {
  const response = await fetch('/api/admin/procurement-workflow-templates');
  if (!response.ok) throw new Error('Failed to fetch workflow templates');
  return response.json();
}

const categoryIcons = {
  LEGAL: FileText,
  FINANCIAL: Building,
  TECHNICAL: Settings,
  EXPERIENCE: Award,
};

const categoryColors = {
  LEGAL: 'bg-blue-100 text-blue-800 border-blue-200',
  FINANCIAL: 'bg-green-100 text-green-800 border-green-200',
  TECHNICAL: 'bg-purple-100 text-purple-800 border-purple-200',
  EXPERIENCE: 'bg-orange-100 text-orange-800 border-orange-200',
};

const typeColors = {
  MANDATORY: 'bg-red-100 text-red-800 border-red-200',
  PREFERRED: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  SCORING: 'bg-blue-100 text-blue-800 border-blue-200',
};

export function VendorRequirementsManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingRequirement, setEditingRequirement] = useState<VendorRequirement | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [templateFilter, setTemplateFilter] = useState<string>('all');

  const [formData, setFormData] = useState<VendorRequirementFormData>({
    name: '',
    description: '',
    category: 'LEGAL',
    type: 'MANDATORY',
    criteria: {
      description: '',
      requirements: [],
      evaluation: {
        method: 'PASS_FAIL',
      },
    },
    requiredDocuments: [],
  });

  const queryClient = useQueryClient();

  const { data: requirementsData, isLoading } = useQuery({
    queryKey: ['vendor-requirements', { categoryFilter, typeFilter, templateFilter, searchTerm }],
    queryFn: () => fetchVendorRequirements({
      category: categoryFilter && categoryFilter !== 'all' ? categoryFilter : undefined,
      type: typeFilter && typeFilter !== 'all' ? typeFilter : undefined,
      templateId: templateFilter && templateFilter !== 'all' ? templateFilter : undefined,
      search: searchTerm || undefined,
    }),
  });

  const { data: templatesData } = useQuery({
    queryKey: ['workflow-templates'],
    queryFn: fetchWorkflowTemplates,
  });

  const requirements = requirementsData?.requirements || [];
  const templates = templatesData?.templates || [];

  const createMutation = useMutation({
    mutationFn: createVendorRequirement,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-requirements'] });
      toast.success('Vendor requirement created successfully');
      setIsCreateDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create vendor requirement');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<VendorRequirementFormData> }) =>
      updateVendorRequirement(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-requirements'] });
      toast.success('Vendor requirement updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update vendor requirement');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteVendorRequirement,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-requirements'] });
      toast.success('Vendor requirement deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete vendor requirement');
    },
  });

  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      description: '',
      category: 'LEGAL',
      type: 'MANDATORY',
      criteria: {
        description: '',
        requirements: [],
        evaluation: {
          method: 'PASS_FAIL',
        },
      },
      requiredDocuments: [],
    });
    setEditingRequirement(null);
  }, []);

  const handleCreate = useCallback(() => {
    createMutation.mutate(formData);
  }, [createMutation, formData]);

  const handleUpdate = useCallback(() => {
    if (editingRequirement) {
      updateMutation.mutate({ id: editingRequirement.id, data: formData });
    }
  }, [updateMutation, editingRequirement, formData]);

  const handleEdit = useCallback((requirement: VendorRequirement) => {
    setEditingRequirement(requirement);
    setFormData({
      name: requirement.name,
      description: requirement.description || '',
      category: requirement.category,
      type: requirement.type,
      templateId: requirement.templateId,
      weight: requirement.weight,
      criteria: requirement.criteria,
      requiredDocuments: requirement.requiredDocuments || [],
    });
    setIsEditDialogOpen(true);
  }, []);

  const handleDelete = useCallback((id: string) => {
    if (confirm('Are you sure you want to delete this vendor requirement?')) {
      deleteMutation.mutate(id);
    }
  }, [deleteMutation]);

  const getCategoryBadge = (category: string) => {
    const Icon = categoryIcons[category as keyof typeof categoryIcons];
    return (
      <Badge variant="outline" className={categoryColors[category as keyof typeof categoryColors]}>
        <Icon className="h-3 w-3 mr-1" />
        {category}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    return (
      <Badge variant="outline" className={typeColors[type as keyof typeof typeColors]}>
        {type}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Vendor Requirements Management</h1>
          <p className="text-gray-600 mt-2">
            Define and manage vendor qualification requirements and eligibility criteria
          </p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Create Requirement
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Vendor Requirement</DialogTitle>
            </DialogHeader>
            <VendorRequirementForm
              formData={formData}
              setFormData={setFormData}
              templates={templates}
              onSubmit={handleCreate}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search requirements..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label>Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  <SelectItem value="LEGAL">Legal</SelectItem>
                  <SelectItem value="FINANCIAL">Financial</SelectItem>
                  <SelectItem value="TECHNICAL">Technical</SelectItem>
                  <SelectItem value="EXPERIENCE">Experience</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  <SelectItem value="MANDATORY">Mandatory</SelectItem>
                  <SelectItem value="PREFERRED">Preferred</SelectItem>
                  <SelectItem value="SCORING">Scoring</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Template</Label>
              <Select value={templateFilter} onValueChange={setTemplateFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All templates" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All templates</SelectItem>
                  {templates.map((template: any) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requirements Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Vendor Requirements
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : requirements.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No vendor requirements found. Create your first requirement!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Template</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {requirements.map((requirement: VendorRequirement) => (
                  <TableRow key={requirement.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{requirement.name}</div>
                        {requirement.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {requirement.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getCategoryBadge(requirement.category)}</TableCell>
                    <TableCell>{getTypeBadge(requirement.type)}</TableCell>
                    <TableCell>
                      {requirement.template ? (
                        <div className="text-sm">
                          <div className="font-medium">{requirement.template.name}</div>
                          <div className="text-gray-500">{requirement.template.type}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400">No template</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {requirement._count.procurementRequirements} procurements
                      </div>
                    </TableCell>
                    <TableCell>
                      {requirement.isActive ? (
                        <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
                          <XCircle className="h-3 w-3 mr-1" />
                          Inactive
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(requirement)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(requirement.id)}
                          disabled={requirement._count.procurementRequirements > 0}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Vendor Requirement</DialogTitle>
          </DialogHeader>
          <VendorRequirementForm
            formData={formData}
            setFormData={setFormData}
            templates={templates}
            onSubmit={handleUpdate}
            onCancel={() => setIsEditDialogOpen(false)}
            isLoading={updateMutation.isPending}
            isEdit
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Vendor Requirement Form Component
interface VendorRequirementFormProps {
  formData: VendorRequirementFormData;
  setFormData: React.Dispatch<React.SetStateAction<VendorRequirementFormData>>;
  templates: any[];
  onSubmit: () => void;
  onCancel: () => void;
  isLoading: boolean;
  isEdit?: boolean;
}

function VendorRequirementForm({
  formData,
  setFormData,
  templates,
  onSubmit,
  onCancel,
  isLoading,
  isEdit = false,
}: VendorRequirementFormProps) {
  const addRequirement = () => {
    setFormData(prev => ({
      ...prev,
      criteria: {
        ...prev.criteria,
        requirements: [
          ...prev.criteria.requirements,
          {
            item: '',
            description: '',
            required: true,
          },
        ],
      },
    }));
  };

  const removeRequirement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      criteria: {
        ...prev.criteria,
        requirements: prev.criteria.requirements.filter((_, i) => i !== index),
      },
    }));
  };

  const updateRequirement = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      criteria: {
        ...prev.criteria,
        requirements: prev.criteria.requirements.map((req, i) =>
          i === index ? { ...req, [field]: value } : req
        ),
      },
    }));
  };

  const addDocument = () => {
    setFormData(prev => ({
      ...prev,
      requiredDocuments: [
        ...prev.requiredDocuments,
        {
          name: '',
          type: '',
          required: true,
        },
      ],
    }));
  };

  const removeDocument = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requiredDocuments: prev.requiredDocuments.filter((_, i) => i !== index),
    }));
  };

  const updateDocument = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      requiredDocuments: prev.requiredDocuments.map((doc, i) =>
        i === index ? { ...doc, [field]: value } : doc
      ),
    }));
  };

  return (
    <Tabs defaultValue="basic" className="space-y-4">
      <TabsList>
        <TabsTrigger value="basic">Basic Information</TabsTrigger>
        <TabsTrigger value="criteria">Evaluation Criteria</TabsTrigger>
        <TabsTrigger value="documents">Required Documents</TabsTrigger>
      </TabsList>

      <TabsContent value="basic" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter requirement name"
            />
          </div>

          <div>
            <Label htmlFor="category">Category *</Label>
            <Select
              value={formData.category}
              onValueChange={(value: any) => setFormData(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="LEGAL">Legal</SelectItem>
                <SelectItem value="FINANCIAL">Financial</SelectItem>
                <SelectItem value="TECHNICAL">Technical</SelectItem>
                <SelectItem value="EXPERIENCE">Experience</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="type">Type *</Label>
            <Select
              value={formData.type}
              onValueChange={(value: any) => setFormData(prev => ({ ...prev, type: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MANDATORY">Mandatory</SelectItem>
                <SelectItem value="PREFERRED">Preferred</SelectItem>
                <SelectItem value="SCORING">Scoring</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="template">Workflow Template</Label>
            <Select
              value={formData.templateId || 'none'}
              onValueChange={(value) => setFormData(prev => ({ ...prev, templateId: value === 'none' ? undefined : value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select template (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No template</SelectItem>
                {templates.map((template: any) => (
                  <SelectItem key={template.id} value={template.id}>
                    {template.name} ({template.type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Enter requirement description"
            rows={3}
          />
        </div>

        {formData.type === 'SCORING' && (
          <div>
            <Label htmlFor="weight">Weight (%)</Label>
            <Input
              id="weight"
              type="number"
              min="0"
              max="100"
              value={formData.weight || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, weight: parseInt(e.target.value) || undefined }))}
              placeholder="Enter weight percentage"
            />
          </div>
        )}
      </TabsContent>

      <TabsContent value="criteria" className="space-y-4">
        <div>
          <Label htmlFor="criteria-description">Evaluation Description</Label>
          <Textarea
            id="criteria-description"
            value={formData.criteria.description}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              criteria: { ...prev.criteria, description: e.target.value }
            }))}
            placeholder="Describe the evaluation criteria"
            rows={3}
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-4">
            <Label>Requirements</Label>
            <Button type="button" variant="outline" size="sm" onClick={addRequirement}>
              <Plus className="h-4 w-4 mr-2" />
              Add Requirement
            </Button>
          </div>

          <div className="space-y-4">
            {formData.criteria.requirements.map((requirement, index) => (
              <Card key={index}>
                <CardContent className="pt-4">
                  <div className="flex items-start justify-between mb-4">
                    <h4 className="font-medium">Requirement {index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRequirement(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Item</Label>
                      <Input
                        value={requirement.item}
                        onChange={(e) => updateRequirement(index, 'item', e.target.value)}
                        placeholder="Requirement item"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={requirement.required}
                        onChange={(e) => updateRequirement(index, 'required', e.target.checked)}
                      />
                      <Label>Required</Label>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label>Description</Label>
                    <Textarea
                      value={requirement.description}
                      onChange={(e) => updateRequirement(index, 'description', e.target.value)}
                      placeholder="Requirement description"
                      rows={2}
                    />
                  </div>

                  {formData.type === 'SCORING' && (
                    <div className="mt-4 grid grid-cols-3 gap-4">
                      <div>
                        <Label>Min Score</Label>
                        <Input
                          type="number"
                          value={requirement.scoringCriteria?.minScore || ''}
                          onChange={(e) => updateRequirement(index, 'scoringCriteria', {
                            ...requirement.scoringCriteria,
                            minScore: parseInt(e.target.value) || undefined
                          })}
                        />
                      </div>
                      <div>
                        <Label>Max Score</Label>
                        <Input
                          type="number"
                          value={requirement.scoringCriteria?.maxScore || ''}
                          onChange={(e) => updateRequirement(index, 'scoringCriteria', {
                            ...requirement.scoringCriteria,
                            maxScore: parseInt(e.target.value) || undefined
                          })}
                        />
                      </div>
                      <div>
                        <Label>Weightage (%)</Label>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          value={requirement.scoringCriteria?.weightage || ''}
                          onChange={(e) => updateRequirement(index, 'scoringCriteria', {
                            ...requirement.scoringCriteria,
                            weightage: parseInt(e.target.value) || undefined
                          })}
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div>
          <Label>Evaluation Method</Label>
          <Select
            value={formData.criteria.evaluation.method}
            onValueChange={(value: any) => setFormData(prev => ({
              ...prev,
              criteria: {
                ...prev.criteria,
                evaluation: { ...prev.criteria.evaluation, method: value }
              }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PASS_FAIL">Pass/Fail</SelectItem>
              <SelectItem value="SCORING">Scoring</SelectItem>
              <SelectItem value="RANKING">Ranking</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {(formData.criteria.evaluation.method === 'SCORING' || formData.criteria.evaluation.method === 'RANKING') && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Passing Score</Label>
              <Input
                type="number"
                value={formData.criteria.evaluation.passingScore || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  criteria: {
                    ...prev.criteria,
                    evaluation: {
                      ...prev.criteria.evaluation,
                      passingScore: parseInt(e.target.value) || undefined
                    }
                  }
                }))}
              />
            </div>
            <div>
              <Label>Max Score</Label>
              <Input
                type="number"
                value={formData.criteria.evaluation.maxScore || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  criteria: {
                    ...prev.criteria,
                    evaluation: {
                      ...prev.criteria.evaluation,
                      maxScore: parseInt(e.target.value) || undefined
                    }
                  }
                }))}
              />
            </div>
          </div>
        )}
      </TabsContent>

      <TabsContent value="documents" className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>Required Documents</Label>
          <Button type="button" variant="outline" size="sm" onClick={addDocument}>
            <Plus className="h-4 w-4 mr-2" />
            Add Document
          </Button>
        </div>

        <div className="space-y-4">
          {formData.requiredDocuments.map((document, index) => (
            <Card key={index}>
              <CardContent className="pt-4">
                <div className="flex items-start justify-between mb-4">
                  <h4 className="font-medium">Document {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeDocument(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Document Name</Label>
                    <Input
                      value={document.name}
                      onChange={(e) => updateDocument(index, 'name', e.target.value)}
                      placeholder="Document name"
                    />
                  </div>

                  <div>
                    <Label>Document Type</Label>
                    <Input
                      value={document.type}
                      onChange={(e) => updateDocument(index, 'type', e.target.value)}
                      placeholder="Document type"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <Label>Description</Label>
                  <Textarea
                    value={document.description || ''}
                    onChange={(e) => updateDocument(index, 'description', e.target.value)}
                    placeholder="Document description"
                    rows={2}
                  />
                </div>

                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={document.required}
                      onChange={(e) => updateDocument(index, 'required', e.target.checked)}
                    />
                    <Label>Required</Label>
                  </div>

                  <div>
                    <Label>Max Size (MB)</Label>
                    <Input
                      type="number"
                      value={document.maxSize || ''}
                      onChange={(e) => updateDocument(index, 'maxSize', parseInt(e.target.value) || undefined)}
                      placeholder="Max file size"
                    />
                  </div>

                  <div>
                    <Label>Allowed Formats</Label>
                    <Input
                      value={document.format?.join(', ') || ''}
                      onChange={(e) => updateDocument(index, 'format', e.target.value.split(',').map(f => f.trim()).filter(Boolean))}
                      placeholder="pdf, doc, jpg"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </TabsContent>

      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <Button onClick={onSubmit} disabled={isLoading || !formData.name}>
          {isLoading ? 'Saving...' : isEdit ? 'Update' : 'Create'} Requirement
        </Button>
      </div>
    </Tabs>
  );
}
