"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  ShoppingCart,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3
} from "lucide-react";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface CommitteeDashboardProps {
  stats: any;
}

interface ProcurementForCommittee {
  id: string;
  title: string;
  procurementNumber: string;
  status: string;
  offersCount: number;
  evaluatedCount: number;
  deadline: string;
  myRole: string;
}

interface PendingEvaluation {
  id: string;
  procurementTitle: string;
  vendorName: string;
  submissionDate: string;
  priority: string;
}

async function fetchCommitteeProcurements(): Promise<ProcurementForCommittee[]> {
  const response = await fetch("/api/committee/procurements");
  if (!response.ok) {
    throw new Error("Failed to fetch committee procurements");
  }
  const result = await response.json();
  return result.data;
}

async function fetchPendingEvaluations(): Promise<PendingEvaluation[]> {
  const response = await fetch("/api/committee/pending-evaluations");
  if (!response.ok) {
    throw new Error("Failed to fetch pending evaluations");
  }
  const result = await response.json();
  return result.data;
}

export function CommitteeDashboard({ stats }: CommitteeDashboardProps) {
  const { data: procurements, isLoading: procurementsLoading } = useQuery({
    queryKey: ["committee-procurements"],
    queryFn: fetchCommitteeProcurements,
    refetchInterval: 30000,
  });

  const { data: evaluations, isLoading: evaluationsLoading } = useQuery({
    queryKey: ["pending-evaluations"],
    queryFn: fetchPendingEvaluations,
    refetchInterval: 30000,
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PUBLISHED: { variant: "default" as const, label: "Terbuka" },
      SUBMISSION: { variant: "default" as const, label: "Penawaran" },
      EVALUATION: { variant: "secondary" as const, label: "Evaluasi" },
      NEGOTIATION: { variant: "secondary" as const, label: "Negosiasi" },
      WINNER_ANNOUNCEMENT: { variant: "default" as const, label: "Pengumuman" },
      AWARDED: { variant: "default" as const, label: "Dikontrakkan" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      CHAIRMAN: { variant: "default" as const, label: "Ketua" },
      SECRETARY: { variant: "secondary" as const, label: "Sekretaris" },
      MEMBER: { variant: "outline" as const, label: "Anggota" },
      TECHNICAL_EXPERT: { variant: "secondary" as const, label: "Ahli Teknis" },
    };

    const config = roleConfig[role as keyof typeof roleConfig] || {
      variant: "outline" as const,
      label: role,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      LOW: { variant: "secondary" as const, label: "Rendah" },
      MEDIUM: { variant: "default" as const, label: "Sedang" },
      HIGH: { variant: "destructive" as const, label: "Tinggi" },
      URGENT: { variant: "destructive" as const, label: "Mendesak" },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || {
      variant: "outline" as const,
      label: priority,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Aksi Cepat</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/evaluations">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <BarChart3 className="h-6 w-6" />
                <span className="text-sm">Evaluasi Penawaran</span>
              </Button>
            </Link>
            
            <Link href="/committee/procurements">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <ShoppingCart className="h-6 w-6" />
                <span className="text-sm">Pengadaan Saya</span>
              </Button>
            </Link>
            
            <Link href="/committee/reports">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <FileText className="h-6 w-6" />
                <span className="text-sm">Laporan</span>
              </Button>
            </Link>
            
            <Link href="/committee/schedule">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Clock className="h-6 w-6" />
                <span className="text-sm">Jadwal</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Procurements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Pengadaan Saya
            </CardTitle>
          </CardHeader>
          <CardContent>
            {procurementsLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : procurements?.length === 0 ? (
              <p className="text-center text-gray-500 py-8">
                Tidak ada pengadaan yang ditugaskan
              </p>
            ) : (
              <div className="space-y-4">
                {procurements?.slice(0, 5).map((procurement) => (
                  <div key={procurement.id} className="border rounded p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{procurement.title}</p>
                        <p className="text-xs text-gray-500">{procurement.procurementNumber}</p>
                      </div>
                      <div className="flex gap-1">
                        {getStatusBadge(procurement.status)}
                        {getRoleBadge(procurement.myRole)}
                      </div>
                    </div>
                    <div className="flex justify-between items-center text-xs text-gray-600">
                      <span>
                        {procurement.offersCount} penawaran • {procurement.evaluatedCount} dievaluasi
                      </span>
                      <span>
                        Deadline: {format(new Date(procurement.deadline), "dd MMM yyyy", { locale: id })}
                      </span>
                    </div>
                    <div className="mt-2">
                      <Link href={`/procurements/${procurement.id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          Lihat Detail
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
                {procurements && procurements.length > 5 && (
                  <Link href="/committee/procurements">
                    <Button variant="outline" size="sm" className="w-full">
                      Lihat Semua Pengadaan
                    </Button>
                  </Link>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pending Evaluations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Evaluasi Menunggu
            </CardTitle>
          </CardHeader>
          <CardContent>
            {evaluationsLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : evaluations?.length === 0 ? (
              <p className="text-center text-gray-500 py-8">
                Tidak ada evaluasi yang menunggu
              </p>
            ) : (
              <div className="space-y-4">
                {evaluations?.slice(0, 5).map((evaluation) => (
                  <div key={evaluation.id} className="border rounded p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{evaluation.procurementTitle}</p>
                        <p className="text-xs text-gray-500">Vendor: {evaluation.vendorName}</p>
                      </div>
                      {getPriorityBadge(evaluation.priority)}
                    </div>
                    <div className="flex justify-between items-center text-xs text-gray-600">
                      <span>
                        Dikirim: {format(new Date(evaluation.submissionDate), "dd MMM yyyy", { locale: id })}
                      </span>
                    </div>
                    <div className="mt-2">
                      <Link href={`/evaluations?offerId=${evaluation.id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          Evaluasi Sekarang
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
                {evaluations && evaluations.length > 5 && (
                  <Link href="/evaluations">
                    <Button variant="outline" size="sm" className="w-full">
                      Lihat Semua Evaluasi
                    </Button>
                  </Link>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Committee Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Performa Panitia
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {procurements?.length || 0}
              </div>
              <div className="text-sm text-gray-600">Pengadaan Aktif</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {procurements?.filter(p => p.status === "AWARDED").length || 0}
              </div>
              <div className="text-sm text-gray-600">Selesai</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {evaluations?.length || 0}
              </div>
              <div className="text-sm text-gray-600">Menunggu Evaluasi</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {procurements?.filter(p => p.status === "EVALUATION").length || 0}
              </div>
              <div className="text-sm text-gray-600">Dalam Evaluasi</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
