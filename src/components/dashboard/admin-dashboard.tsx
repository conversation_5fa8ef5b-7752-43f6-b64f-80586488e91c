"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  Users,
  Package,
  AlertTriangle,
  TrendingUp,
  Clock,
  CheckCircle,
  BarChart3,
  Target,
  Calendar,
  Settings,
} from "lucide-react";
import Link from "next/link";

import { WorkflowOverviewWidget } from "@/components/admin/workflow-overview-widget";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface DashboardStats {
  vendors: {
    verified: number;
    pending: number;
    total: number;
  };
  procurements: {
    active: number;
    completed: number;
    total: number;
  };
  offers: {
    submitted: number;
    evaluated: number;
    total: number;
  };
}

interface AdminDashboardProps {
  stats: DashboardStats;
}

interface RecentActivity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  severity: string;
}

interface PendingApproval {
  id: string;
  type: string;
  title: string;
  requestedBy: string;
  requestedAt: string;
  priority: string;
}

async function fetchRecentActivities(): Promise<RecentActivity[]> {
  const response = await fetch("/api/admin/activities?limit=10");
  if (!response.ok) {
    throw new Error("Failed to fetch recent activities");
  }
  const result = await response.json();
  return result.data;
}

async function fetchPendingApprovals(): Promise<PendingApproval[]> {
  const response = await fetch("/api/admin/pending-approvals");
  if (!response.ok) {
    throw new Error("Failed to fetch pending approvals");
  }
  const result = await response.json();
  return result.data;
}

export function AdminDashboard({ stats }: AdminDashboardProps) {
  const { data: activities, isLoading: activitiesLoading } = useQuery({
    queryKey: ["admin-activities"],
    queryFn: fetchRecentActivities,
    refetchInterval: 30000,
  });

  const { data: approvals, isLoading: approvalsLoading } = useQuery({
    queryKey: ["pending-approvals"],
    queryFn: fetchPendingApprovals,
    refetchInterval: 30000,
  });

  const getSeverityBadge = (severity: string) => {
    const severityConfig = {
      LOW: { variant: "secondary" as const, label: "Rendah" },
      MEDIUM: { variant: "default" as const, label: "Sedang" },
      HIGH: { variant: "destructive" as const, label: "Tinggi" },
      CRITICAL: { variant: "destructive" as const, label: "Kritis" },
    };

    const config = severityConfig[severity as keyof typeof severityConfig] || {
      variant: "outline" as const,
      label: severity,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      LOW: { variant: "secondary" as const, label: "Rendah" },
      MEDIUM: { variant: "default" as const, label: "Sedang" },
      HIGH: { variant: "destructive" as const, label: "Tinggi" },
      URGENT: { variant: "destructive" as const, label: "Mendesak" },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || {
      variant: "outline" as const,
      label: priority,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Aksi Cepat</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/admin/vendors">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Users className="h-6 w-6" />
                <span className="text-sm">Kelola Vendor</span>
              </Button>
            </Link>

            <Link href="/procurements/create">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Package className="h-6 w-6" />
                <span className="text-sm">Buat Pengadaan</span>
              </Button>
            </Link>

            <Link href="/evaluations">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <CheckCircle className="h-6 w-6" />
                <span className="text-sm">Evaluasi</span>
              </Button>
            </Link>

            <Link href="/reports">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <TrendingUp className="h-6 w-6" />
                <span className="text-sm">Laporan</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Workflow Management Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Workflow Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/admin/workflows?tab=dashboard">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <BarChart3 className="h-6 w-6" />
                <span className="text-sm">Dashboard</span>
              </Button>
            </Link>

            <Link href="/admin/workflows?tab=requirements">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Target className="h-6 w-6" />
                <span className="text-sm">Requirements</span>
              </Button>
            </Link>

            <Link href="/admin/workflows?tab=schedules">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Calendar className="h-6 w-6" />
                <span className="text-sm">Schedules</span>
              </Button>
            </Link>

            <Link href="/admin/workflows?tab=builder">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <Settings className="h-6 w-6" />
                <span className="text-sm">Builder</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Workflow Overview */}
      <WorkflowOverviewWidget />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Aktivitas Terbaru
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activitiesLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : activities?.length === 0 ? (
              <p className="text-center text-gray-500 py-8">
                Tidak ada aktivitas terbaru
              </p>
            ) : (
              <div className="space-y-4">
                {activities?.map((activity) => (
                  <div key={activity.id} className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.description}</p>
                      <p className="text-xs text-gray-500">
                        {format(new Date(activity.timestamp), "dd MMM yyyy HH:mm", { locale: id })}
                      </p>
                    </div>
                    {getSeverityBadge(activity.severity)}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pending Approvals */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Menunggu Persetujuan
            </CardTitle>
          </CardHeader>
          <CardContent>
            {approvalsLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : approvals?.length === 0 ? (
              <p className="text-center text-gray-500 py-8">
                Tidak ada item yang menunggu persetujuan
              </p>
            ) : (
              <div className="space-y-4">
                {approvals?.map((approval) => (
                  <div key={approval.id} className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{approval.title}</p>
                      <p className="text-xs text-gray-500">
                        Oleh {approval.requestedBy} • {format(new Date(approval.requestedAt), "dd MMM yyyy", { locale: id })}
                      </p>
                    </div>
                    {getPriorityBadge(approval.priority)}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* System Health */}
      <Card>
        <CardHeader>
          <CardTitle>Status Sistem</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats?.vendors.verified || 0}
              </div>
              <div className="text-sm text-gray-600">Vendor Terverifikasi</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {stats?.vendors.pending || 0}
              </div>
              <div className="text-sm text-gray-600">Vendor Pending</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats?.procurements.active || 0}
              </div>
              <div className="text-sm text-gray-600">Pengadaan Aktif</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {stats?.offers.submitted || 0}
              </div>
              <div className="text-sm text-gray-600">Penawaran Masuk</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
