"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  ShoppingCart,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp
} from "lucide-react";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface VendorDashboardProps {
  stats: any;
}

interface VendorOffer {
  id: string;
  procurementTitle: string;
  procurementNumber: string;
  status: string;
  totalOfferedPrice: number;
  submissionDate: string;
  evaluationStatus?: string;
}

interface AvailableProcurement {
  id: string;
  title: string;
  procurementNumber: string;
  type: string;
  deadline: string;
  estimatedValue?: number;
  status: string;
}

async function fetchVendorOffers(): Promise<VendorOffer[]> {
  const response = await fetch("/api/vendor/offers");
  if (!response.ok) {
    throw new Error("Failed to fetch vendor offers");
  }
  const result = await response.json();
  return result.data;
}

async function fetchAvailableProcurements(): Promise<AvailableProcurement[]> {
  const response = await fetch("/api/vendor/available-procurements");
  if (!response.ok) {
    throw new Error("Failed to fetch available procurements");
  }
  const result = await response.json();
  return result.data;
}

export function VendorDashboard({ stats }: VendorDashboardProps) {
  const { data: offers, isLoading: offersLoading } = useQuery({
    queryKey: ["vendor-offers"],
    queryFn: fetchVendorOffers,
    refetchInterval: 30000,
  });

  const { data: procurements, isLoading: procurementsLoading } = useQuery({
    queryKey: ["available-procurements"],
    queryFn: fetchAvailableProcurements,
    refetchInterval: 60000,
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      SUBMITTED: { variant: "default" as const, label: "Dikirim" },
      EVALUATED: { variant: "secondary" as const, label: "Dievaluasi" },
      AWARDED: { variant: "default" as const, label: "Menang" },
      REJECTED: { variant: "destructive" as const, label: "Ditolak" },
      NEGOTIATION: { variant: "secondary" as const, label: "Negosiasi" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getProcurementStatusBadge = (status: string) => {
    const statusConfig = {
      PUBLISHED: { variant: "default" as const, label: "Terbuka" },
      SUBMISSION: { variant: "default" as const, label: "Penawaran" },
      EVALUATION: { variant: "secondary" as const, label: "Evaluasi" },
      CLOSED: { variant: "outline" as const, label: "Ditutup" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Aksi Cepat</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/procurements">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <ShoppingCart className="h-6 w-6" />
                <span className="text-sm">Lihat Pengadaan</span>
              </Button>
            </Link>
            
            <Link href="/vendor/offers">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <FileText className="h-6 w-6" />
                <span className="text-sm">Penawaran Saya</span>
              </Button>
            </Link>
            
            <Link href="/vendor/profile">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <CheckCircle className="h-6 w-6" />
                <span className="text-sm">Profil Vendor</span>
              </Button>
            </Link>
            
            <Link href="/vendor/documents">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2">
                <FileText className="h-6 w-6" />
                <span className="text-sm">Dokumen</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Recent Offers */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Penawaran Terbaru
            </CardTitle>
          </CardHeader>
          <CardContent>
            {offersLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : offers?.length === 0 ? (
              <p className="text-center text-gray-500 py-8">
                Belum ada penawaran yang dikirim
              </p>
            ) : (
              <div className="space-y-4">
                {offers?.slice(0, 5).map((offer) => (
                  <div key={offer.id} className="border rounded p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{offer.procurementTitle}</p>
                        <p className="text-xs text-gray-500">{offer.procurementNumber}</p>
                      </div>
                      {getStatusBadge(offer.status)}
                    </div>
                    <div className="flex justify-between items-center text-xs text-gray-600">
                      <span>{formatCurrency(offer.totalOfferedPrice)}</span>
                      <span>{format(new Date(offer.submissionDate), "dd MMM yyyy", { locale: id })}</span>
                    </div>
                  </div>
                ))}
                {offers && offers.length > 5 && (
                  <Link href="/vendor/offers">
                    <Button variant="outline" size="sm" className="w-full">
                      Lihat Semua Penawaran
                    </Button>
                  </Link>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Available Procurements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Pengadaan Tersedia
            </CardTitle>
          </CardHeader>
          <CardContent>
            {procurementsLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : procurements?.length === 0 ? (
              <p className="text-center text-gray-500 py-8">
                Tidak ada pengadaan yang tersedia saat ini
              </p>
            ) : (
              <div className="space-y-4">
                {procurements?.slice(0, 5).map((procurement) => (
                  <div key={procurement.id} className="border rounded p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{procurement.title}</p>
                        <p className="text-xs text-gray-500">{procurement.procurementNumber}</p>
                      </div>
                      {getProcurementStatusBadge(procurement.status)}
                    </div>
                    <div className="flex justify-between items-center text-xs text-gray-600">
                      <span>
                        {procurement.estimatedValue 
                          ? formatCurrency(procurement.estimatedValue)
                          : "Nilai tidak ditampilkan"
                        }
                      </span>
                      <span>
                        Deadline: {format(new Date(procurement.deadline), "dd MMM yyyy", { locale: id })}
                      </span>
                    </div>
                    <div className="mt-2">
                      <Link href={`/procurements/${procurement.id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          Lihat Detail
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
                {procurements && procurements.length > 5 && (
                  <Link href="/procurements">
                    <Button variant="outline" size="sm" className="w-full">
                      Lihat Semua Pengadaan
                    </Button>
                  </Link>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Ringkasan Performa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {offers?.length || 0}
              </div>
              <div className="text-sm text-gray-600">Total Penawaran</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {offers?.filter(o => o.status === "AWARDED").length || 0}
              </div>
              <div className="text-sm text-gray-600">Penawaran Menang</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {offers?.filter(o => o.status === "SUBMITTED").length || 0}
              </div>
              <div className="text-sm text-gray-600">Menunggu Evaluasi</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {offers?.filter(o => o.status === "NEGOTIATION").length || 0}
              </div>
              <div className="text-sm text-gray-600">Dalam Negosiasi</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
