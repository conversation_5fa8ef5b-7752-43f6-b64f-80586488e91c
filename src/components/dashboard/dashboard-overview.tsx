"use client";

import { useQuery } from "@tanstack/react-query";
import {
  ShoppingCart,
  Users,
  FileText,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  DollarSign
} from "lucide-react";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AdminDashboard } from "@/components/dashboard/admin-dashboard";
import { VendorDashboard } from "@/components/dashboard/vendor-dashboard";
import { CommitteeDashboard } from "@/components/dashboard/committee-dashboard";

interface DashboardStats {
  procurements: {
    total: number;
    active: number;
    completed: number;
    draft: number;
  };
  vendors: {
    total: number;
    verified: number;
    pending: number;
    rejected: number;
  };
  offers: {
    total: number;
    submitted: number;
    evaluated: number;
    awarded: number;
  };
  financial: {
    totalValue: number;
    averageValue: number;
    savings: number;
  };
}

async function fetchDashboardStats(): Promise<DashboardStats> {
  const response = await fetch("/api/dashboard/stats");
  if (!response.ok) {
    throw new Error("Failed to fetch dashboard stats");
  }
  const result = await response.json();
  return result.data;
}

interface DashboardOverviewProps {
  userRoles: string[];
}

export function DashboardOverview({ userRoles }: DashboardOverviewProps) {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ["dashboard-stats"],
    queryFn: fetchDashboardStats,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Error loading dashboard: {(error as Error).message}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const isAdmin = userRoles.includes("ADMIN");
  const isVendor = userRoles.includes("VENDOR");
  const isCommittee = userRoles.includes("COMMITTEE");

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pengadaan</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.procurements.total || 0}</div>
            <div className="flex gap-2 mt-2">
              <Badge variant="default" className="text-xs">
                {stats?.procurements.active || 0} Aktif
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {stats?.procurements.completed || 0} Selesai
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Vendor Terdaftar</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.vendors.total || 0}</div>
            <div className="flex gap-2 mt-2">
              <Badge variant="default" className="text-xs">
                {stats?.vendors.verified || 0} Terverifikasi
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {stats?.vendors.pending || 0} Pending
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Penawaran</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.offers.total || 0}</div>
            <div className="flex gap-2 mt-2">
              <Badge variant="default" className="text-xs">
                {stats?.offers.submitted || 0} Dikirim
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {stats?.offers.evaluated || 0} Dievaluasi
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Nilai Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats?.financial.totalValue || 0)}
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              Rata-rata: {formatCurrency(stats?.financial.averageValue || 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Role-specific Dashboards */}
      <Tabs defaultValue={isAdmin ? "admin" : isVendor ? "vendor" : "committee"} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {isAdmin && <TabsTrigger value="admin">Admin</TabsTrigger>}
          {isVendor && <TabsTrigger value="vendor">Vendor</TabsTrigger>}
          {isCommittee && <TabsTrigger value="committee">Panitia</TabsTrigger>}
        </TabsList>

        {isAdmin && (
          <TabsContent value="admin">
            <AdminDashboard stats={stats || { vendors: { verified: 0, pending: 0, total: 0 }, procurements: { active: 0, completed: 0, total: 0 }, offers: { submitted: 0, evaluated: 0, total: 0 } }} />
          </TabsContent>
        )}

        {isVendor && (
          <TabsContent value="vendor">
            <VendorDashboard stats={stats} />
          </TabsContent>
        )}

        {isCommittee && (
          <TabsContent value="committee">
            <CommitteeDashboard stats={stats} />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
