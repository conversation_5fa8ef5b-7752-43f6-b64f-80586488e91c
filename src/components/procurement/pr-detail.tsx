"use client";

import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, Send, Edit, FileText, Clock, CheckCircle, X as XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";

interface PRDetailProps {
  prId: string;
}

const statusLabels = {
  DRAFT: "Draft",
  PENDING_APPROVAL: "Menunggu Persetujuan",
  APPROVED: "Disetujui",
  REJECTED: "Ditolak",
  CONSOLIDATED: "Terkonsolidasi",
};

const statusColors = {
  DRAFT: "secondary",
  PENDING_APPROVAL: "warning",
  APPROVED: "success",
  REJECTED: "destructive",
  CONSOLIDATED: "default",
} as const;

export function PRDetailComponent({ prId }: PRDetailProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch PR details
  const { data: pr, isLoading, error, refetch } = useQuery({
    queryKey: ["purchase-requisition", prId],
    queryFn: async () => {
      const response = await fetch(`/api/purchase-requisitions/${prId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch purchase requisition");
      }
      return response.json();
    },
  });

  const handleSubmitForApproval = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/purchase-requisitions/${prId}/release`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit for approval");
      }

      toast({
        title: "Berhasil",
        description: "PR berhasil diajukan untuk persetujuan",
      });

      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Gagal mengajukan PR untuk persetujuan",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error || !pr?.data) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">Error loading purchase requisition</p>
        </CardContent>
      </Card>
    );
  }

  const prData = pr.data;
  const canEdit = ["DRAFT", "REJECTED"].includes(prData.status);
  const canSubmit = ["DRAFT", "REJECTED"].includes(prData.status);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{prData.prNumber}</h1>
            <p className="text-muted-foreground">{prData.title}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant={statusColors[prData.status as keyof typeof statusColors]} className="text-sm">
            {statusLabels[prData.status as keyof typeof statusLabels]}
          </Badge>
          
          {canEdit && (
            <Button variant="outline" onClick={() => router.push(`/purchase-requisitions/${prId}/edit`)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
          
          {canSubmit && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button disabled={isSubmitting}>
                  <Send className="h-4 w-4 mr-2" />
                  Ajukan Persetujuan
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Ajukan untuk Persetujuan</AlertDialogTitle>
                  <AlertDialogDescription>
                    Apakah Anda yakin ingin mengajukan PR ini untuk persetujuan? 
                    Setelah diajukan, PR tidak dapat diedit hingga proses persetujuan selesai.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Batal</AlertDialogCancel>
                  <AlertDialogAction onClick={() => void handleSubmitForApproval()}>
                    Ya, Ajukan
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Informasi Dasar</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Nomor PR</label>
                  <p className="font-medium">{prData.prNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tipe</label>
                  <p className="font-medium">{prData.type}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Pemohon</label>
                  <p className="font-medium">{prData.requester.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Total Estimasi</label>
                  <p className="font-medium">{formatCurrency(prData.totalAmount)}</p>
                </div>
              </div>
              
              {prData.sourceContract && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Kontrak Sumber</label>
                  <p className="font-medium">{prData.sourceContract.contractNumber} - {prData.sourceContract.title}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Item</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {prData.items.map((item: any, index: number) => (
                  <div key={item.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium">{item.name}</h4>
                      <span className="text-sm text-muted-foreground">#{index + 1}</span>
                    </div>
                    
                    {item.description && (
                      <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                    )}
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Jumlah: </span>
                        <span className="font-medium">{item.quantity} {item.unit}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Harga Estimasi: </span>
                        <span className="font-medium">{formatCurrency(item.estimatedPrice)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Total: </span>
                        <span className="font-medium">{formatCurrency(item.quantity * item.estimatedPrice)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Information */}
          <Card>
            <CardHeader>
              <CardTitle>Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                {prData.status === "APPROVED" && <CheckCircle className="h-5 w-5 text-green-500" />}
                {prData.status === "REJECTED" && <XCircle className="h-5 w-5 text-red-500" />}
                {prData.status === "PENDING_APPROVAL" && <Clock className="h-5 w-5 text-yellow-500" />}
                <Badge variant={statusColors[prData.status as keyof typeof statusColors]}>
                  {statusLabels[prData.status as keyof typeof statusLabels]}
                </Badge>
              </div>
              
              <div className="text-sm text-muted-foreground">
                <p>Dibuat: {new Date(prData.createdAt).toLocaleDateString('id-ID')}</p>
                {prData.submittedAt && (
                  <p>Diajukan: {new Date(prData.submittedAt).toLocaleDateString('id-ID')}</p>
                )}
                {prData.approvedAt && (
                  <p>Disetujui: {new Date(prData.approvedAt).toLocaleDateString('id-ID')}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Approval Status */}
          {prData.approvalStatus && (
            <Card>
              <CardHeader>
                <CardTitle>Status Persetujuan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm font-medium">{prData.approvalStatus.workflowName}</p>
                
                <div className="space-y-2">
                  {prData.approvalStatus.allSteps.map((step: any) => (
                    <div key={step.id} className="flex items-center gap-2 text-sm">
                      {step.status === "APPROVED" && <CheckCircle className="h-4 w-4 text-green-500" />}
                      {step.status === "IN_PROGRESS" && <Clock className="h-4 w-4 text-yellow-500" />}
                      {step.status === "PENDING" && <div className="h-4 w-4 rounded-full border-2 border-gray-300" />}
                      <span className={step.status === "IN_PROGRESS" ? "font-medium" : ""}>
                        {step.step.name}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Documents */}
          {prData.documents && prData.documents.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Dokumen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {prData.documents.map((doc: any) => (
                    <div key={doc.id} className="flex items-center gap-2 text-sm">
                      <FileText className="h-4 w-4" />
                      <a 
                        href={doc.fileUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        {doc.fileName}
                      </a>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
