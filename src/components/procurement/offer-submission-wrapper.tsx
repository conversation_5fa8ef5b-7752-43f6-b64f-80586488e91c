"use client";

import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";

import { OfferSubmissionForm } from "@/components/procurement/offer-submission-form";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface OfferSubmissionWrapperProps {
  procurement: {
    id: string;
    title: string;
    items: Array<{
      id: string;
      name: string;
      description?: string;
      quantity: number;
      unit: string;
      ownerEstimate: number;
    }>;
  };
  currentUser: any;
}

interface ExistingOffer {
  id: string;
  status: string;
  totalOfferedPrice: number;
  negotiatedPrice?: number;
  submissionDate: string;
  items: Array<{
    id: string;
    itemId: string;
    offeredPrice: number;
    item: {
      id: string;
      name: string;
      quantity: number;
      unit: string;
    };
  }>;
  documents: Array<{
    id: string;
    fileName: string;
    fileUrl: string;
    fileType: string;
    description?: string;
  }>;
}

async function fetchVendorOffer(procurementId: string): Promise<ExistingOffer | null> {
  const response = await fetch(`/api/procurements/${procurementId}/offers/my-offer`);
  if (response.status === 404) {
    return null; // No offer submitted yet
  }
  if (!response.ok) {
    throw new Error("Failed to fetch vendor offer");
  }
  const result = await response.json();
  return result.data;
}

export function OfferSubmissionWrapper({ procurement, currentUser }: OfferSubmissionWrapperProps) {
  const [hasVendorProfile, setHasVendorProfile] = useState(false);

  useEffect(() => {
    setHasVendorProfile(!!currentUser?.vendor);
  }, [currentUser]);

  const { data: existingOffer, isLoading, error } = useQuery({
    queryKey: ["vendor-offer", procurement.id],
    queryFn: () => fetchVendorOffer(procurement.id),
    enabled: hasVendorProfile,
  });

  if (!hasVendorProfile) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Submit Penawaran</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-gray-500">
            Anda perlu memiliki profil vendor yang terverifikasi untuk submit penawaran.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (currentUser?.vendor?.verificationStatus !== "VERIFIED") {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Submit Penawaran</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-2">
            <Badge variant="secondary">
              Status: {currentUser?.vendor?.verificationStatus || "PENDING"}
            </Badge>
            <p className="text-gray-500">
              Akun vendor Anda masih dalam proses verifikasi. 
              Silakan tunggu hingga akun diverifikasi untuk dapat submit penawaran.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Submit Penawaran</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center">Loading...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Submit Penawaran</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-red-500">
            Error: {(error as Error).message}
          </p>
        </CardContent>
      </Card>
    );
  }

  // If vendor has already submitted an offer, show the submitted offer details
  if (existingOffer) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Penawaran Anda</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="font-medium">Status:</span>
            <Badge variant={existingOffer.status === "SUBMITTED" ? "default" : "secondary"}>
              {existingOffer.status}
            </Badge>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="font-medium">Total Penawaran:</span>
            <span className="font-semibold">
              {new Intl.NumberFormat("id-ID", {
                style: "currency",
                currency: "IDR",
                minimumFractionDigits: 0,
              }).format(existingOffer.totalOfferedPrice)}
            </span>
          </div>

          {existingOffer.negotiatedPrice && (
            <div className="flex justify-between items-center">
              <span className="font-medium">Harga Negosiasi:</span>
              <span className="font-semibold text-green-600">
                {new Intl.NumberFormat("id-ID", {
                  style: "currency",
                  currency: "IDR",
                  minimumFractionDigits: 0,
                }).format(existingOffer.negotiatedPrice)}
              </span>
            </div>
          )}

          <div className="flex justify-between items-center">
            <span className="font-medium">Tanggal Submit:</span>
            <span>
              {new Date(existingOffer.submissionDate).toLocaleDateString("id-ID", {
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </span>
          </div>

          <div className="pt-4 border-t">
            <h4 className="font-medium mb-2">Detail Item:</h4>
            <div className="space-y-2">
              {existingOffer.items.map((item) => (
                <div key={item.id} className="flex justify-between text-sm">
                  <span>{item.item.name}</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                      minimumFractionDigits: 0,
                    }).format(item.offeredPrice)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {existingOffer.documents.length > 0 && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Dokumen Penawaran:</h4>
              <div className="space-y-2">
                {existingOffer.documents.map((doc) => (
                  <div key={doc.id} className="flex justify-between items-center text-sm">
                    <span>{doc.fileName}</span>
                    <a
                      href={doc.fileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      Lihat
                    </a>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="pt-4 border-t">
            <p className="text-sm text-gray-600">
              Penawaran Anda telah berhasil dikirim. Anda akan mendapat notifikasi 
              mengenai hasil evaluasi penawaran.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If no offer submitted yet, show the submission form
  return <OfferSubmissionForm procurement={procurement} />;
}
