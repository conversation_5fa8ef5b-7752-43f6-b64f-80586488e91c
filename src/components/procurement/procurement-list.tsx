"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Plus, Search, Filter } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface Procurement {
  id: string;
  title: string;
  procurementNumber: string;
  type: "TENDER" | "RFQ";
  status: string;
  ownerEstimate: number;
  createdAt: string;
  offers: Array<{
    id: string;
    status: string;
    submissionDate: string;
    vendor?: {
      id: string;
      companyName: string;
    };
  }>;
  _count: {
    offers: number;
  };
}

interface ProcurementsResponse {
  procurements: Procurement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

async function fetchProcurements(
  page = 1,
  status?: string,
  type?: string,
  search?: string
): Promise<ProcurementsResponse> {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  if (status) params.append("status", status);
  if (type) params.append("type", type);
  if (search) params.append("search", search);

  const response = await fetch(`/api/procurements?${params}`);
  if (!response.ok) {
    throw new Error("Failed to fetch procurements");
  }
  const result = await response.json();
  return result.data;
}

interface ProcurementListProps {
  userRoles: string[];
}

export function ProcurementList({ userRoles }: ProcurementListProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [search, setSearch] = useState("");

  const { data, isLoading, error } = useQuery({
    queryKey: ["procurements", currentPage, statusFilter, typeFilter, search],
    queryFn: () => fetchProcurements(currentPage, statusFilter, typeFilter, search),
    staleTime: 30000,
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      DRAFT: { variant: "outline" as const, label: "Draft" },
      PUBLISHED: { variant: "default" as const, label: "Dipublikasikan" },
      AANWIJZING: { variant: "secondary" as const, label: "Aanwijzing" },
      SUBMISSION: { variant: "default" as const, label: "Penawaran" },
      EVALUATION: { variant: "secondary" as const, label: "Evaluasi" },
      NEGOTIATION: { variant: "secondary" as const, label: "Negosiasi" },
      WINNER_ANNOUNCEMENT: { variant: "default" as const, label: "Pengumuman Pemenang" },
      AWARDED: { variant: "default" as const, label: "Dikontrakkan" },
      COMPLETED: { variant: "default" as const, label: "Selesai" },
      CANCELED: { variant: "destructive" as const, label: "Dibatalkan" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    return (
      <Badge variant="outline">
        {type === "TENDER" ? "Tender" : "RFQ"}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const canCreateProcurement = userRoles.includes("ADMIN") || userRoles.includes("PROCUREMENT_USER");

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Error loading procurements: {(error as Error).message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Daftar Pengadaan</CardTitle>
            {canCreateProcurement && (
              <Link href="/procurements/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Buat Pengadaan Baru
                </Button>
              </Link>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari pengadaan..."
                  value={search}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="PUBLISHED">Dipublikasikan</SelectItem>
                <SelectItem value="SUBMISSION">Penawaran</SelectItem>
                <SelectItem value="EVALUATION">Evaluasi</SelectItem>
                <SelectItem value="AWARDED">Dikontrakkan</SelectItem>
                <SelectItem value="COMPLETED">Selesai</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Jenis" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua</SelectItem>
                <SelectItem value="RFQ">RFQ</SelectItem>
                <SelectItem value="TENDER">Tender</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nomor / Judul</TableHead>
                  <TableHead>Jenis</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Estimasi</TableHead>
                  <TableHead>Penawaran</TableHead>
                  <TableHead>Tanggal Dibuat</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : data?.procurements.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      Tidak ada pengadaan ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  data?.procurements.map((procurement) => (
                    <TableRow key={procurement.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium text-sm">
                            {procurement.procurementNumber}
                          </p>
                          <p className="text-sm text-gray-600">
                            {procurement.title}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>{getTypeBadge(procurement.type)}</TableCell>
                      <TableCell>{getStatusBadge(procurement.status)}</TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {formatCurrency(procurement.ownerEstimate)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {procurement._count.offers} penawaran
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {format(new Date(procurement.createdAt), "dd MMM yyyy", {
                            locale: id,
                          })}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Link href={`/procurements/${procurement.id}`}>
                          <Button variant="outline" size="sm">
                            Detail
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {data && data.pagination.totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="flex items-center px-3">
                Page {currentPage} of {data.pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === data.pagination.totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
