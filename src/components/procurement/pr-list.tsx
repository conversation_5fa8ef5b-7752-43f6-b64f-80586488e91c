"use client";

import { useQuery } from "@tanstack/react-query";
import { Plus, Eye, Check as CheckSquare, Package } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";

interface PurchaseRequisition {
  id: string;
  prNumber: string;
  title: string;
  type: string;
  status: string;
  totalAmount: number;
  createdAt: string;
  requester: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    unit: string;
    estimatedPrice: number;
  }>;
  _count: {
    items: number;
    documents: number;
  };
}

const statusLabels = {
  DRAFT: "Draft",
  PENDING_APPROVAL: "Menunggu Persetujuan",
  APPROVED: "Disetujui",
  REJECTED: "Ditolak",
  CONSOLIDATED: "Terkonsolidasi",
};

const statusColors = {
  DRAFT: "secondary",
  PENDING_APPROVAL: "warning",
  APPROVED: "success",
  REJECTED: "destructive",
  CONSOLIDATED: "default",
} as const;

export function PRListComponent() {
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("DRAFT");
  const [selectedPRs, setSelectedPRs] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch purchase requisitions
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["purchase-requisitions", activeTab, searchQuery],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (activeTab !== "ALL") {
        params.append("status", activeTab);
      }
      if (searchQuery) {
        params.append("search", searchQuery);
      }
      
      const response = await fetch(`/api/purchase-requisitions?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch purchase requisitions");
      }
      return response.json();
    },
  });

  const handlePRSelection = (prId: string, checked: boolean) => {
    if (checked) {
      setSelectedPRs(prev => [...prev, prId]);
    } else {
      setSelectedPRs(prev => prev.filter(id => id !== prId));
    }
  };

  const handleCreateProcurementPackage = async () => {
    if (selectedPRs.length === 0) {
      toast({
        title: "Error",
        description: "Pilih minimal satu PR untuk dibuat paket pengadaan",
        variant: "destructive",
      });
      return;
    }

    try {
      const packageName = `Paket Pengadaan ${new Date().toLocaleDateString('id-ID')}`;
      
      const response = await fetch("/api/procurement-packages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: packageName,
          requisitionIds: selectedPRs,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create procurement package");
      }

      toast({
        title: "Berhasil",
        description: "Paket pengadaan berhasil dibuat",
      });

      setSelectedPRs([]);
      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal membuat paket pengadaan",
        variant: "destructive",
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">Error loading purchase requisitions</p>
        </CardContent>
      </Card>
    );
  }

  const purchaseRequisitions = data?.data || [];

  return (
    <div className="space-y-6">
      {/* Search and Actions */}
      <div className="flex items-center justify-between gap-4">
        <Input
          placeholder="Cari PR berdasarkan nomor atau judul..."
          value={searchQuery}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
          className="max-w-md"
        />
        <Button onClick={() => router.push("/purchase-requisitions/new")}>
          <Plus className="h-4 w-4 mr-2" />
          Buat PR Baru
        </Button>
      </div>

      {/* Tabs for different statuses */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="DRAFT">Draft</TabsTrigger>
          <TabsTrigger value="PENDING_APPROVAL">Menunggu Persetujuan</TabsTrigger>
          <TabsTrigger value="APPROVED">Disetujui</TabsTrigger>
          <TabsTrigger value="REJECTED">Ditolak</TabsTrigger>
          <TabsTrigger value="CONSOLIDATED">Terkonsolidasi</TabsTrigger>
        </TabsList>

        {/* Approved tab with special actions */}
        <TabsContent value="APPROVED" className="space-y-4">
          {selectedPRs.length > 0 && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {selectedPRs.length} PR dipilih
                  </p>
                  <Button onClick={handleCreateProcurementPackage}>
                    <Package className="h-4 w-4 mr-2" />
                    Buat Paket Pengadaan
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
          <PRTable 
            purchaseRequisitions={purchaseRequisitions}
            showCheckboxes={true}
            selectedPRs={selectedPRs}
            onPRSelection={handlePRSelection}
            formatCurrency={formatCurrency}
          />
        </TabsContent>

        {/* Other tabs */}
        {["DRAFT", "PENDING_APPROVAL", "REJECTED", "CONSOLIDATED"].map(status => (
          <TabsContent key={status} value={status}>
            <PRTable 
              purchaseRequisitions={purchaseRequisitions}
              showCheckboxes={false}
              selectedPRs={[]}
              onPRSelection={() => {}}
              formatCurrency={formatCurrency}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

interface PRTableProps {
  purchaseRequisitions: PurchaseRequisition[];
  showCheckboxes: boolean;
  selectedPRs: string[];
  onPRSelection: (prId: string, checked: boolean) => void;
  formatCurrency: (amount: number) => string;
}

function PRTable({ 
  purchaseRequisitions, 
  showCheckboxes, 
  selectedPRs, 
  onPRSelection, 
  formatCurrency 
}: PRTableProps) {
  const router = useRouter();

  if (purchaseRequisitions.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Tidak ada purchase requisition</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {purchaseRequisitions.map((pr) => (
        <Card key={pr.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4 flex-1">
                {showCheckboxes && (
                  <Checkbox
                    checked={selectedPRs.includes(pr.id)}
                    onCheckedChange={(checked: boolean) => onPRSelection(pr.id, checked as boolean)}
                  />
                )}
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold">{pr.prNumber}</h3>
                    <Badge variant={statusColors[pr.status as keyof typeof statusColors]}>
                      {statusLabels[pr.status as keyof typeof statusLabels]}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-muted-foreground">{pr.title}</p>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>Pemohon: {pr.requester.name}</span>
                    <span>{pr._count.items} item</span>
                    <span>{formatCurrency(pr.totalAmount)}</span>
                    <span>{new Date(pr.createdAt).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/purchase-requisitions/${pr.id}`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                Detail
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
