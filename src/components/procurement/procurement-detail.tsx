"use client";

import { format } from "date-fns";
import { id } from "date-fns/locale";
import { ArrowLeft, Calendar, Users, Package, FileText } from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";

import { OfferSubmissionWrapper } from "@/components/procurement/offer-submission-wrapper";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface ProcurementDetailProps {
  procurement: {
    id: string;
    title: string;
    procurementNumber: string;
    type: "TENDER" | "RFQ";
    status: string;
    ownerEstimate: number;
    showOwnerEstimateToVendor: boolean;
    evaluationMethod: string;
    createdAt: string;
    updatedAt: string;
    items: Array<{
      id: string;
      name: string;
      description?: string;
      quantity: number;
      unit: string;
      ownerEstimate: number;
    }>;
    stages: Array<{
      id: string;
      name: string;
      status: string;
      startDate: string;
      endDate: string;
      sequence: number;
    }>;
    committee: Array<{
      id: string;
      committeeRole: string;
      user: {
        id: string;
        name: string;
        email: string;
      };
    }>;
    offers: Array<{
      id: string;
      status: string;
      totalOfferedPrice: number;
      negotiatedPrice?: number;
      submissionDate: string;
      vendor: {
        id: string;
        companyName: string;
      };
    }>;
    _count: {
      offers: number;
    };
  };
}

export function ProcurementDetail({ procurement }: ProcurementDetailProps) {
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem("user");
    if (userData) {
      const user = JSON.parse(userData);
      setUserRoles(user.roles || []);
      setCurrentUser(user);
    }
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      DRAFT: { variant: "outline" as const, label: "Draft" },
      PUBLISHED: { variant: "default" as const, label: "Dipublikasikan" },
      AANWIJZING: { variant: "secondary" as const, label: "Aanwijzing" },
      SUBMISSION: { variant: "default" as const, label: "Penawaran" },
      EVALUATION: { variant: "secondary" as const, label: "Evaluasi" },
      NEGOTIATION: { variant: "secondary" as const, label: "Negosiasi" },
      WINNER_ANNOUNCEMENT: { variant: "default" as const, label: "Pengumuman Pemenang" },
      AWARDED: { variant: "default" as const, label: "Dikontrakkan" },
      COMPLETED: { variant: "default" as const, label: "Selesai" },
      CANCELED: { variant: "destructive" as const, label: "Dibatalkan" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getStageStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "outline" as const, label: "Menunggu" },
      ONGOING: { variant: "default" as const, label: "Berlangsung" },
      COMPLETED: { variant: "secondary" as const, label: "Selesai" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const isVendor = userRoles.includes("VENDOR");
  const canSubmitOffer = isVendor && 
    (procurement.status === "PUBLISHED" || procurement.status === "SUBMISSION");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/procurements">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{procurement.title}</h1>
            <p className="text-gray-600 mt-1">
              {procurement.procurementNumber} • {procurement.type}
            </p>
          </div>
        </div>
        <div className="text-right">
          {getStatusBadge(procurement.status)}
          <p className="text-sm text-gray-600 mt-1">
            {procurement._count.offers} penawaran diterima
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Info */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Ringkasan</TabsTrigger>
              <TabsTrigger value="items">Item</TabsTrigger>
              <TabsTrigger value="stages">Tahapan</TabsTrigger>
              <TabsTrigger value="offers">Penawaran</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Informasi Pengadaan
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Jenis Pengadaan</p>
                      <p className="text-sm">{procurement.type === "TENDER" ? "Tender" : "RFQ"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Metode Evaluasi</p>
                      <p className="text-sm">{procurement.evaluationMethod}</p>
                    </div>
                    {procurement.showOwnerEstimateToVendor && (
                      <div>
                        <p className="text-sm font-medium text-gray-600">Estimasi Pemilik</p>
                        <p className="text-sm font-semibold">{formatCurrency(procurement.ownerEstimate)}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-600">Tanggal Dibuat</p>
                      <p className="text-sm">
                        {format(new Date(procurement.createdAt), "dd MMMM yyyy", { locale: id })}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Panitia Pengadaan
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {procurement.committee.map((member) => (
                      <div key={member.id} className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{member.user.name}</p>
                          <p className="text-sm text-gray-600">{member.user.email}</p>
                        </div>
                        <Badge variant="outline">{member.committeeRole}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="items">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Daftar Item
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nama Item</TableHead>
                        <TableHead>Kuantitas</TableHead>
                        <TableHead>Satuan</TableHead>
                        {procurement.showOwnerEstimateToVendor && (
                          <TableHead>Estimasi</TableHead>
                        )}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {procurement.items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{item.name}</p>
                              {item.description && (
                                <p className="text-sm text-gray-600">{item.description}</p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{item.quantity.toLocaleString("id-ID")}</TableCell>
                          <TableCell>{item.unit}</TableCell>
                          {procurement.showOwnerEstimateToVendor && (
                            <TableCell>{formatCurrency(item.ownerEstimate)}</TableCell>
                          )}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="stages">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Tahapan Pengadaan
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {procurement.stages
                      .sort((a, b) => a.sequence - b.sequence)
                      .map((stage) => (
                        <div key={stage.id} className="flex items-center justify-between p-4 border rounded">
                          <div>
                            <div className="flex items-center gap-3">
                              <span className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                                {stage.sequence}
                              </span>
                              <div>
                                <p className="font-medium">{stage.name}</p>
                                <p className="text-sm text-gray-600">
                                  {format(new Date(stage.startDate), "dd MMM", { locale: id })} -{" "}
                                  {format(new Date(stage.endDate), "dd MMM yyyy", { locale: id })}
                                </p>
                              </div>
                            </div>
                          </div>
                          {getStageStatusBadge(stage.status)}
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="offers">
              <Card>
                <CardHeader>
                  <CardTitle>Daftar Penawaran</CardTitle>
                </CardHeader>
                <CardContent>
                  {procurement.offers.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">
                      Belum ada penawaran yang masuk
                    </p>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Vendor</TableHead>
                          <TableHead>Harga Penawaran</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Tanggal Submit</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {procurement.offers.map((offer) => (
                          <TableRow key={offer.id}>
                            <TableCell>{offer.vendor.companyName}</TableCell>
                            <TableCell>{formatCurrency(offer.totalOfferedPrice)}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{offer.status}</Badge>
                            </TableCell>
                            <TableCell>
                              {format(new Date(offer.submissionDate), "dd MMM yyyy", { locale: id })}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Offer Submission */}
        <div className="space-y-6">
          {canSubmitOffer && (
            <OfferSubmissionWrapper 
              procurement={procurement} 
              currentUser={currentUser}
            />
          )}
        </div>
      </div>
    </div>
  );
}
