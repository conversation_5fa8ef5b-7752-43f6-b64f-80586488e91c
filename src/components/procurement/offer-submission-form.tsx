"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileUpload } from "@/components/ui/file-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { type UploadResult } from "@/lib/upload";

const offerSubmissionSchema = z.object({
  offerItems: z.array(z.object({
    itemId: z.string(),
    offeredPrice: z.number().positive("Harga harus lebih dari 0"),
  })).min(1, "Minimal satu item harus diisi"),
});

type OfferSubmissionInput = z.infer<typeof offerSubmissionSchema>;

interface OfferSubmissionFormProps {
  procurement: {
    id: string;
    title: string;
    items: Array<{
      id: string;
      name: string;
      description?: string;
      quantity: number;
      unit: string;
      ownerEstimate: number;
    }>;
  };
}

interface OfferSubmissionData {
  procurementId: string;
  offerItems: Array<{
    itemId: string;
    offeredPrice: number;
  }>;
  documents?: Array<{
    documentType: string;
    fileName: string;
    fileUrl: string;
    fileType: string;
    description?: string;
  }>;
}

async function submitOffer(data: OfferSubmissionData) {
  const response = await fetch(`/api/procurements/${data.procurementId}/offers`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to submit offer");
  }

  return response.json();
}

export function OfferSubmissionForm({ procurement }: OfferSubmissionFormProps) {
  const [error, setError] = useState<string | null>(null);
  const [documents, setDocuments] = useState<{
    technical?: UploadResult;
    commercial?: UploadResult;
  }>({});

  const queryClient = useQueryClient();

  const form = useForm<OfferSubmissionInput>({
    resolver: zodResolver(offerSubmissionSchema),
    defaultValues: {
      offerItems: procurement.items.map(item => ({
        itemId: item.id,
        offeredPrice: 0,
      })),
    },
  });

  const submitMutation = useMutation({
    mutationFn: submitOffer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["vendor-offer", procurement.id] });
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const onSubmit = (data: OfferSubmissionInput) => {
    setError(null);

    // Calculate total offered price
    const totalOfferedPrice = data.offerItems.reduce((total, item) => {
      return total + item.offeredPrice;
    }, 0);

    // Prepare documents array
    const documentArray = [];
    if (documents.technical) {
      documentArray.push({
        documentType: "TECHNICAL_OFFER",
        fileName: documents.technical.fileName,
        fileUrl: documents.technical.url,
        fileType: documents.technical.fileType,
        description: "Dokumen Penawaran Teknis",
      });
    }
    if (documents.commercial) {
      documentArray.push({
        documentType: "COMMERCIAL_OFFER",
        fileName: documents.commercial.fileName,
        fileUrl: documents.commercial.url,
        fileType: documents.commercial.fileType,
        description: "Dokumen Penawaran Komersial",
      });
    }

    submitMutation.mutate({
      procurementId: procurement.id,
      offerItems: data.offerItems,
      documents: documentArray.length > 0 ? documentArray : undefined,
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateTotal = () => {
    const offerItems = form.watch("offerItems");
    return offerItems.reduce((total: number, item: any) => {
      return total + (item.offeredPrice || 0);
    }, 0);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Submit Penawaran</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Items Table */}
          <div>
            <Label className="text-base font-semibold">Daftar Item dan Harga Penawaran</Label>
            <div className="mt-2 border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Kuantitas</TableHead>
                    <TableHead>Satuan</TableHead>
                    <TableHead>Harga Penawaran *</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {procurement.items.map((item, index) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.name}</p>
                          {item.description && (
                            <p className="text-sm text-gray-600">{item.description}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{item.quantity.toLocaleString("id-ID")}</TableCell>
                      <TableCell>{item.unit}</TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          placeholder="0"
                          min="0"
                          step="1000"
                          {...form.register(`offerItems.${index}.offeredPrice`, {
                            valueAsNumber: true,
                          })}
                          className="w-full"
                        />
                        {form.formState.errors.offerItems?.[index]?.offeredPrice && (
                          <p className="text-sm text-red-500 mt-1">
                            {form.formState.errors.offerItems[index]?.offeredPrice?.message}
                          </p>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Total */}
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold">Total Penawaran:</span>
              <span className="text-xl font-bold text-blue-600">
                {formatCurrency(calculateTotal())}
              </span>
            </div>
          </div>

          {/* Documents */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Dokumen Penawaran</Label>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Dokumen Teknis (Opsional)</Label>
                <FileUpload
                  label="Upload Dokumen Teknis"
                  description="Upload dokumen penawaran teknis (PDF - Max 10MB)"
                  onUpload={(result) => setDocuments(prev => ({ ...prev, technical: result }))}
                  onRemove={() => setDocuments(prev => ({ ...prev, technical: undefined }))}
                  currentFile={documents.technical ? {
                    name: documents.technical.fileName,
                    url: documents.technical.url
                  } : undefined}
                />
              </div>

              <div className="space-y-2">
                <Label>Dokumen Komersial (Opsional)</Label>
                <FileUpload
                  label="Upload Dokumen Komersial"
                  description="Upload dokumen penawaran komersial (PDF - Max 10MB)"
                  onUpload={(result) => setDocuments(prev => ({ ...prev, commercial: result }))}
                  onRemove={() => setDocuments(prev => ({ ...prev, commercial: undefined }))}
                  currentFile={documents.commercial ? {
                    name: documents.commercial.fileName,
                    url: documents.commercial.url
                  } : undefined}
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={submitMutation.isPending || calculateTotal() === 0}
              className="w-full md:w-auto"
            >
              {submitMutation.isPending ? "Mengirim Penawaran..." : "Kirim Penawaran"}
            </Button>
          </div>

          {/* Info */}
          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
            <p className="font-medium mb-1">Catatan:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Pastikan semua harga penawaran telah diisi dengan benar</li>
              <li>Penawaran yang telah dikirim tidak dapat diubah</li>
              <li>Anda akan mendapat notifikasi mengenai hasil evaluasi penawaran</li>
            </ul>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
