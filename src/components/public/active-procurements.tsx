"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Calendar, DollarSign, FileText, Clock } from "lucide-react";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface PublicProcurement {
  id: string;
  title: string;
  procurementNumber: string;
  type: string;
  status: string;
  ownerEstimate?: number;
  showOwnerEstimateToVendor: boolean;
  createdAt: string;
  stages: Array<{
    name: string;
    endDate: string;
    status: string;
  }>;
  _count: {
    items: number;
    offers: number;
  };
}

async function fetchActiveProcurements(): Promise<PublicProcurement[]> {
  const response = await fetch("/api/public/procurements?status=active&limit=6");
  if (!response.ok) {
    throw new Error("Failed to fetch active procurements");
  }
  const result = await response.json();
  return result.data.procurements || [];
}

export function ActiveProcurements() {
  const { data: procurements, isLoading, error } = useQuery({
    queryKey: ["active-procurements"],
    queryFn: fetchActiveProcurements,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PUBLISHED: { variant: "default" as const, label: "Terbuka" },
      SUBMISSION: { variant: "default" as const, label: "Penawaran" },
      EVALUATION: { variant: "secondary" as const, label: "Evaluasi" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getActiveDeadline = (stages: PublicProcurement["stages"]) => {
    const activeStage = stages.find(stage => stage.status === "ONGOING");
    return activeStage ? new Date(activeStage.endDate) : null;
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-20 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !procurements) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-gray-500 mb-2">Tidak dapat memuat pengadaan aktif</div>
          <p className="text-gray-400">Silakan coba lagi nanti</p>
        </CardContent>
      </Card>
    );
  }

  if (procurements.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Tidak ada pengadaan aktif
          </h3>
          <p className="text-gray-600">
            Saat ini tidak ada pengadaan yang sedang berlangsung. Periksa kembali nanti.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {procurements.map((procurement) => {
        const deadline = getActiveDeadline(procurement.stages);
        
        return (
          <Card key={procurement.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start mb-2">
                <Badge variant="outline">{procurement.type}</Badge>
                {getStatusBadge(procurement.status)}
              </div>
              <CardTitle className="text-lg line-clamp-2">
                {procurement.title}
              </CardTitle>
              <p className="text-sm text-gray-600">
                {procurement.procurementNumber}
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {procurement.showOwnerEstimateToVendor && procurement.ownerEstimate && (
                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">
                      {formatCurrency(procurement.ownerEstimate)}
                    </span>
                  </div>
                )}
                
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <FileText className="h-4 w-4" />
                  <span>{procurement._count.items} item</span>
                  <span>•</span>
                  <span>{procurement._count.offers} penawaran</span>
                </div>

                {deadline && (
                  <div className="flex items-center gap-2 text-sm text-red-600">
                    <Clock className="h-4 w-4" />
                    <span>
                      Deadline: {format(deadline, "dd MMM yyyy HH:mm", { locale: id })}
                    </span>
                  </div>
                )}

                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <Calendar className="h-3 w-3" />
                  <span>
                    Dipublikasi {format(new Date(procurement.createdAt), "dd MMM yyyy", { locale: id })}
                  </span>
                </div>
              </div>

              <div className="mt-4">
                <Link href={`/procurements/${procurement.id}`}>
                  <Button className="w-full">
                    Lihat Detail
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
