"use client";

import { useQuery } from "@tanstack/react-query";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState, useEffect } from "react";

import { Button } from "@/components/ui/button";

interface LandingAsset {
  id: string;
  filename: string;
  originalName: string;
  url: string;
  title?: string;
  description?: string;
  category: string;
}

interface LandingAssetsResponse {
  banners: LandingAsset[];
  logos: LandingAsset[];
  primaryLogo: LandingAsset | null;
  primaryBanner: LandingAsset | null;
}

async function fetchLandingAssets(): Promise<LandingAssetsResponse> {
  const response = await fetch("/api/public/landing-assets");
  if (!response.ok) {
    throw new Error("Failed to fetch landing assets");
  }
  const result = await response.json();
  return result.data;
}

interface DynamicBannerProps {
  fallbackContent?: React.ReactNode;
  className?: string;
}

export function DynamicBanner({ fallbackContent, className = "" }: DynamicBannerProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const { data: assets, isLoading, error } = useQuery({
    queryKey: ["landing-assets"],
    queryFn: fetchLandingAssets,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const banners = assets?.banners || [];

  // Auto-advance slideshow
  useEffect(() => {
    if (banners.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % banners.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [banners.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % banners.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + banners.length) % banners.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Show fallback content if no banners or loading
  if (isLoading || error || banners.length === 0) {
    return fallbackContent || (
      <section className={`bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Portal E-Procurement
            </h1>
            <h2 className="text-xl md:text-2xl mb-8 text-blue-100">
              PT Bank BPD Sulteng
            </h2>
            <p className="text-lg md:text-xl mb-8 text-blue-100">
              Sistem pengadaan elektronik yang transparan, efisien, dan terpercaya
              untuk mendukung operasional perbankan yang berkualitas.
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`relative overflow-hidden ${className}`}>
      {/* Banner Slideshow */}
      <div className="relative h-96 md:h-[500px] lg:h-[600px]">
        {banners.map((banner, index) => (
          <div
            key={banner.id}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? "opacity-100" : "opacity-0"
            }`}
          >
            <div className="relative h-full">
              <img
                src={banner.url}
                alt={banner.title || banner.originalName}
                className="w-full h-full object-cover"
              />
              {/* Overlay for better text readability */}
              <div className="absolute inset-0 bg-black bg-opacity-40" />
              
              {/* Banner Content */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white max-w-4xl mx-auto px-4">
                  {banner.title && (
                    <h1 className="text-4xl md:text-6xl font-bold mb-6">
                      {banner.title}
                    </h1>
                  )}
                  {banner.description && (
                    <p className="text-lg md:text-xl mb-8 text-gray-100">
                      {banner.description}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Controls */}
      {banners.length > 1 && (
        <>
          {/* Previous/Next Buttons */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 border-white/30 text-white hover:bg-white/30"
            onClick={prevSlide}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 border-white/30 text-white hover:bg-white/30"
            onClick={nextSlide}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Slide Indicators */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {banners.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-all ${
                  index === currentSlide
                    ? "bg-white"
                    : "bg-white/50 hover:bg-white/75"
                }`}
                onClick={() => goToSlide(index)}
              />
            ))}
          </div>
        </>
      )}
    </section>
  );
}
