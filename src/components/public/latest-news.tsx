"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Calendar, User, ArrowRight } from "lucide-react";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface NewsArticle {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage?: string;
  status: string;
  publishedAt: string;
  author: {
    name: string;
  };
  category: {
    name: string;
    slug: string;
  };
}

async function fetchLatestNews(): Promise<NewsArticle[]> {
  const response = await fetch("/api/public/news?limit=3&status=published");
  if (!response.ok) {
    throw new Error("Failed to fetch latest news");
  }
  const result = await response.json();
  return result.data.articles || [];
}

export function LatestNews() {
  const { data: articles, isLoading, error } = useQuery({
    queryKey: ["latest-news"],
    queryFn: fetchLatestNews,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-32 bg-gray-200 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-16 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !articles) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-gray-500 mb-2">Tidak dapat memuat berita terbaru</div>
          <p className="text-gray-400">Silakan coba lagi nanti</p>
        </CardContent>
      </Card>
    );
  }

  if (articles.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-gray-500 mb-2">Belum ada berita</div>
          <p className="text-gray-400">Berita dan pengumuman akan muncul di sini</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {articles.map((article) => (
        <Card key={article.id} className="hover:shadow-lg transition-shadow overflow-hidden">
          {article.featuredImage && (
            <div className="aspect-video overflow-hidden">
              <img
                src={article.featuredImage}
                alt={article.title}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
          )}
          <CardHeader>
            <div className="flex items-center justify-between mb-2">
              <Badge variant="outline">{article.category.name}</Badge>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Calendar className="h-3 w-3" />
                <span>
                  {format(new Date(article.publishedAt), "dd MMM yyyy", { locale: id })}
                </span>
              </div>
            </div>
            <CardTitle className="text-lg line-clamp-2">
              {article.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-gray-600 line-clamp-3">
                {article.excerpt}
              </p>
              
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <User className="h-3 w-3" />
                <span>{article.author.name}</span>
              </div>

              <Link href={`/news/${article.slug}`}>
                <Button variant="outline" className="w-full group">
                  Baca Selengkapnya
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
