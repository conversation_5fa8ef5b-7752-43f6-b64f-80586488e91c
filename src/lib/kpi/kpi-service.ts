import { prisma } from "@/lib/db";
import { toPrismaJson } from "@/lib/utils/json";

export interface KpiMetricDefinition {
  name: string;
  type: "PERCENTAGE" | "RATIO" | "COUNT" | "DURATION" | "CURRENCY" | "SCORE" | "BOOLEAN";
  category: "QUALITY" | "DELIVERY" | "COST" | "SERVICE" | "COMPLIANCE" | "INNOVATION";
  weight: number;
  target?: number;
  description?: string;
  calculationFormula?: string;
}

export interface CreateKpiTemplateData {
  name: string;
  description?: string;
  category: "QUALITY" | "DELIVERY" | "COST" | "SERVICE" | "COMPLIANCE" | "INNOVATION" | "OVERALL";
  metrics: KpiMetricDefinition[];
  weights: Record<string, number>;
  thresholds: {
    excellent: number;
    good: number;
    satisfactory: number;
    needsImprovement: number;
  };
  calculationMethod?: "WEIGHTED_AVERAGE" | "SIMPLE_AVERAGE" | "MINIMUM_SCORE" | "MAXIMUM_SCORE" | "CUSTOM_FORMULA";
  evaluationPeriod?: "MONTHLY" | "QUARTERLY" | "SEMI_ANNUAL" | "ANNUAL" | "PROJECT_BASED";
  createdById: string;
}

export interface CreateKpiEvaluationData {
  vendorId: string;
  templateId: string;
  evaluationPeriod: string;
  startDate: Date;
  endDate: Date;
  metricScores: Record<string, number>;
  strengths?: string;
  weaknesses?: string;
  recommendations?: string;
  evaluatedById: string;
}

export interface RecordMetricData {
  vendorId: string;
  metricName: string;
  metricType: "PERCENTAGE" | "RATIO" | "COUNT" | "DURATION" | "CURRENCY" | "SCORE" | "BOOLEAN";
  category: "QUALITY" | "DELIVERY" | "COST" | "SERVICE" | "COMPLIANCE" | "INNOVATION";
  actualValue: number;
  targetValue?: number;
  evaluationPeriod: string;
  sourceType: "MANUAL_ENTRY" | "PROCUREMENT_DATA" | "CONTRACT_DATA" | "DELIVERY_DATA" | "INVOICE_DATA" | "SURVEY_DATA" | "SYSTEM_CALCULATED";
  sourceId?: string;
  notes?: string;
  recordedById: string;
}

export class VendorKpiService {
  // Create KPI template
  async createKpiTemplate(data: CreateKpiTemplateData) {
    return await prisma.vendorKpiTemplate.create({
      data: {
        name: data.name,
        description: data.description,
        category: data.category as any,
        metrics: toPrismaJson(data.metrics),
        weights: data.weights,
        thresholds: data.thresholds,
        calculationMethod: (data.calculationMethod as any) || "WEIGHTED_AVERAGE",
        evaluationPeriod: (data.evaluationPeriod as any) || "QUARTERLY",
        createdById: data.createdById,
      },
    });
  }

  // Calculate vendor KPI evaluation
  async createKpiEvaluation(data: CreateKpiEvaluationData) {
    // Get template
    const template = await prisma.vendorKpiTemplate.findUnique({
      where: { id: data.templateId },
    });

    if (!template) {
      throw new Error("KPI template not found");
    }

    // Calculate scores
    const { overallScore, categoryScores, rating } = this.calculateScores(
      data.metricScores,
      template.metrics as any,
      template.weights as any,
      template.thresholds as any,
      template.calculationMethod
    );

    // Create evaluation
    const evaluation = await prisma.vendorKpiEvaluation.create({
      data: {
        vendorId: data.vendorId,
        templateId: data.templateId,
        evaluationPeriod: data.evaluationPeriod,
        startDate: data.startDate,
        endDate: data.endDate,
        overallScore,
        categoryScores,
        metricScores: data.metricScores,
        rating: rating as any,
        strengths: data.strengths,
        weaknesses: data.weaknesses,
        recommendations: data.recommendations,
        evaluatedById: data.evaluatedById,
      },
      include: {
        vendor: {
          select: {
            id: true,
            companyName: true,
          },
        },
        template: {
          select: {
            name: true,
            category: true,
          },
        },
        evaluatedBy: {
          select: {
            name: true,
          },
        },
      },
    });

    // Update performance history
    await this.updatePerformanceHistory(data.vendorId, data.evaluationPeriod, overallScore, categoryScores);

    return evaluation;
  }

  // Record individual metric
  async recordMetric(data: RecordMetricData) {
    // Calculate achievement rate if target is provided
    const achievementRate = data.targetValue 
      ? (data.actualValue / data.targetValue) * 100 
      : null;

    // Calculate normalized score (0-100)
    const score = this.calculateMetricScore(
      data.actualValue,
      data.targetValue,
      data.metricType
    );

    return await prisma.vendorKpiMetric.create({
      data: {
        vendorId: data.vendorId,
        metricName: data.metricName,
        metricType: data.metricType as any,
        category: data.category as any,
        actualValue: data.actualValue,
        targetValue: data.targetValue,
        achievementRate,
        score,
        weight: 1.0, // Default weight, can be updated
        evaluationPeriod: data.evaluationPeriod,
        sourceType: data.sourceType as any,
        sourceId: data.sourceId,
        notes: data.notes,
        isAutoCalculated: data.sourceType !== "MANUAL_ENTRY",
        recordedById: data.recordedById,
      },
    });
  }

  // Auto-calculate metrics from system data
  async autoCalculateMetrics(vendorId: string, evaluationPeriod: string) {
    const [startDate, endDate] = this.parsePeriod(evaluationPeriod);

    // Calculate delivery performance
    await this.calculateDeliveryMetrics(vendorId, evaluationPeriod, startDate, endDate);

    // Calculate quality metrics
    await this.calculateQualityMetrics(vendorId, evaluationPeriod, startDate, endDate);

    // Calculate cost metrics
    await this.calculateCostMetrics(vendorId, evaluationPeriod, startDate, endDate);

    // Calculate compliance metrics
    await this.calculateComplianceMetrics(vendorId, evaluationPeriod, startDate, endDate);
  }

  // Get vendor performance dashboard
  async getVendorPerformanceDashboard(vendorId: string, periods: number = 4) {
    const [evaluations, performanceHistory, recentMetrics] = await Promise.all([
      // Recent evaluations
      prisma.vendorKpiEvaluation.findMany({
        where: { vendorId },
        include: {
          template: {
            select: {
              name: true,
              category: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        take: periods,
      }),

      // Performance history
      prisma.vendorPerformanceHistory.findMany({
        where: { vendorId },
        orderBy: { period: "desc" },
        take: periods,
      }),

      // Recent metrics
      prisma.vendorKpiMetric.findMany({
        where: { vendorId },
        orderBy: { recordedDate: "desc" },
        take: 20,
      }),
    ]);

    // Calculate trends
    const trends = this.calculatePerformanceTrends(performanceHistory);

    // Get current ranking
    const ranking = await this.getVendorRanking(vendorId);

    return {
      evaluations,
      performanceHistory,
      recentMetrics,
      trends,
      ranking,
    };
  }

  // Blacklist vendor
  async blacklistVendor(data: {
    vendorId: string;
    reason: string;
    description?: string;
    severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    category: "QUALITY_ISSUES" | "DELIVERY_DELAYS" | "CONTRACT_BREACH" | "FRAUD" | "CORRUPTION" | "NON_COMPLIANCE" | "POOR_PERFORMANCE" | "LEGAL_ISSUES" | "OTHER";
    endDate?: Date;
    isPermanent?: boolean;
    evidenceFiles?: string[];
    relatedContracts?: string[];
    createdById: string;
  }) {
    return await prisma.blacklistEntry.create({
      data: {
        vendorId: data.vendorId,
        reason: data.reason,
        description: data.description,
        severity: data.severity as any,
        category: data.category as any,
        endDate: data.endDate,
        isPermanent: data.isPermanent || false,
        evidenceFiles: data.evidenceFiles,
        relatedContracts: data.relatedContracts,
        createdById: data.createdById,
      },
      include: {
        vendor: {
          select: {
            companyName: true,
          },
        },
        createdBy: {
          select: {
            name: true,
          },
        },
      },
    });
  }

  // Submit blacklist appeal
  async submitBlacklistAppeal(blacklistId: string, appealReason: string) {
    return await prisma.blacklistEntry.update({
      where: { id: blacklistId },
      data: {
        appealSubmitted: true,
        appealDate: new Date(),
        appealReason,
        appealStatus: "PENDING",
        status: "APPEALED",
      },
    });
  }

  // Private helper methods
  private calculateScores(
    metricScores: Record<string, number>,
    metrics: KpiMetricDefinition[],
    weights: Record<string, number>,
    thresholds: any,
    calculationMethod: string
  ) {
    let totalScore = 0;
    let totalWeight = 0;
    const categoryScores: Record<string, number> = {};

    // Calculate category scores
    for (const metric of metrics) {
      const score = metricScores[metric.name] || 0;
      const weight = weights[metric.name] || metric.weight || 1;

      if (!categoryScores[metric.category]) {
        categoryScores[metric.category] = 0;
      }
      categoryScores[metric.category] += score * weight;
      totalScore += score * weight;
      totalWeight += weight;
    }

    // Calculate overall score
    const overallScore = totalWeight > 0 ? totalScore / totalWeight : 0;

    // Determine rating
    let rating = "POOR";
    if (overallScore >= thresholds.excellent) {
      rating = "EXCELLENT";
    } else if (overallScore >= thresholds.good) {
      rating = "GOOD";
    } else if (overallScore >= thresholds.satisfactory) {
      rating = "SATISFACTORY";
    } else if (overallScore >= thresholds.needsImprovement) {
      rating = "NEEDS_IMPROVEMENT";
    }

    return { overallScore, categoryScores, rating };
  }

  private calculateMetricScore(actualValue: number, targetValue?: number, metricType?: string): number {
    if (!targetValue) {
      // For metrics without targets, assume the value is already a score
      return Math.min(Math.max(actualValue, 0), 100);
    }

    // Calculate percentage achievement
    const achievement = (actualValue / targetValue) * 100;
    
    // Cap at 100 for most metrics, but allow over-achievement for some
    if (metricType === "COST") {
      // For cost metrics, lower is better
      return Math.min(100, (targetValue / actualValue) * 100);
    } else {
      // For other metrics, higher is better
      return Math.min(achievement, 100);
    }
  }

  private parsePeriod(period: string): [Date, Date] {
    // Parse period string like "2024-Q1", "2024-01", etc.
    const year = parseInt(period.substring(0, 4));
    
    if (period.includes("Q")) {
      const quarter = parseInt(period.substring(6));
      const startMonth = (quarter - 1) * 3;
      return [
        new Date(year, startMonth, 1),
        new Date(year, startMonth + 3, 0),
      ];
    } else if (period.includes("-")) {
      const month = parseInt(period.substring(5)) - 1;
      return [
        new Date(year, month, 1),
        new Date(year, month + 1, 0),
      ];
    } else {
      // Annual
      return [
        new Date(year, 0, 1),
        new Date(year, 11, 31),
      ];
    }
  }

  private async calculateDeliveryMetrics(vendorId: string, period: string, startDate: Date, endDate: Date) {
    // Calculate delivery-related metrics for the vendor within the specified period
    const deliveries = await prisma.procurement.findMany({
      where: {
        vendorId,
        createdAt: { gte: startDate, lte: endDate },
        status: 'COMPLETED'
      }
    });

    const onTimeDeliveries = deliveries.filter(d => d.deliveredAt && d.deliveredAt <= d.expectedDeliveryDate);
    return {
      onTimeDeliveryRate: deliveries.length > 0 ? (onTimeDeliveries.length / deliveries.length) * 100 : 0,
      totalDeliveries: deliveries.length,
      period
    };
  }

  private async calculateQualityMetrics(vendorId: string, period: string, startDate: Date, endDate: Date) {
    // Calculate quality-related metrics for the vendor
    const qualityReviews = await prisma.vendorReview.findMany({
      where: {
        vendorId,
        createdAt: { gte: startDate, lte: endDate }
      }
    });

    const avgQualityScore = qualityReviews.length > 0
      ? qualityReviews.reduce((sum, review) => sum + review.qualityScore, 0) / qualityReviews.length
      : 0;

    return { averageQualityScore: avgQualityScore, reviewCount: qualityReviews.length, period };
  }

  private async calculateCostMetrics(vendorId: string, period: string, startDate: Date, endDate: Date) {
    // Calculate cost-related metrics for the vendor
    const procurements = await prisma.procurement.findMany({
      where: {
        vendorId,
        createdAt: { gte: startDate, lte: endDate }
      }
    });

    const totalValue = procurements.reduce((sum, p) => sum + (p.estimatedValue || 0), 0);
    const avgValue = procurements.length > 0 ? totalValue / procurements.length : 0;

    return { totalContractValue: totalValue, averageContractValue: avgValue, contractCount: procurements.length, period };
  }

  private async calculateComplianceMetrics(vendorId: string, period: string, startDate: Date, endDate: Date) {
    // Calculate compliance-related metrics for the vendor
    const complianceChecks = await prisma.vendorCompliance.findMany({
      where: {
        vendorId,
        checkDate: { gte: startDate, lte: endDate }
      }
    });

    const passedChecks = complianceChecks.filter(check => check.status === 'PASSED');
    const complianceRate = complianceChecks.length > 0 ? (passedChecks.length / complianceChecks.length) * 100 : 0;

    return { complianceRate, totalChecks: complianceChecks.length, passedChecks: passedChecks.length, period };
  }

  private calculatePerformanceTrends(history: any[]) {
    // Analyze trends in performance data
    if (history.length < 2) {
      return { trend: 'INSUFFICIENT_DATA', changePercent: 0 };
    }

    const recent = history[history.length - 1];
    const previous = history[history.length - 2];

    const changePercent = ((recent.overallScore - previous.overallScore) / previous.overallScore) * 100;

    let trend = 'STABLE';
    if (changePercent > 10) trend = 'IMPROVING';
    else if (changePercent < -10) trend = 'DECLINING';

    return { trend, changePercent, dataPoints: history.length };
  }

  private async getVendorRanking(vendorId: string) {
    // Calculate vendor ranking based on overall performance scores
    const allVendors = await prisma.vendorPerformanceHistory.findMany({
      select: { vendorId: true, overallScore: true },
      orderBy: { overallScore: 'desc' },
      distinct: ['vendorId']
    });

    const vendorIndex = allVendors.findIndex(v => v.vendorId === vendorId);
    const rank = vendorIndex >= 0 ? vendorIndex + 1 : allVendors.length + 1;

    return { rank, totalVendors: allVendors.length, percentile: Math.round((1 - rank / allVendors.length) * 100) };
  }

  private async updatePerformanceHistory(vendorId: string, period: string, overallScore: number, categoryScores: any) {
    // Get previous score for trend calculation
    const previousHistory = await prisma.vendorPerformanceHistory.findFirst({
      where: { vendorId },
      orderBy: { period: "desc" },
    });

    const previousScore = previousHistory?.overallScore;
    const scoreChange = previousScore ? overallScore - previousScore : 0;
    
    let trend: "IMPROVING" | "STABLE" | "DECLINING" = "STABLE";
    if (scoreChange > 5) trend = "IMPROVING";
    else if (scoreChange < -5) trend = "DECLINING";

    await prisma.vendorPerformanceHistory.upsert({
      where: {
        vendorId_period: {
          vendorId,
          period,
        },
      },
      update: {
        overallScore,
        qualityScore: categoryScores.QUALITY,
        deliveryScore: categoryScores.DELIVERY,
        costScore: categoryScores.COST,
        serviceScore: categoryScores.SERVICE,
        complianceScore: categoryScores.COMPLIANCE,
        previousScore,
        scoreChange,
        trend: trend as any,
      },
      create: {
        vendorId,
        period,
        overallScore,
        qualityScore: categoryScores.QUALITY,
        deliveryScore: categoryScores.DELIVERY,
        costScore: categoryScores.COST,
        serviceScore: categoryScores.SERVICE,
        complianceScore: categoryScores.COMPLIANCE,
        previousScore,
        scoreChange,
        trend: trend as any,
      },
    });
  }
}

// Singleton instance
export const vendorKpiService = new VendorKpiService();
