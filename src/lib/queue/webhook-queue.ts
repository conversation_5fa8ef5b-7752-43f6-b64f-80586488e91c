import { prisma } from "@/lib/db";
import { WebhookQueueJob, QueueJobStatus } from "@/lib/types";
import { toPrismaJson } from "@/lib/utils/json";

/**
 * Enterprise-grade Webhook Queue System
 * Handles webhook processing with retry logic, scheduling, and comprehensive error handling
 */

export class WebhookQueue {
  private static instance: WebhookQueue;
  private readonly maxRetries = 5;
  private readonly retryDelays = [1000, 2000, 5000, 10000, 30000]; // Progressive delay in ms

  static getInstance(): WebhookQueue {
    if (!WebhookQueue.instance) {
      WebhookQueue.instance = new WebhookQueue();
    }
    return WebhookQueue.instance;
  }

  /**
   * Add webhook to queue for processing
   */
  async addWebhook(
    webhookData: Omit<
      WebhookQueueJob,
      "id" | "status" | "attempts" | "createdAt" | "updatedAt"
    >,
    options?: { priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT" }
  ): Promise<string> {
    try {
      const job = await prisma.webhookQueue.create({
        data: {
          url: webhookData.url,
          method: webhookData.method,
          headers: toPrismaJson(webhookData.headers || {}),
          body: webhookData.body ? toPrismaJson(webhookData.body) : null,
          payload: webhookData.payload ? toPrismaJson(webhookData.payload) : null,
          status: "PENDING" as QueueJobStatus,
          attempts: 0,
          maxAttempts: webhookData.maxAttempts || this.maxRetries,
          priority: options?.priority || webhookData.priority || "MEDIUM",
          metadata: toPrismaJson(webhookData.metadata || {}),
          scheduledAt: webhookData.scheduledAt || new Date(),
        },
      });

      return job.id;
    } catch (error) {
      console.error("Failed to enqueue webhook:", error);
      throw new Error("Failed to add webhook to queue");
    }
  }

  /**
   * Process pending webhooks in the queue
   */
  async processPendingWebhooks(limit: number = 10): Promise<void> {
    try {
      const pendingJobs = await prisma.webhookQueue.findMany({
        where: {
          status: "PENDING",
          scheduledAt: {
            lte: new Date(),
          },
        },
        orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
        take: limit,
      });

      for (const job of pendingJobs) {
        await this.processWebhookJob(job);
      }
    } catch (error) {
      console.error("Error processing webhook queue:", error);
    }
  }

  /**
   * Process individual webhook job
   */
  private async processWebhookJob(job: any): Promise<void> {
    try {
      // Update status to processing
      await prisma.webhookQueue.update({
        where: { id: job.id },
        data: {
          status: "PROCESSING",
          attempts: job.attempts + 1,
        },
      });

      // Send webhook
      const success = await this.sendWebhook(job);

      if (success) {
        await prisma.webhookQueue.update({
          where: { id: job.id },
          data: {
            status: "COMPLETED",
            processedAt: new Date(),
          },
        });
      } else {
        await this.handleWebhookFailure(job);
      }
    } catch (error) {
      console.error(`Failed to process webhook job ${job.id}:`, error);
      await this.handleWebhookFailure(
        job,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Handle webhook sending failure with retry logic
   */
  private async handleWebhookFailure(
    job: any,
    errorMessage?: string
  ): Promise<void> {
    const isMaxAttemptsReached = job.attempts >= job.maxAttempts;

    if (isMaxAttemptsReached) {
      await prisma.webhookQueue.update({
        where: { id: job.id },
        data: {
          status: "FAILED",
          failedAt: new Date(),
          error: errorMessage || "Max retry attempts reached",
        },
      });
    } else {
      // Schedule retry with progressive delay
      const retryDelay =
        this.retryDelays[Math.min(job.attempts, this.retryDelays.length - 1)];
      const nextAttemptAt = new Date(Date.now() + retryDelay);

      await prisma.webhookQueue.update({
        where: { id: job.id },
        data: {
          status: "PENDING",
          scheduledAt: nextAttemptAt,
          error: errorMessage,
        },
      });
    }
  }

  /**
   * Send webhook using HTTP client
   */
  private async sendWebhook(job: any): Promise<boolean> {
    try {
      // This is a placeholder for actual HTTP webhook delivery
      // Replace with actual HTTP client (fetch, axios, etc.)

      console.log(`Sending webhook to: ${job.url}`);
      console.log(`Method: ${job.method}`);
      console.log(`Headers:`, job.headers);

      // Simulate HTTP request
      const response = await fetch(job.url, {
        method: job.method,
        headers: {
          "Content-Type": "application/json",
          ...job.headers,
        },
        body: job.body ? JSON.stringify(job.body) : undefined,
      });

      // Consider 2xx status codes as success
      return response.status >= 200 && response.status < 300;
    } catch (error) {
      console.error("Webhook delivery error:", error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    total: number;
  }> {
    const [pending, processing, completed, failed, total] = await Promise.all([
      prisma.webhookQueue.count({ where: { status: "PENDING" } }),
      prisma.webhookQueue.count({ where: { status: "PROCESSING" } }),
      prisma.webhookQueue.count({ where: { status: "COMPLETED" } }),
      prisma.webhookQueue.count({ where: { status: "FAILED" } }),
      prisma.webhookQueue.count(),
    ]);

    return {
      pending,
      processing,
      completed,
      failed,
      total,
    };
  }

  /**
   * Retry failed webhooks
   */
  async retryFailedWebhooks(jobIds?: string[]): Promise<number> {
    const whereClause: any = { status: "FAILED" };

    if (jobIds && jobIds.length > 0) {
      whereClause.id = { in: jobIds };
    }

    const result = await prisma.webhookQueue.updateMany({
      where: whereClause,
      data: {
        status: "PENDING",
        attempts: 0,
        scheduledAt: new Date(),
        error: null,
        failedAt: null,
      },
    });

    return result.count;
  }

  /**
   * Clean up old completed and failed jobs
   */
  async cleanupOldJobs(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await prisma.webhookQueue.deleteMany({
      where: {
        OR: [
          { status: "COMPLETED", processedAt: { lt: cutoffDate } },
          { status: "FAILED", failedAt: { lt: cutoffDate } },
        ],
      },
    });

    return result.count;
  }
}

// Export singleton instance
export const webhookQueue = WebhookQueue.getInstance();
