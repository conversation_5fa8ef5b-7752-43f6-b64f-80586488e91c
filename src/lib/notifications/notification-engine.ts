import { prisma } from "@/lib/db";
import { toPrismaJson } from "@/lib/utils/json";

export interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  bodyTemplate: string;
  channels: NotificationChannel[];
  variables: string[];
  category: NotificationCategory;
  priority: NotificationPriority;
}

export interface NotificationRule {
  id: string;
  name: string;
  event: string;
  conditions: NotificationCondition[];
  recipients: NotificationRecipient[];
  template: string;
  enabled: boolean;
  escalationRules?: EscalationRule[];
}

export interface NotificationCondition {
  field: string;
  operator: "EQUALS" | "GREATER_THAN" | "LESS_THAN" | "CONTAINS" | "IN";
  value: unknown;
  logicalOperator?: "AND" | "OR";
}

export interface NotificationRecipient {
  type: "USER" | "ROLE" | "EMAIL" | "DEPARTMENT";
  value: string;
}

export interface EscalationRule {
  afterMinutes: number;
  recipients: NotificationRecipient[];
  template?: string;
}

export type NotificationChannel = "EMAIL" | "IN_APP" | "SMS" | "PUSH" | "WEBHOOK";
export type NotificationCategory = "SYSTEM" | "WORKFLOW" | "ALERT" | "REMINDER" | "ANNOUNCEMENT";
export type NotificationPriority = "LOW" | "MEDIUM" | "HIGH" | "URGENT";

export interface NotificationContext {
  entityType: string;
  entityId: string;
  entityData: Record<string, unknown>;
  triggerUser?: {
    id: string;
    name: string;
    email: string;
  };
  metadata?: Record<string, unknown>;
}

export class NotificationEngine {
  private templates: Map<string, NotificationTemplate> = new Map();
  private rules: Map<string, NotificationRule[]> = new Map();

  constructor() {
    this.initializeDefaultTemplates();
    this.initializeDefaultRules();
  }

  async sendNotification(
    event: string,
    context: NotificationContext,
    customRecipients?: NotificationRecipient[]
  ): Promise<void> {
    try {
      // Get applicable rules for this event
      const applicableRules = this.getApplicableRules(event, context);
      
      for (const rule of applicableRules) {
        if (!rule.enabled) continue;

        // Check if conditions are met
        const conditionsMet = await this.evaluateConditions(rule.conditions, context);
        if (!conditionsMet) continue;

        // Get recipients
        const recipients = customRecipients || rule.recipients;
        const resolvedRecipients = await this.resolveRecipients(recipients, context);

        // Get template
        const template = this.templates.get(rule.template);
        if (!template) {
          console.error(`Template ${rule.template} not found`);
          continue;
        }

        // Send notifications
        await this.sendToRecipients(template, context, resolvedRecipients);

        // Schedule escalations if configured
        if (rule.escalationRules && rule.escalationRules.length > 0) {
          await this.scheduleEscalations(rule.escalationRules, template, context);
        }
      }
    } catch (error) {
      console.error("Error sending notification:", error);
    }
  }

  private getApplicableRules(event: string, _context: NotificationContext): NotificationRule[] {
    const eventRules = this.rules.get(event) || [];
    return eventRules.filter(rule => rule.enabled);
  }

  private async evaluateConditions(
    conditions: NotificationCondition[],
    context: NotificationContext
  ): Promise<boolean> {
    if (conditions.length === 0) return true;

    let result = true;
    let currentOperator: "AND" | "OR" = "AND";

    for (const condition of conditions) {
      const conditionResult = this.evaluateCondition(condition, context);
      
      if (currentOperator === "AND") {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      currentOperator = condition.logicalOperator || "AND";
    }

    return result;
  }

  private evaluateCondition(condition: NotificationCondition, context: NotificationContext): boolean {
    const fieldValue = this.getNestedValue(context.entityData, condition.field);
    
    switch (condition.operator) {
      case "EQUALS":
        return fieldValue === condition.value;
      case "GREATER_THAN":
        return Number(fieldValue) > Number(condition.value);
      case "LESS_THAN":
        return Number(fieldValue) < Number(condition.value);
      case "CONTAINS":
        return String(fieldValue).includes(String(condition.value));
      case "IN":
        return Array.isArray(condition.value) && condition.value.includes(fieldValue);
      default:
        return false;
    }
  }

  private async resolveRecipients(
    recipients: NotificationRecipient[],
    context: NotificationContext
  ): Promise<Array<{ id: string; email: string; name: string; }>> {
    const resolvedRecipients: Array<{ id: string; email: string; name: string; }> = [];

    for (const recipient of recipients) {
      switch (recipient.type) {
        case "USER":
          const user = await prisma.user.findUnique({
            where: { id: recipient.value },
            select: { id: true, email: true, name: true }
          });
          if (user) resolvedRecipients.push(user);
          break;

        case "ROLE":
          const roleUsers = await prisma.user.findMany({
            where: {
              userRoles: {
                some: {
                  role: {
                    name: recipient.value
                  }
                }
              }
            },
            select: { id: true, email: true, name: true }
          });
          resolvedRecipients.push(...roleUsers);
          break;

        case "EMAIL":
          resolvedRecipients.push({
            id: `email_${recipient.value}`,
            email: recipient.value,
            name: recipient.value
          });
          break;

        case "DEPARTMENT":
          const deptUsers = await prisma.user.findMany({
            where: {
              department: {
                name: recipient.value
              }
            },
            select: { id: true, email: true, name: true }
          });
          resolvedRecipients.push(...deptUsers);
          break;
      }
    }

    return resolvedRecipients;
  }

  private async sendToRecipients(
    template: NotificationTemplate,
    context: NotificationContext,
    recipients: Array<{ id: string; email: string; name: string; }>
  ): Promise<void> {
    for (const recipient of recipients) {
      // Render template with context
      const renderedSubject = this.renderTemplate(template.subject, context, recipient);
      const renderedBody = this.renderTemplate(template.bodyTemplate, context, recipient);

      // Send through each configured channel
      for (const channel of template.channels) {
        await this.sendThroughChannel(
          channel,
          recipient,
          renderedSubject,
          renderedBody,
          template,
          context
        );
      }
    }
  }

  private async sendThroughChannel(
    channel: NotificationChannel,
    recipient: { id: string; email: string; name: string; },
    subject: string,
    body: string,
    template: NotificationTemplate,
    context: NotificationContext
  ): Promise<void> {
    switch (channel) {
      case "IN_APP":
        await this.sendInAppNotification(recipient, subject, body, template, context);
        break;
      case "EMAIL":
        await this.sendEmailNotification(recipient, subject, body, template, context);
        break;
      case "SMS":
        await this.sendSMSNotification(recipient, subject, body, template, context);
        break;
      case "PUSH":
        await this.sendPushNotification(recipient, subject, body, template, context);
        break;
      case "WEBHOOK":
        await this.sendWebhookNotification(recipient, subject, body, template, context);
        break;
    }
  }

  private async sendInAppNotification(
    recipient: { id: string; email: string; name: string; },
    subject: string,
    body: string,
    template: NotificationTemplate,
    context: NotificationContext
  ): Promise<void> {
    // Only send in-app notifications to actual users (not email-only recipients)
    if (recipient.id.startsWith("email_")) return;

    await prisma.notification.create({
      data: {
        userId: recipient.id,
        title: subject,
        message: body,
        type: this.mapPriorityToType(template.priority) as any,
        notificationType: template.name.toUpperCase().replace(/\s+/g, "_"),
        metadata: {
          entityType: context.entityType,
          entityId: context.entityId,
          templateId: template.id,
          category: template.category,
          priority: template.priority,
          ...context.metadata,
        }
      }
    });
  }

  private async sendEmailNotification(
    recipient: { id: string; email: string; name: string; },
    subject: string,
    body: string,
    template: NotificationTemplate,
    context: NotificationContext
  ): Promise<void> {
    // Import email queue dynamically to avoid circular dependencies
    const { emailQueue } = await import('../queue/email-queue');

    // Add email to Redis queue
    await emailQueue.addEmail(
      {
        to: recipient.email,
        subject,
        body,
        templateId: template.id,
      },
      {
        priority: this.mapNotificationPriorityToQueuePriority(template.priority),
      }
    );
  }

  private async sendSMSNotification(
    recipient: { id: string; email: string; name: string; },
    subject: string,
    body: string,
    template: NotificationTemplate,
    context: NotificationContext
  ): Promise<void> {
    // Get user's phone number
    if (recipient.id.startsWith("email_")) return;

    const user = await prisma.user.findUnique({
      where: { id: recipient.id },
      select: { phone: true }
    });

    if (!user?.phone) return;

    // Import SMS queue dynamically to avoid circular dependencies
    const { smsQueue } = await import('../queue/sms-queue');

    // Add SMS to Redis queue
    await smsQueue.addSms(
      {
        to: user.phone,
        message: `${subject}\n\n${body}`,
        templateId: template.id,
      },
      {
        priority: this.mapNotificationPriorityToQueuePriority(template.priority),
      }
    );
  }

  private async sendPushNotification(
    recipient: { id: string; email: string; name: string; },
    subject: string,
    body: string,
    template: NotificationTemplate,
    context: NotificationContext
  ): Promise<void> {
    // Only send push notifications to actual users
    if (recipient.id.startsWith("email_")) return;

    // Import push queue dynamically to avoid circular dependencies
    const { pushQueue } = await import('../queue/push-queue');

    // Add push notification to Redis queue
    await pushQueue.addPushNotification(
      {
        userId: recipient.id,
        title: subject,
        body,
        templateId: template.id,
        data: {
          entityType: context.entityType,
          entityId: context.entityId,
          ...context.metadata,
        },
      },
      {
        priority: this.mapNotificationPriorityToQueuePriority(template.priority),
      }
    );
  }

  private async sendWebhookNotification(
    recipient: { id: string; email: string; name: string; },
    subject: string,
    body: string,
    template: NotificationTemplate,
    context: NotificationContext
  ): Promise<void> {
    // Import webhook queue dynamically to avoid circular dependencies
    const { webhookQueue } = await import('../queue/webhook-queue');

    // Add webhook to Redis queue
    await webhookQueue.addWebhook(
      {
        url: recipient.email, // Use email field as webhook URL
        method: "POST",
        payload: {
          subject,
          body,
          recipient,
          template: {
            id: template.id,
            name: template.name,
            category: template.category,
            priority: template.priority,
          },
          context: {
            entityType: context.entityType,
            entityId: context.entityId,
            triggerUser: context.triggerUser,
            ...context.metadata,
          }
        },

      },
      {
        priority: this.mapNotificationPriorityToQueuePriority(template.priority),
      }
    );
  }

  private renderTemplate(
    template: string,
    context: NotificationContext,
    recipient: { id: string; email: string; name: string; }
  ): string {
    let rendered = template;

    // Replace recipient variables
    rendered = rendered.replace(/\{\{recipient\.name\}\}/g, recipient.name);
    rendered = rendered.replace(/\{\{recipient\.email\}\}/g, recipient.email);

    // Replace trigger user variables
    if (context.triggerUser) {
      rendered = rendered.replace(/\{\{triggerUser\.name\}\}/g, context.triggerUser.name);
      rendered = rendered.replace(/\{\{triggerUser\.email\}\}/g, context.triggerUser.email);
    }

    // Replace entity variables
    rendered = rendered.replace(/\{\{entityType\}\}/g, context.entityType);
    rendered = rendered.replace(/\{\{entityId\}\}/g, context.entityId);

    // Replace entity data variables
    const entityDataRegex = /\{\{entityData\.([^}]+)\}\}/g;
    rendered = rendered.replace(entityDataRegex, (match, path) => {
      const value = this.getNestedValue(context.entityData, path);
      return value !== undefined ? String(value) : match;
    });

    // Replace metadata variables
    if (context.metadata) {
      const metadataRegex = /\{\{metadata\.([^}]+)\}\}/g;
      rendered = rendered.replace(metadataRegex, (match, path) => {
        const value = this.getNestedValue(context.metadata!, path);
        return value !== undefined ? String(value) : match;
      });
    }

    return rendered;
  }

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current: any, key) => current?.[key], obj);
  }

  private mapPriorityToType(priority: NotificationPriority): string {
    switch (priority) {
      case "URGENT": return "ERROR";
      case "HIGH": return "WARNING";
      case "MEDIUM": return "INFO";
      case "LOW": return "SUCCESS";
      default: return "INFO";
    }
  }

  private mapNotificationPriorityToQueuePriority(priority: NotificationPriority): NotificationPriority {
    return priority;
  }

  private async scheduleEscalations(
    escalationRules: EscalationRule[],
    template: NotificationTemplate,
    context: NotificationContext
  ): Promise<void> {
    for (const rule of escalationRules) {
      await prisma.notificationEscalation.create({
        data: {
          entityType: context.entityType,
          entityId: context.entityId,
          templateId: template.id,
          escalateAt: new Date(Date.now() + rule.afterMinutes * 60 * 1000),
          recipients: toPrismaJson(rule.recipients),
          escalationTemplate: rule.template || template.id,
          metadata: toPrismaJson(context.metadata || {}),
        }
      });
    }
  }

  private initializeDefaultTemplates(): void {
    // Initialize default notification templates
    // This would typically be loaded from database or configuration
  }

  private initializeDefaultRules(): void {
    // Initialize default notification rules
    // This would typically be loaded from database or configuration
  }
}

export const notificationEngine = new NotificationEngine();
