import { NextRequest } from 'next/server';

import { auditSystem } from '@/lib/audit/comprehensive-audit-system';
import { prisma } from '@/lib/db';

export interface AuditTrailData {
  userId?: string;
  userEmail?: string;
  userRole?: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
  changedFields?: string[];
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
  procurementId?: string;
  workflowStage?: string;
  approvalStep?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category?: 'GENERAL' | 'AUTHENTICATION' | 'AUTHORIZATION' | 'DATA_CHANGE' | 'APPROVAL_WORKFLOW' | 'SECURITY' | 'DOCUMENT_MANAGEMENT';
  metadata?: any;
  description?: string;
}

// Create audit log entry
export async function createAuditLog(data: AuditTrailData): Promise<void> {
  await auditSystem.logAudit(data);
}

// Helper functions for common audit scenarios
export const auditHelpers = {
  // Vendor-related audit logs
  async vendorApproval(
    userId: string,
    vendorId: string,
    approved: boolean,
    comments?: string,
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action: approved ? 'APPROVE' : 'REJECT',
      resource: 'vendor',
      resourceId: vendorId,
      severity: 'MEDIUM',
      category: 'APPROVAL_WORKFLOW',
      description: `Vendor ${approved ? 'approved' : 'rejected'}`,
      metadata: { comments, approved },
    });
  },

  // Procurement-related audit logs
  async procurementCreated(
    userId: string,
    procurementId: string,
    procurementData: any,
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action: 'CREATE',
      resource: 'procurement',
      resourceId: procurementId,
      procurementId,
      severity: 'MEDIUM',
      category: 'DATA_CHANGE',
      description: 'New procurement created',
      newValues: procurementData,
    });
  },

  async procurementUpdated(
    userId: string,
    procurementId: string,
    oldData: any,
    newData: any,
    changedFields: string[],
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action: 'UPDATE',
      resource: 'procurement',
      resourceId: procurementId,
      procurementId,
      severity: 'LOW',
      category: 'DATA_CHANGE',
      description: 'Procurement updated',
      oldValues: oldData,
      newValues: newData,
      changedFields,
    });
  },

  // Document-related audit logs
  async documentGenerated(
    userId: string,
    documentType: string,
    resourceId: string,
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action: 'GENERATE_DOCUMENT',
      resource: 'document',
      resourceId,
      severity: 'LOW',
      category: 'DOCUMENT_MANAGEMENT',
      description: `${documentType} document generated`,
      metadata: { documentType },
    });
  },

  // Security-related audit logs
  async securityEvent(
    userId: string,
    eventType: string,
    description: string,
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM',
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action: eventType,
      resource: 'security',
      severity,
      category: 'SECURITY',
      description,
    });
  },

  // Authentication-related audit logs
  async loginAttempt(
    userEmail: string,
    success: boolean,
    ipAddress?: string,
    userAgent?: string,
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userEmail,
      action: success ? 'LOGIN' : 'LOGIN_FAILED',
      resource: 'authentication',
      severity: success ? 'LOW' : 'MEDIUM',
      category: 'AUTHENTICATION',
      description: success ? 'User login successful' : 'User login failed',
      ipAddress,
      userAgent,
    });
  },

  // Approval workflow audit logs
  async approvalAction(
    userId: string,
    action: 'APPROVE' | 'REJECT' | 'DELEGATE' | 'ESCALATE',
    resourceType: string,
    resourceId: string,
    workflowStage?: string,
    approvalStep?: string,
    comments?: string,
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action,
      resource: resourceType,
      resourceId,
      workflowStage,
      approvalStep,
      severity: 'MEDIUM',
      category: 'APPROVAL_WORKFLOW',
      description: `${action.toLowerCase()} action performed`,
      metadata: { comments },
    });
  },

  // Data export/import audit logs
  async dataExport(
    userId: string,
    exportType: string,
    recordCount: number,
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action: 'EXPORT',
      resource: 'data',
      severity: 'MEDIUM',
      category: 'DATA_CHANGE',
      description: `Data export: ${exportType}`,
      metadata: { exportType, recordCount },
    });
  },

  async dataImport(
    userId: string,
    importType: string,
    recordCount: number,
    request?: NextRequest
  ): Promise<void> {
    await createAuditLog({
      userId,
      action: 'IMPORT',
      resource: 'data',
      severity: 'HIGH',
      category: 'DATA_CHANGE',
      description: `Data import: ${importType}`,
      metadata: { importType, recordCount },
    });
  },
};

// Get audit trail for a specific resource
export async function getAuditTrail(
  resourceType: string,
  resourceId: string,
  limit: number = 50
): Promise<any[]> {
  return await prisma.auditLog.findMany({
    where: {
      resource: resourceType,
      resourceId,
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: limit,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
        },
      },
    },
  });
}

// Get audit trail for a user
export async function getUserAuditTrail(
  userId: string,
  limit: number = 50
): Promise<any[]> {
  return await prisma.auditLog.findMany({
    where: {
      userId,
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: limit,
  });
}

// Search audit logs
export async function searchAuditLogs(filters: {
  userId?: string;
  action?: string;
  resource?: string;
  severity?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
  searchText?: string;
  procurementId?: string;
  limit?: number;
  offset?: number;
}): Promise<{ logs: any[]; total: number }> {
  const where: any = {};

  if (filters.userId) where.userId = filters.userId;
  if (filters.action) where.action = filters.action;
  if (filters.resource) where.resource = filters.resource;
  if (filters.severity) where.severity = filters.severity;
  if (filters.category) where.category = filters.category;
  if (filters.procurementId) where.procurementId = filters.procurementId;

  if (filters.startDate || filters.endDate) {
    where.timestamp = {};
    if (filters.startDate) where.timestamp.gte = filters.startDate;
    if (filters.endDate) where.timestamp.lte = filters.endDate;
  }

  if (filters.searchText) {
    where.OR = [
      { description: { contains: filters.searchText, mode: 'insensitive' } },
      { userEmail: { contains: filters.searchText, mode: 'insensitive' } },
    ];
  }

  const [logs, total] = await Promise.all([
    prisma.auditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: filters.limit || 50,
      skip: filters.offset || 0,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
          },
        },
      },
    }),
    prisma.auditLog.count({ where }),
  ]);

  return { logs, total };
}

// Alias for backward compatibility
export const getAuditLogs = searchAuditLogs;
