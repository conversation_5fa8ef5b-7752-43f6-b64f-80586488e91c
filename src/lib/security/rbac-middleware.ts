import { NextRequest, NextResponse } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { handleApiError } from "@/lib/errors";
import { hasPermission, hasAllPermissions, hasAnyPermission, PermissionCheck } from "@/lib/security/rbac";

export interface PermissionRequirement {
  resource: string;
  action: string;
  resourceId?: string;
  conditions?: Record<string, any>;
}

export interface RBACMiddlewareOptions {
  permissions?: PermissionRequirement[];
  requireAll?: boolean; // true = AND logic, false = OR logic (default: true)
  roles?: string[]; // Simple role-based check
  requireAllRoles?: boolean; // true = AND logic, false = OR logic (default: false)
}

// Higher-order function to create RBAC middleware
export function requirePermissions(
  permissions: PermissionRequirement[],
  requireAll: boolean = true
) {
  return function rbacMiddleware<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, ...args: T): Promise<NextResponse> {
      try {
        const user = await getCurrentUser(request);

        // Convert permission requirements to permission checks
        const permissionChecks: PermissionCheck[] = permissions.map(perm => ({
          resource: perm.resource,
          action: perm.action,
          resourceId: perm.resourceId,
          conditions: perm.conditions,
        }));

        let hasAccess = false;

        if (requireAll) {
          hasAccess = await hasAllPermissions(user.id, permissionChecks);
        } else {
          hasAccess = await hasAnyPermission(user.id, permissionChecks);
        }

        if (!hasAccess) {
          return handleApiError(new Error("Access denied. Insufficient permissions."));
        }

        return handler(request, ...args);
      } catch (error) {
        return handleApiError(error);
      }
    };
  };
}

// Simple role-based middleware
export function requireRoles(
  roles: string[],
  requireAll: boolean = false
) {
  return function roleMiddleware<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, ...args: T): Promise<NextResponse> {
      try {
        console.log("🔐 RBAC Middleware: Checking roles", roles);
        console.log("📍 Request URL:", request.url);
        console.log("🔑 Request method:", request.method);
        
        const user = await getCurrentUser(request);
        
        console.log("👤 User found:", user.email);
        console.log("🎭 User roles:", user.roles);

        let hasAccess = false;

        if (requireAll) {
          // User must have ALL specified roles
          hasAccess = roles.every(role => user.roles.includes(role as any));
        } else {
          // User must have ANY of the specified roles
          hasAccess = roles.some(role => user.roles.includes(role as any));
        }

        console.log("✅ Access granted:", hasAccess);

        if (!hasAccess) {
          const requiredRolesText = requireAll ? roles.join(" AND ") : roles.join(" OR ");
          console.log("❌ Access denied. Required roles:", requiredRolesText);
          return handleApiError(new Error(`Access denied. Required roles: ${requiredRolesText}`));
        }

        return handler(request, ...args);
      } catch (error) {
        console.error("❌ RBAC Middleware Error:", error instanceof Error ? error.message : String(error));
        console.log("📍 Failed URL:", request.url);
        return handleApiError(error);
      }
    };
  };
}

// Combined RBAC middleware with flexible options
export function withRBAC(options: RBACMiddlewareOptions) {
  return function rbacMiddleware<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, ...args: T): Promise<NextResponse> {
      try {
        const user = await getCurrentUser(request);

        // Check role-based access first (if specified)
        if (options.roles && options.roles.length > 0) {
          let hasRoleAccess = false;

          if (options.requireAllRoles) {
            hasRoleAccess = options.roles.every(role => user.roles.includes(role as any));
          } else {
            hasRoleAccess = options.roles.some(role => user.roles.includes(role as any));
          }

          if (!hasRoleAccess) {
            const requiredRolesText = options.requireAllRoles 
              ? options.roles.join(" AND ") 
              : options.roles.join(" OR ");
            return handleApiError(new Error(`Access denied. Required roles: ${requiredRolesText}`));
          }
        }

        // Check permission-based access (if specified)
        if (options.permissions && options.permissions.length > 0) {
          const permissionChecks: PermissionCheck[] = options.permissions.map(perm => ({
            resource: perm.resource,
            action: perm.action,
            resourceId: perm.resourceId,
            conditions: perm.conditions,
          }));

          let hasPermissionAccess = false;

          if (options.requireAll !== false) { // Default to true
            hasPermissionAccess = await hasAllPermissions(user.id, permissionChecks);
          } else {
            hasPermissionAccess = await hasAnyPermission(user.id, permissionChecks);
          }

          if (!hasPermissionAccess) {
            return handleApiError(new Error("Access denied. Insufficient permissions."));
          }
        }

        return handler(request, ...args);
      } catch (error) {
        return handleApiError(error);
      }
    };
  };
}

// Resource ownership middleware
export function requireResourceOwnership(
  resourceType: string,
  getResourceId: (request: NextRequest, ...args: any[]) => string
) {
  return function ownershipMiddleware<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, ...args: T): Promise<NextResponse> {
      try {
        const user = await getCurrentUser(request);
        const resourceId = getResourceId(request, ...args);

        // Check if user owns the resource
        const hasAccess = await hasPermission(user.id, {
          resource: resourceType,
          action: "read",
          resourceId,
          conditions: {
            ownerId: user.id,
            userId: user.id,
          },
        });

        if (!hasAccess) {
          return handleApiError(new Error("Access denied. You can only access your own resources."));
        }

        return handler(request, ...args);
      } catch (error) {
        return handleApiError(error);
      }
    };
  };
}

// Committee membership middleware for procurement-related endpoints
export function requireCommitteeMembership() {
  return function committeeMiddleware<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, ...args: T): Promise<NextResponse> {
      try {
        const user = await getCurrentUser(request);

        // Admin users bypass committee membership check
        if (user.roles.includes("ADMIN")) {
          return handler(request, ...args);
        }

        // Check if user has COMMITTEE role
        if (!user.roles.includes("COMMITTEE")) {
          return handleApiError(new Error("Access denied. Committee membership required."));
        }

        return handler(request, ...args);
      } catch (error) {
        return handleApiError(error);
      }
    };
  };
}

// Vendor verification middleware
export function requireVerifiedVendor() {
  return function vendorMiddleware<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, ...args: T): Promise<NextResponse> {
      try {
        const user = await getCurrentUser(request);

        // Check if user is a vendor
        if (!user.roles.includes("VENDOR") || !user.vendor) {
          return handleApiError(new Error("Access denied. Vendor account required."));
        }

        // Check if vendor is verified
        if (user.vendor.verificationStatus !== "VERIFIED") {
          return handleApiError(new Error("Access denied. Vendor account must be verified."));
        }

        return handler(request, ...args);
      } catch (error) {
        return handleApiError(error);
      }
    };
  };
}

// Admin-only middleware
export function requireAdmin() {
  return requireRoles(["ADMIN"], true);
}

// Alias for backward compatibility
export const adminOnly = requireAdmin;

// Multiple middleware composition helper
export function composeMiddleware<T extends any[]>(
  ...middlewares: Array<(handler: (request: NextRequest, ...args: T) => Promise<NextResponse>) => (request: NextRequest, ...args: T) => Promise<NextResponse>>
) {
  return function composedMiddleware(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return middlewares.reduceRight(
      (acc, middleware) => middleware(acc),
      handler
    );
  };
}
