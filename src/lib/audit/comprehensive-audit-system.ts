import { NextRequest } from 'next/server';

import { prisma } from '@/lib/db';
import { toPrismaJson } from "@/lib/utils/json";

export interface AuditData {
  userId?: string;
  userEmail?: string;
  userRole?: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
  changedFields?: string[];
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
  procurementId?: string;
  workflowStage?: string;
  approvalStep?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category?: 'GENERAL' | 'AUTHENTICATION' | 'AUTHORIZATION' | 'DATA_CHANGE' | 'APPROVAL_WORKFLOW' | 'SECURITY' | 'DOCUMENT_MANAGEMENT';
  metadata?: any;
  description?: string;
}

export interface AuthenticationData {
  userId?: string;
  userEmail?: string;
  action: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED' | 'PASSWORD_CHANGE' | 'PASSWORD_RESET';
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  metadata?: any;
}

export interface DataChangeData {
  userId?: string;
  userEmail?: string;
  userRole?: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE';
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
  request?: NextRequest;
  metadata?: any;
}

export interface ApprovalActionData {
  userId?: string;
  userEmail?: string;
  userRole?: string;
  action: 'APPROVE' | 'REJECT' | 'REQUEST_CHANGES' | 'DELEGATE' | 'ESCALATE';
  resourceId?: string;
  procurementId?: string;
  workflowStage?: string;
  approvalStep?: string;
  metadata?: any;
  request?: NextRequest;
}

export interface SecurityEventData {
  userId?: string;
  userEmail?: string;
  action: string;
  resource: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  metadata?: any;
  request?: NextRequest;
}

export interface AuditSearchFilters {
  userId?: string;
  action?: string;
  resource?: string;
  severity?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
  searchText?: string;
  procurementId?: string;
}

export interface AuditStatisticsFilters {
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  procurementId?: string;
}

export class ComprehensiveAuditSystem {
  async logAudit(data: AuditData): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: data.userId,
          userEmail: data.userEmail,
          userRole: data.userRole,
          action: data.action as any,
          resource: data.resource,
          resourceId: data.resourceId,
          oldValues: data.oldValues,
          newValues: data.newValues,
          changedFields: data.changedFields,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          sessionId: data.sessionId,
          requestId: data.requestId,
          procurementId: data.procurementId,
          workflowStage: data.workflowStage,
          approvalStep: data.approvalStep,
          severity: data.severity || 'LOW',
          category: data.category || 'GENERAL',
          metadata: data.metadata,
          description: data.description,
          timestamp: new Date(),
          partitionDate: new Date(),
        },
      });
    } catch (error) {
      // Fail silently to avoid breaking the main application flow
      console.error('Failed to log audit event:', error);
    }
  }

  async logAuthentication(data: AuthenticationData): Promise<void> {
    const severity = data.action === 'LOGIN_FAILED' ? 'MEDIUM' : 'LOW';
    const description = data.action === 'LOGIN_FAILED' ? 'User login failed' : 'User login';

    await this.logAudit({
      userId: data.userId,
      userEmail: data.userEmail,
      action: data.action,
      resource: 'authentication',
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      sessionId: data.sessionId,
      severity,
      category: 'AUTHENTICATION',
      description,
      metadata: data.metadata,
    });
  }

  async logDataChange(data: DataChangeData): Promise<void> {
    const changedFields = data.action === 'UPDATE' && data.oldValues && data.newValues
      ? this.getChangedFields(data.oldValues, data.newValues)
      : [];

    await this.logAudit({
      userId: data.userId,
      userEmail: data.userEmail,
      userRole: data.userRole,
      action: data.action,
      resource: data.resource,
      resourceId: data.resourceId,
      oldValues: data.oldValues,
      newValues: data.newValues,
      changedFields,
      ipAddress: data.request ? this.getClientIP(data.request) : undefined,
      userAgent: data.request?.headers.get('user-agent') || undefined,
      requestId: data.request?.headers.get('x-request-id') || undefined,
      severity: this.determineSeverity(data.resource, data.action),
      category: 'DATA_CHANGE',
      metadata: data.metadata,
    });
  }

  async logApprovalAction(data: ApprovalActionData): Promise<void> {
    await this.logAudit({
      userId: data.userId,
      userEmail: data.userEmail,
      userRole: data.userRole,
      action: data.action,
      resource: 'approval',
      resourceId: data.resourceId,
      procurementId: data.procurementId,
      workflowStage: data.workflowStage,
      approvalStep: data.approvalStep,
      ipAddress: data.request ? this.getClientIP(data.request) : undefined,
      userAgent: data.request?.headers.get('user-agent') || undefined,
      severity: 'MEDIUM',
      category: 'APPROVAL_WORKFLOW',
      metadata: data.metadata,
    });
  }

  async logSecurityEvent(data: SecurityEventData): Promise<void> {
    await this.logAudit({
      userId: data.userId,
      userEmail: data.userEmail,
      action: data.action,
      resource: data.resource,
      severity: data.severity,
      category: 'SECURITY',
      description: data.description,
      ipAddress: data.request ? this.getClientIP(data.request) : undefined,
      userAgent: data.request?.headers.get('user-agent') || undefined,
      metadata: data.metadata,
    });
  }

  async searchAuditLogs(filters: AuditSearchFilters, page: number = 1, limit: number = 50) {
    const where: any = {};

    if (filters.userId) where.userId = filters.userId;
    if (filters.action) where.action = filters.action;
    if (filters.resource) where.resource = filters.resource;
    if (filters.severity) where.severity = filters.severity;
    if (filters.category) where.category = filters.category;
    if (filters.procurementId) where.procurementId = filters.procurementId;

    if (filters.startDate || filters.endDate) {
      where.timestamp = {};
      if (filters.startDate) where.timestamp.gte = filters.startDate;
      if (filters.endDate) where.timestamp.lte = filters.endDate;
    }

    if (filters.searchText) {
      where.OR = [
        { userEmail: { contains: filters.searchText, mode: 'insensitive' } },
        { description: { contains: filters.searchText, mode: 'insensitive' } },
        { resource: { contains: filters.searchText, mode: 'insensitive' } },
      ];
    }

    const [logs, totalCount] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    return {
      logs,
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
    };
  }

  async getAuditStatistics(filters?: AuditStatisticsFilters) {
    const where: any = {};

    if (filters?.startDate || filters?.endDate) {
      where.timestamp = {};
      if (filters.startDate) where.timestamp.gte = filters.startDate;
      if (filters.endDate) where.timestamp.lte = filters.endDate;
    }

    if (filters?.userId) where.userId = filters.userId;
    if (filters?.procurementId) where.procurementId = filters.procurementId;

    const [totalLogs, averageScore, actionStats, severityStats, categoryStats, userStats] = await Promise.all([
      prisma.auditLog.count({ where }),
      prisma.auditLog.count({ where }),
      prisma.auditLog.groupBy({
        by: ['action'],
        where,
        _count: { action: true },
      }),
      prisma.auditLog.groupBy({
        by: ['severity'],
        where,
        _count: { severity: true },
      }),
      prisma.auditLog.groupBy({
        by: ['category'],
        where,
        _count: { category: true },
      }),
      prisma.auditLog.groupBy({
        by: ['userEmail'],
        where,
        _count: { userEmail: true },
        orderBy: { _count: { userEmail: 'desc' } },
        take: 10,
      }),
    ]);

    return {
      totalLogs,
      averageScore: 0,
      actionStats,
      severityStats,
      categoryStats,
      userStats,
    };
  }

  async archiveOldLogs(retentionDays: number): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const oldLogs = await prisma.auditLog.findMany({
      where: {
        timestamp: { lt: cutoffDate },
      },
    });

    if (oldLogs.length === 0) {
      return 0;
    }

    // Archive logs
    await prisma.auditLogArchive.createMany({
      data: oldLogs.map((log: any) => ({
        originalId: log.id,
        userId: log.userId,
        userEmail: log.userEmail,
        userRole: log.userRole,
        action: log.action as any,
        resource: log.resource,
        resourceId: log.resourceId,
        entityType: log.entityType,
        entityId: log.entityId,
        oldValues: toPrismaJson(log.oldValues),
        newValues: toPrismaJson(log.newValues),
        changedFields: toPrismaJson(log.changedFields),
        details: toPrismaJson(log.details),
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        sessionId: log.sessionId,
        requestId: log.requestId,
        procurementId: log.procurementId,
        workflowStage: log.workflowStage,
        approvalStep: log.approvalStep,
        severity: log.severity,
        category: log.category,
        metadata: toPrismaJson(log.metadata),
        description: log.description,
        timestamp: log.timestamp,
        partitionDate: new Date(log.createdAt ? log.createdAt.getFullYear() : 2024, log.createdAt ? log.createdAt.getMonth() : 0, log.createdAt ? log.createdAt.getDate() : 1),
        archivedAt: new Date(),
        archiveReason: `Archived after ${retentionDays} days`,
      })),
    });

    // Delete original logs
    await prisma.auditLog.deleteMany({
      where: {
        timestamp: { lt: cutoffDate },
      },
    });

    return oldLogs.length;
  }

  async detectSuspiciousActivities(timeWindowHours: number = 24) {
    const since = new Date();
    since.setHours(since.getHours() - timeWindowHours);

    const [failedLogins, highVolumeUsers, afterHoursActions] = await Promise.all([
      // Multiple failed logins
      prisma.auditLog.groupBy({
        by: ['userEmail', 'ipAddress'],
        where: {
          action: 'LOGIN_FAILED',
          timestamp: { gte: since },
        },
        _count: { action: true },
        having: { action: { _count: { gte: 3 } } },
      }),
      // High volume activity
      prisma.auditLog.groupBy({
        by: ['userId'],
        where: {
          timestamp: { gte: since },
        },
        _count: { action: true },
        having: { action: { _count: { gte: 100 } } },
      }),
      // After hours critical actions
      prisma.auditLog.findMany({
        where: {
          severity: 'CRITICAL',
          timestamp: { gte: since },
        },
        take: 50,
      }),
    ]);

    const suspiciousActivities = [];

    // Add failed login attempts
    for (const login of failedLogins) {
      suspiciousActivities.push({
        type: 'MULTIPLE_FAILED_LOGINS',
        userEmail: login.userEmail,
        ipAddress: login.ipAddress,
        count: login._count.action,
      });
    }

    // Add high volume users
    for (const user of highVolumeUsers) {
      suspiciousActivities.push({
        type: 'HIGH_VOLUME_ACTIVITY',
        userId: user.userId,
        count: user._count.action,
      });
    }

    // Add after hours actions
    for (const action of afterHoursActions) {
      suspiciousActivities.push({
        type: 'AFTER_HOURS_CRITICAL_ACTION',
        ...action,
      });
    }

    return suspiciousActivities;
  }

  private getChangedFields(oldValues: any, newValues: any): string[] {
    const changed: string[] = [];
    
    for (const key in newValues) {
      if (oldValues[key] !== newValues[key]) {
        changed.push(key);
      }
    }
    
    return changed;
  }

  private getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    const realIP = request.headers.get('x-real-ip');
    if (realIP) {
      return realIP;
    }
    
    return 'unknown';
  }

  private determineSeverity(resource: string, action: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (resource === 'user' && action === 'DELETE') return 'CRITICAL';
    if (resource === 'approval' && ['APPROVE', 'REJECT'].includes(action)) return 'HIGH';
    if (resource === 'procurement' && action === 'CREATE') return 'MEDIUM';
    return 'LOW';
  }
}

// Export singleton instance
export const auditSystem = new ComprehensiveAuditSystem();
