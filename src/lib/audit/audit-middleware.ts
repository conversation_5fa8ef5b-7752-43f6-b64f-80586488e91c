import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';

import { auditSystem } from './comprehensive-audit-system';

export interface AuditOptions {
  resource: string;
  action?: string;
  description?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category?: 'GENERAL' | 'AUTHENTICATION' | 'AUTHORIZATION' | 'DATA_CHANGE' | 'APPROVAL_WORKFLOW' | 'SECURITY' | 'DOCUMENT_MANAGEMENT' | 'BAST_APPROVAL' | 'PROCUREMENT';
  includeRequestBody?: boolean;
  includeResponseBody?: boolean;
  sensitiveFields?: string[];
  skipAudit?: (req: NextRequest) => boolean;
}

export function auditCreate(resource: string, options?: Partial<AuditOptions>) {
  return function (handler: Function) {
    return async function (req: NextRequest, ...args: any[]) {
      const auditOptions: AuditOptions = {
        resource,
        action: 'CREATE',
        includeRequestBody: true,
        includeResponseBody: false,
        ...options,
      };

      // Skip audit if condition is met
      if (auditOptions.skipAudit && auditOptions.skipAudit(req)) {
        return handler(req, ...args);
      }

      const startTime = Date.now();
      let user: any = null;
      let error: any = null;
      let response: NextResponse | null = null;

      try {
        // Get current user for audit trail
        try {
          user = await getCurrentUser(req);
        } catch (e) {
          // Continue without user info if authentication fails
        }

        // Execute the handler
        response = await handler(req, ...args);

        // Log successful creation
        await logAuditEvent(req, response, user, auditOptions, startTime);

        return response;
      } catch (e) {
        error = e;
        
        // Log failed creation attempt
        await logAuditEvent(req, null, user, auditOptions, startTime, error);
        
        throw e;
      }
    };
  };
}

export function auditRead(resource: string, options?: Partial<AuditOptions>) {
  return function (handler: Function) {
    return async function (req: NextRequest, ...args: any[]) {
      const auditOptions: AuditOptions = {
        resource,
        action: 'READ',
        includeRequestBody: false,
        includeResponseBody: false,
        ...options,
      };

      // Skip audit if condition is met
      if (auditOptions.skipAudit && auditOptions.skipAudit(req)) {
        return handler(req, ...args);
      }

      const startTime = Date.now();
      let user: any = null;
      let error: any = null;
      let response: NextResponse | null = null;

      try {
        // Get current user for audit trail
        try {
          user = await getCurrentUser(req);
        } catch (e) {
          // Continue without user info if authentication fails
        }

        // Execute the handler
        response = await handler(req, ...args);

        // Log successful read (only for sensitive resources)
        if (isSensitiveResource(resource)) {
          await logAuditEvent(req, response, user, auditOptions, startTime);
        }

        return response;
      } catch (e) {
        error = e;
        
        // Log failed read attempt
        await logAuditEvent(req, null, user, auditOptions, startTime, error);
        
        throw e;
      }
    };
  };
}

export function auditUpdate(resource: string, options?: Partial<AuditOptions>) {
  return function (handler: Function) {
    return async function (req: NextRequest, ...args: any[]) {
      const auditOptions: AuditOptions = {
        resource,
        action: 'UPDATE',
        includeRequestBody: true,
        includeResponseBody: false,
        ...options,
      };

      // Skip audit if condition is met
      if (auditOptions.skipAudit && auditOptions.skipAudit(req)) {
        return handler(req, ...args);
      }

      const startTime = Date.now();
      let user: any = null;
      let error: any = null;
      let response: NextResponse | null = null;

      try {
        // Get current user for audit trail
        try {
          user = await getCurrentUser(req);
        } catch (e) {
          // Continue without user info if authentication fails
        }

        // Execute the handler
        response = await handler(req, ...args);

        // Log successful update
        await logAuditEvent(req, response, user, auditOptions, startTime);

        return response;
      } catch (e) {
        error = e;
        
        // Log failed update attempt
        await logAuditEvent(req, null, user, auditOptions, startTime, error);
        
        throw e;
      }
    };
  };
}

export function auditDelete(resource: string, options?: Partial<AuditOptions>) {
  return function (handler: Function) {
    return async function (req: NextRequest, ...args: any[]) {
      const auditOptions: AuditOptions = {
        resource,
        action: 'DELETE',
        includeRequestBody: false,
        includeResponseBody: false,
        ...options,
      };

      // Skip audit if condition is met
      if (auditOptions.skipAudit && auditOptions.skipAudit(req)) {
        return handler(req, ...args);
      }

      const startTime = Date.now();
      let user: any = null;
      let error: any = null;
      let response: NextResponse | null = null;

      try {
        // Get current user for audit trail
        try {
          user = await getCurrentUser(req);
        } catch (e) {
          // Continue without user info if authentication fails
        }

        // Execute the handler
        response = await handler(req, ...args);

        // Log successful deletion
        await logAuditEvent(req, response, user, auditOptions, startTime);

        return response;
      } catch (e) {
        error = e;
        
        // Log failed deletion attempt
        await logAuditEvent(req, null, user, auditOptions, startTime, error);
        
        throw e;
      }
    };
  };
}

async function logAuditEvent(
  req: NextRequest,
  response: NextResponse | null,
  user: any,
  options: AuditOptions,
  startTime: number,
  error?: any
) {
  try {
    const duration = Date.now() - startTime;
    const url = new URL(req.url);
    const resourceId = extractResourceId(url.pathname);

    let requestBody: any = null;
    let responseBody: any = null;

    // Extract request body if needed
    if (options.includeRequestBody && req.method !== 'GET') {
      try {
        // Create a clone of the request to avoid consuming the body
        const clonedRequest = new Request(req.url, {
          method: req.method,
          headers: req.headers,
          body: (req as any).body,
        });
        const body = await clonedRequest.text();
        if (body) {
          requestBody = JSON.parse(body);
          // Remove sensitive fields
          if (options.sensitiveFields) {
            requestBody = removeSensitiveFields(requestBody, options.sensitiveFields);
          }
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    // Extract response body if needed
    if (options.includeResponseBody && response) {
      try {
        // Create a clone of the response to avoid consuming the body
        const clonedResponse = response.clone();
        const body = await (clonedResponse as any).text();
        if (body) {
          responseBody = JSON.parse(body);
          // Remove sensitive fields
          if (options.sensitiveFields) {
            responseBody = removeSensitiveFields(responseBody, options.sensitiveFields);
          }
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    await auditSystem.logAudit({
      userId: user?.id,
      userEmail: user?.email,
      userRole: user?.roles?.[0],
      action: options.action || req.method,
      resource: options.resource,
      resourceId,
      ipAddress: getClientIP(req),
      userAgent: req.headers.get('user-agent') || undefined,
      sessionId: req.headers.get('x-session-id') || undefined,
      requestId: req.headers.get('x-request-id') || undefined,
      severity: error ? 'HIGH' : (options.severity || 'LOW'),
      category: (options.category as any) || 'DATA_CHANGE',
      description: error 
        ? `Failed ${options.action} on ${options.resource}: ${error.message}`
        : `Successful ${options.action} on ${options.resource}`,
      metadata: {
        method: req.method,
        url: req.url,
        statusCode: response?.status,
        duration,
        requestBody,
        responseBody,
        error: error ? {
          message: error.message,
          stack: error.stack,
        } : undefined,
      },
    });
  } catch (auditError) {
    // Log audit errors but don't fail the request
    console.error('Audit logging failed:', auditError);
  }
}

function extractResourceId(pathname: string): string | undefined {
  // Extract ID from common REST patterns
  const segments = pathname.split('/').filter(Boolean);
  
  // Look for UUID or numeric ID patterns
  for (let i = segments.length - 1; i >= 0; i--) {
    const segment = segments[i];
    
    // UUID pattern
    if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(segment)) {
      return segment;
    }
    
    // Numeric ID pattern
    if (/^\d+$/.test(segment)) {
      return segment;
    }
  }
  
  return undefined;
}

function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  const realIP = req.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

function removeSensitiveFields(obj: any, sensitiveFields: string[]): any {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const cleaned = { ...obj };
  
  for (const field of sensitiveFields) {
    if (field.includes('.')) {
      // Handle nested fields
      const parts = field.split('.');
      let current = cleaned;
      
      for (let i = 0; i < parts.length - 1; i++) {
        if (current[parts[i]]) {
          current = current[parts[i]];
        } else {
          break;
        }
      }
      
      if (current && current[parts[parts.length - 1]]) {
        current[parts[parts.length - 1]] = '[REDACTED]';
      }
    } else {
      // Handle top-level fields
      if (cleaned[field]) {
        cleaned[field] = '[REDACTED]';
      }
    }
  }
  
  return cleaned;
}

function isSensitiveResource(resource: string): boolean {
  const sensitiveResources = [
    'user',
    'vendor',
    'procurement',
    'approval',
    'payment',
    'contract',
  ];
  
  return sensitiveResources.includes(resource.toLowerCase());
}
