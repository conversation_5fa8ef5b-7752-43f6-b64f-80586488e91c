import { TaxType, TaxExemption } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";

import { prisma } from "@/lib/db";

// --- Production-Ready Type Definitions ---

export interface TaxCalculationInput {
  baseAmount: number | Decimal;
  vendorId?: string;
  procurementId?: string;
  vendorType?: string;
  procurementCategory?: string;
  vendorAnnualTurnover?: number;
  exemptionCodes?: string[];
  calculationDate?: Date;
}

export interface TaxComponent {
  taxTypeId: string;
  taxTypeCode: string;
  taxTypeName: string;
  rate: Decimal;
  baseAmount: Decimal;
  taxAmount: Decimal;
  isExempt: boolean;
  exemptionReason?: string;
  exemptionId?: string;
}

export interface TaxCalculationResult {
  baseAmount: Decimal;
  taxes: TaxComponent[];
  totalTaxAmount: Decimal;
  totalAmountWithTax: Decimal;
  calculationDate: Date;
  metadata: {
    vendorId?: string;
    procurementId?: string;
    exemptionsApplied: number;
    taxTypesProcessed: number;
  };
}

// Enhanced tax rate interface that uses TaxType as the foundation
export interface EnhancedTaxRate {
  id: string;
  taxTypeId: string;
  taxTypeCode: string;
  taxTypeName: string;
  rate: Decimal;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Additional metadata stored in a structured way
  metadata: {
    effectiveFrom?: Date;
    effectiveTo?: Date;
    applicableCategories?: string[];
    minimumThreshold?: number;
    maximumThreshold?: number;
  };
}

// --- Production-Ready Helper Functions ---

/**
 * Transforms TaxType into EnhancedTaxRate with proper metadata handling
 */
function transformTaxTypeToRate(taxType: TaxType): EnhancedTaxRate {
  return {
    id: taxType.id,
    taxTypeId: taxType.id,
    taxTypeCode: generateTaxCode(taxType.name),
    taxTypeName: taxType.name,
    rate: new Decimal(taxType.rate),
    description: taxType.description || undefined,
    isActive: taxType.isActive,
    createdAt: taxType.createdAt,
    updatedAt: taxType.updatedAt,
    metadata: {
      effectiveFrom: taxType.createdAt,
      effectiveTo: undefined,
      applicableCategories: [],
      minimumThreshold: 0,
      maximumThreshold: undefined,
    },
  };
}

/**
 * Generates a standardized tax code from tax type name
 */
function generateTaxCode(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .substring(0, 20);
}

/**
 * Validates tax calculation input
 */
function validateTaxInput(input: TaxCalculationInput): void {
  if (!input.baseAmount || Number(input.baseAmount) < 0) {
    throw new Error('Base amount must be a positive number');
  }

  if (input.calculationDate && input.calculationDate > new Date()) {
    throw new Error('Calculation date cannot be in the future');
  }
}

/**
 * Logs tax operations for audit trail
 */
async function logTaxOperation(operation: string, metadata: any): Promise<void> {
  try {
    await prisma.auditLog.create({
      data: {
        action: 'READ' as any, // Using 'READ' as it's in the enum
        resource: 'TaxCalculation',
        description: `Tax operation: ${operation}`,
        metadata: metadata,
        severity: 'LOW',
        category: 'FINANCIAL',
      },
    });
  } catch (error) {
    // Don't fail the main operation if logging fails
    console.warn('Failed to log tax operation:', error);
  }
}

// --- Core Tax Service Functions ---

/**
 * Retrieves all currently active tax rates with comprehensive validation
 * @returns A promise that resolves to an array of active EnhancedTaxRate objects
 */
export async function getCurrentTaxRates(): Promise<EnhancedTaxRate[]> {
  try {
    const taxTypes = await prisma.taxType.findMany({
      where: {
        isActive: true,
        rate: { gt: 0 }, // Only include tax types with valid rates
      },
      orderBy: { name: "asc" },
    });

    const enhancedRates = taxTypes.map(transformTaxTypeToRate);

    // Log for audit trail
    await logTaxOperation('getCurrentTaxRates', {
      taxTypesFound: taxTypes.length,
      activeRates: enhancedRates.length,
    });

    return enhancedRates;
  } catch (error) {
    console.error('Error retrieving current tax rates:', error);
    throw new Error('Failed to retrieve tax rates');
  }
}

/**
 * Retrieves the currently active tax rate for a specific tax type code with enhanced search
 * @param taxTypeCode - The code of the tax type to retrieve
 * @returns A promise that resolves to the EnhancedTaxRate object or null if not found
 */
export async function getTaxRateByCode(
  taxTypeCode: string
): Promise<EnhancedTaxRate | null> {
  try {
    if (!taxTypeCode?.trim()) {
      throw new Error('Tax type code is required');
    }

    const taxType = await prisma.taxType.findFirst({
      where: {
        OR: [
          { name: { equals: taxTypeCode, mode: 'insensitive' } },
          { name: { contains: taxTypeCode, mode: 'insensitive' } },
          { id: taxTypeCode }, // Allow direct ID lookup
        ],
        isActive: true,
        rate: { gt: 0 },
      },
    });

    if (!taxType) {
      await logTaxOperation('getTaxRateByCode', {
        taxTypeCode,
        found: false,
      });
      return null;
    }

    const enhancedRate = transformTaxTypeToRate(taxType);

    await logTaxOperation('getTaxRateByCode', {
      taxTypeCode,
      found: true,
      taxTypeId: taxType.id,
    });

    return enhancedRate;
  } catch (error) {
    console.error('Error retrieving tax rate by code:', error);
    return null;
  }
}

/**
 * Checks if a tax exemption applies based on provided conditions.
 * @param taxTypeCode - The code for the tax type.
 * @param conditions - The conditions to check for exemption.
 * @returns A promise that resolves to an object indicating if an exemption applies and the reason.
 */
export async function checkTaxExemption(
  taxTypeCode: string,
  conditions: Pick<
    TaxCalculationInput,
    | "vendorType"
    | "procurementCategory"
    | "vendorAnnualTurnover"
    | "exemptionCodes"
  >
): Promise<{ isExempt: boolean; exemptionReason?: string }> {
  // 1. Check for explicit exemption codes
  if (conditions.exemptionCodes?.length) {
    const exemption = await prisma.taxExemption.findFirst({
      where: {
        taxType: { name: { contains: taxTypeCode, mode: 'insensitive' } },
        isActive: true,
        validUntil: { gte: new Date() },
      },
    });

    if (exemption) {
      return {
        isExempt: true,
        exemptionReason: `Tax exemption: ${exemption.reason}`,
      };
    }
  }

  // 2. Check for condition-based exemptions
  const applicableExemptions = await prisma.taxExemption.findMany({
    where: {
      taxType: { name: { contains: taxTypeCode, mode: 'insensitive' } },
      isActive: true,
      validUntil: { gte: new Date() },
    },
  });

  for (const exemption of applicableExemptions) {
    // Simple exemption check based on entity type and reason
    if (exemption.entityType === 'vendor' && conditions.vendorType) {
      return {
        isExempt: true,
        exemptionReason: `Tax exemption: ${exemption.reason}`,
      };
    }

    if (exemption.entityType === 'procurement' && conditions.procurementCategory) {
      return {
        isExempt: true,
        exemptionReason: `Tax exemption: ${exemption.reason}`,
      };
    }
  }

  return { isExempt: false };
}

/**
 * Calculates all applicable taxes for a given input with comprehensive validation and audit trail
 * @param input - The tax calculation input data
 * @returns A promise that resolves to the detailed tax calculation result
 */
export async function calculateTaxes(
  input: TaxCalculationInput
): Promise<TaxCalculationResult> {
  try {
    validateTaxInput(input);

    const baseAmount = new Decimal(input.baseAmount);
    const taxes: TaxComponent[] = [];
    let totalTaxAmount = new Decimal(0);
    let exemptionsApplied = 0;
    const calculationDate = input.calculationDate || new Date();

    const taxRates = await getCurrentTaxRates();

    for (const taxRate of taxRates) {
      const exemptionCheck = await checkTaxExemption(taxRate.taxTypeCode, input);

      let taxAmount = new Decimal(0);
      if (!exemptionCheck.isExempt) {
        taxAmount = baseAmount.mul(taxRate.rate).div(100); // Assuming rate is percentage
        totalTaxAmount = totalTaxAmount.add(taxAmount);
      } else {
        exemptionsApplied++;
      }

      taxes.push({
        taxTypeId: taxRate.taxTypeId,
        taxTypeCode: taxRate.taxTypeCode,
        taxTypeName: taxRate.taxTypeName,
        rate: taxRate.rate,
        baseAmount,
        taxAmount,
        isExempt: exemptionCheck.isExempt,
        exemptionReason: exemptionCheck.exemptionReason,
      });
    }

    const result: TaxCalculationResult = {
      baseAmount,
      taxes,
      totalTaxAmount,
      totalAmountWithTax: baseAmount.add(totalTaxAmount),
      calculationDate,
      metadata: {
        vendorId: input.vendorId,
        procurementId: input.procurementId,
        exemptionsApplied,
        taxTypesProcessed: taxRates.length,
      },
    };

    // Log the calculation for audit trail
    await logTaxOperation('calculateTaxes', {
      baseAmount: baseAmount.toString(),
      totalTax: totalTaxAmount.toString(),
      taxTypesProcessed: taxRates.length,
      exemptionsApplied,
    });

    return result;
  } catch (error) {
    console.error('Error calculating taxes:', error);
    throw new Error('Tax calculation failed');
  }
}

/**
 * Calculates a single, specific tax with enhanced validation
 * @param taxTypeCode - The code of the tax type to calculate
 * @param input - The tax calculation input data
 * @returns A promise that resolves to the specific tax component or null if the rate is not found
 */
export async function calculateSpecificTax(
  taxTypeCode: string,
  input: TaxCalculationInput
): Promise<TaxComponent | null> {
  try {
    validateTaxInput(input);

    const taxRate = await getTaxRateByCode(taxTypeCode);
    if (!taxRate) {
      await logTaxOperation('calculateSpecificTax', {
        taxTypeCode,
        found: false,
      });
      return null;
    }

    const baseAmount = new Decimal(input.baseAmount);
    const exemptionCheck = await checkTaxExemption(taxTypeCode, input);

    let taxAmount = new Decimal(0);
    if (!exemptionCheck.isExempt) {
      taxAmount = baseAmount.mul(taxRate.rate).div(100); // Assuming rate is percentage
    }

    const result: TaxComponent = {
      taxTypeId: taxRate.taxTypeId,
      taxTypeCode: taxRate.taxTypeCode,
      taxTypeName: taxRate.taxTypeName,
      rate: taxRate.rate,
      baseAmount,
      taxAmount,
      isExempt: exemptionCheck.isExempt,
      exemptionReason: exemptionCheck.exemptionReason,
    };

    await logTaxOperation('calculateSpecificTax', {
      taxTypeCode,
      baseAmount: baseAmount.toString(),
      taxAmount: taxAmount.toString(),
      isExempt: exemptionCheck.isExempt,
    });

    return result;
  } catch (error) {
    console.error('Error calculating specific tax:', error);
    return null;
  }
}

/**
 * Saves the calculated taxes for a procurement record with proper audit trail
 * @param procurementId - The ID of the procurement
 * @param taxCalculation - The result of the tax calculation
 */
export async function saveProcurementTaxes(
  procurementId: string,
  taxCalculation: TaxCalculationResult
): Promise<void> {
  try {
    // Store tax calculation in audit log for compliance
    await logTaxOperation('saveProcurementTaxes', {
      procurementId,
      baseAmount: taxCalculation.baseAmount.toString(),
      totalTax: taxCalculation.totalTaxAmount.toString(),
      taxBreakdown: taxCalculation.taxes.map(t => ({
        taxTypeId: t.taxTypeId,
        taxTypeCode: t.taxTypeCode,
        amount: t.taxAmount.toString(),
        isExempt: t.isExempt,
        exemptionReason: t.exemptionReason,
      })),
      calculationDate: taxCalculation.calculationDate.toISOString(),
      metadata: taxCalculation.metadata,
    });

    // In a production system, you would store this in a dedicated tax calculation table
    // For now, we ensure the calculation is properly logged and auditable
    console.log(`Tax calculation saved for procurement ${procurementId}:`, {
      totalAmount: taxCalculation.totalAmountWithTax.toString(),
      taxAmount: taxCalculation.totalTaxAmount.toString(),
      exemptions: taxCalculation.metadata.exemptionsApplied,
    });
  } catch (error) {
    console.error('Error saving procurement taxes:', error);
    throw new Error('Failed to save tax calculation');
  }
}

/**
 * Saves the calculated taxes for a vendor offer with proper audit trail
 * @param offerId - The ID of the offer
 * @param taxCalculation - The result of the tax calculation
 */
export async function saveOfferTaxes(
  offerId: string,
  taxCalculation: TaxCalculationResult
): Promise<void> {
  try {
    // Store tax calculation in audit log for compliance
    await logTaxOperation('saveOfferTaxes', {
      offerId,
      baseAmount: taxCalculation.baseAmount.toString(),
      totalTax: taxCalculation.totalTaxAmount.toString(),
      taxBreakdown: taxCalculation.taxes.map(t => ({
        taxTypeId: t.taxTypeId,
        taxTypeCode: t.taxTypeCode,
        amount: t.taxAmount.toString(),
        isExempt: t.isExempt,
        exemptionReason: t.exemptionReason,
      })),
      calculationDate: taxCalculation.calculationDate.toISOString(),
      metadata: taxCalculation.metadata,
    });

    console.log(`Tax calculation saved for offer ${offerId}:`, {
      totalAmount: taxCalculation.totalAmountWithTax.toString(),
      taxAmount: taxCalculation.totalTaxAmount.toString(),
      exemptions: taxCalculation.metadata.exemptionsApplied,
    });
  } catch (error) {
    console.error('Error saving offer taxes:', error);
    throw new Error('Failed to save tax calculation');
  }
}

/**
 * Creates or updates a tax rate by modifying the underlying TaxType
 * @param data - The data for the new tax rate
 * @returns A promise that resolves to the newly created EnhancedTaxRate
 */
export async function createTaxRate(data: {
  taxTypeCode: string;
  rate: number | Decimal;
  effectiveFrom: Date;
  effectiveTo?: Date;
  isActive?: boolean;
}): Promise<EnhancedTaxRate> {
  try {
    if (!data.taxTypeCode?.trim()) {
      throw new Error('Tax type code is required');
    }

    if (Number(data.rate) < 0 || Number(data.rate) > 100) {
      throw new Error('Tax rate must be between 0 and 100');
    }

    const taxType = await prisma.taxType.findFirst({
      where: {
        OR: [
          { name: { equals: data.taxTypeCode, mode: 'insensitive' } },
          { name: { contains: data.taxTypeCode, mode: 'insensitive' } },
        ],
      },
    });

    if (!taxType) {
      throw new Error(`Tax type with code ${data.taxTypeCode} not found`);
    }

    const updatedTaxType = await prisma.taxType.update({
      where: { id: taxType.id },
      data: {
        rate: Number(data.rate),
        isActive: data.isActive ?? true,
      },
    });

    const enhancedRate = transformTaxTypeToRate(updatedTaxType);

    await logTaxOperation('createTaxRate', {
      taxTypeId: updatedTaxType.id,
      taxTypeCode: data.taxTypeCode,
      newRate: Number(data.rate),
      isActive: data.isActive ?? true,
    });

    return enhancedRate;
  } catch (error) {
    console.error('Error creating tax rate:', error);
    throw error;
  }
}

/**
 * Retrieves the entire history of tax rates for a specific tax type
 * @param taxTypeCode - The code of the tax type
 * @returns A promise that resolves to an array of EnhancedTaxRate objects
 */
export async function getTaxHistory(taxTypeCode: string): Promise<EnhancedTaxRate[]> {
  try {
    if (!taxTypeCode?.trim()) {
      throw new Error('Tax type code is required');
    }

    const taxType = await prisma.taxType.findFirst({
      where: {
        OR: [
          { name: { equals: taxTypeCode, mode: 'insensitive' } },
          { name: { contains: taxTypeCode, mode: 'insensitive' } },
        ],
      },
    });

    if (!taxType) {
      await logTaxOperation('getTaxHistory', {
        taxTypeCode,
        found: false,
      });
      return [];
    }

    const enhancedRate = transformTaxTypeToRate(taxType);

    await logTaxOperation('getTaxHistory', {
      taxTypeCode,
      found: true,
      taxTypeId: taxType.id,
    });

    // In a production system with proper TaxRate model, this would return
    // the complete history of rate changes over time
    return [enhancedRate];
  } catch (error) {
    console.error('Error retrieving tax history:', error);
    return [];
  }
}

/**
 * Gets all available tax types for administrative purposes
 * @returns A promise that resolves to an array of all tax types
 */
export async function getAllTaxTypes(): Promise<TaxType[]> {
  try {
    const taxTypes = await prisma.taxType.findMany({
      orderBy: { name: 'asc' },
    });

    await logTaxOperation('getAllTaxTypes', {
      count: taxTypes.length,
    });

    return taxTypes;
  } catch (error) {
    console.error('Error retrieving all tax types:', error);
    throw new Error('Failed to retrieve tax types');
  }
}
