import crypto from 'crypto';
import { existsSync } from 'fs';
import { writeFile, unlink, mkdir } from 'fs/promises';
import path from 'path';

import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { NextRequest } from 'next/server';

import { prisma } from '@/lib/db';
import { validatePublicAssetFile, scanFileContent } from '@/lib/security/public-asset-security';

export interface PublicAssetUploadOptions {
  category: string;
  title?: string;
  description?: string;
  tags?: string[];
  isActive?: boolean;
  expiresAt?: Date;
}

export interface StorageConfig {
  type: 'local' | 's3';
  local?: {
    uploadPath: string;
    publicUrl: string;
  };
  s3?: {
    endpoint?: string; // For S3-compatible services like R2
    region: string;
    bucket: string;
    accessKeyId: string;
    secretAccessKey: string;
    publicUrl?: string; // Custom domain for public access
    forcePathStyle?: boolean; // For S3-compatible services
  };
}

// Storage configuration from environment variables
const getStorageConfig = (): StorageConfig => {
  const storageType = process.env.STORAGE_TYPE as 'local' | 's3' || 'local';

  if (storageType === 's3') {
    return {
      type: 's3',
      s3: {
        endpoint: process.env.S3_ENDPOINT, // For R2: https://accountid.r2.cloudflarestorage.com
        region: process.env.S3_REGION || 'auto',
        bucket: process.env.S3_BUCKET || 'public-assets',
        accessKeyId: process.env.S3_ACCESS_KEY_ID!,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
        publicUrl: process.env.S3_PUBLIC_URL, // Custom domain if available
        forcePathStyle: process.env.S3_FORCE_PATH_STYLE === 'true',
      },
    };
  }

  return {
    type: 'local',
    local: {
      uploadPath: process.env.LOCAL_UPLOAD_PATH || './public/uploads',
      publicUrl: process.env.LOCAL_PUBLIC_URL || '/uploads',
    },
  };
};

const storageConfig = getStorageConfig();

export interface PublicAssetData {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  category: string;
  title?: string;
  description?: string;
  tags: string[];
  url: string;
  isActive: boolean;
  uploadedById: string;
  uploadedAt: Date;
  expiresAt?: Date;
}

// Initialize S3 client if using S3 storage
const getS3Client = (): S3Client | null => {
  if (storageConfig.type !== 's3' || !storageConfig.s3) {
    return null;
  }

  const config = storageConfig.s3;
  return new S3Client({
    endpoint: config.endpoint,
    region: config.region,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
    },
    forcePathStyle: config.forcePathStyle,
  });
};

const s3Client = getS3Client();

// Generate secure filename
const generateSecureFilename = (originalName: string, category: string): string => {
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(8).toString('hex');
  const extension = path.extname(originalName);
  const baseName = path.basename(originalName, extension).replace(/[^a-zA-Z0-9]/g, '_');

  return `${category}/${timestamp}_${randomBytes}_${baseName}${extension}`;
};

// Upload file to local storage
const uploadToLocal = async (file: File, filename: string): Promise<string> => {
  if (!storageConfig.local) {
    throw new Error('Local storage not configured');
  }

  const uploadPath = storageConfig.local.uploadPath;
  const fullPath = path.join(uploadPath, filename);
  const directory = path.dirname(fullPath);

  // Ensure directory exists
  if (!existsSync(directory)) {
    await mkdir(directory, { recursive: true });
  }

  // Convert File to Buffer
  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  // Write file
  await writeFile(fullPath, buffer);

  // Return public URL
  return `${storageConfig.local.publicUrl}/${filename}`;
};

// Upload file to S3-compatible storage
const uploadToS3 = async (file: File, filename: string): Promise<string> => {
  if (!s3Client || !storageConfig.s3) {
    throw new Error('S3 storage not configured');
  }

  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  const command = new PutObjectCommand({
    Bucket: storageConfig.s3.bucket,
    Key: filename,
    Body: buffer,
    ContentType: file.type,
    ContentLength: file.size,
    // Make object publicly readable
    ACL: 'public-read',
  });

  await s3Client.send(command);

  // Return public URL
  if (storageConfig.s3.publicUrl) {
    return `${storageConfig.s3.publicUrl}/${filename}`;
  } else if (storageConfig.s3.endpoint) {
    return `${storageConfig.s3.endpoint}/${storageConfig.s3.bucket}/${filename}`;
  } else {
    return `https://${storageConfig.s3.bucket}.s3.${storageConfig.s3.region}.amazonaws.com/${filename}`;
  }
};

// Delete file from local storage
const deleteFromLocal = async (filename: string): Promise<void> => {
  if (!storageConfig.local) {
    throw new Error('Local storage not configured');
  }

  const fullPath = path.join(storageConfig.local.uploadPath, filename);

  try {
    await unlink(fullPath);
  } catch (error) {
    // File might not exist, which is fine
    console.warn(`Failed to delete local file: ${fullPath}`, error);
  }
};

// Delete file from S3-compatible storage
const deleteFromS3 = async (filename: string): Promise<void> => {
  if (!s3Client || !storageConfig.s3) {
    throw new Error('S3 storage not configured');
  }

  const command = new DeleteObjectCommand({
    Bucket: storageConfig.s3.bucket,
    Key: filename,
  });

  try {
    await s3Client.send(command);
  } catch (error) {
    console.warn(`Failed to delete S3 file: ${filename}`, error);
  }
};

// Upload a public asset
export async function uploadPublicAsset(
  file: File,
  options: PublicAssetUploadOptions,
  uploadedById: string
): Promise<PublicAssetData> {
  // Validate the file
  const validation = validatePublicAssetFile(file);
  if (!validation.valid) {
    throw new Error(validation.error);
  }

  // Scan file content for security threats
  const scanResult = await scanFileContent(file);
  if (!scanResult.safe) {
    throw new Error(`File security scan failed: ${scanResult.threats?.join(', ')}`);
  }

  // Generate secure filename
  const filename = generateSecureFilename(file.name, options.category);

  let fileUrl: string;

  try {
    // Upload to configured storage
    if (storageConfig.type === 's3') {
      fileUrl = await uploadToS3(file, filename);
    } else {
      fileUrl = await uploadToLocal(file, filename);
    }

    // Save metadata to database
    const asset = await prisma.publicAsset.create({
      data: {
        fileName: filename,
        originalFileName: file.name,
        filePath: filename,
        fileType: file.type as any,
        mimeType: file.type,
        size: file.size,
        category: options.category,
        title: options.title,
        description: options.description,
        tags: options.tags || [],
        url: fileUrl,
        isActive: options.isActive ?? true,
        uploadedById,
        expiresAt: options.expiresAt,
      },
    });

    return {
      id: asset.id,
      filename: asset.fileName,
      originalName: asset.originalFileName,
      mimeType: asset.mimeType,
      size: asset.size || 0,
      category: asset.category || "",
      title: asset.title || undefined,
      description: asset.description || undefined,
      tags: asset.tags as string[],
      url: asset.url || "",
      isActive: asset.isActive,
      uploadedById: asset.uploadedById,
      uploadedAt: asset.createdAt,
      expiresAt: asset.expiresAt || undefined,
    };
  } catch (error) {
    // If upload failed, clean up any partial uploads
    try {
      if (storageConfig.type === 's3') {
        await deleteFromS3(filename);
      } else {
        await deleteFromLocal(filename);
      }
    } catch (cleanupError) {
      console.warn('Failed to cleanup after upload error:', cleanupError);
    }

    throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Get public assets by category
export async function getPublicAssetsByCategory(
  category: string,
  includeInactive: boolean = false
): Promise<PublicAssetData[]> {
  const where: any = { category };
  
  if (!includeInactive) {
    where.isActive = true;
    where.OR = [
      { expiresAt: null },
      { expiresAt: { gt: new Date() } },
    ];
  }

  const assets = await prisma.publicAsset.findMany({
    where,
    orderBy: { createdAt: 'desc' },
  });

  return assets.map((asset: any) => ({
    id: asset.id,
    filename: asset.filename,
    originalName: asset.originalName,
    mimeType: asset.mimeType,
    size: asset.size,
    category: asset.category,
    title: asset.title || undefined,
    description: asset.description || undefined,
    tags: asset.tags as string[],
    url: asset.url,
    isActive: asset.isActive,
    uploadedById: asset.uploadedById,
    uploadedAt: asset.createdAt,
    expiresAt: asset.expiresAt || undefined,
  }));
}

// Get a specific public asset
export async function getPublicAsset(id: string): Promise<PublicAssetData | null> {
  const asset = await prisma.publicAsset.findUnique({
    where: { id },
  });

  if (!asset) return null;

  return {
    id: asset.id,
    filename: asset.fileName,
    originalName: asset.originalFileName,
    mimeType: asset.mimeType,
    size: asset.size || 0,
    category: asset.category || "",
    title: asset.title || undefined,
    description: asset.description || undefined,
    tags: asset.tags as string[],
    url: asset.url || "",
    isActive: asset.isActive,
    uploadedById: asset.uploadedById,
    uploadedAt: asset.createdAt,
    expiresAt: asset.expiresAt || undefined,
  };
}

// Update public asset metadata
export async function updatePublicAsset(
  id: string,
  updates: Partial<Pick<PublicAssetData, 'title' | 'description' | 'tags' | 'isActive' | 'expiresAt'>>
): Promise<PublicAssetData> {
  const asset = await prisma.publicAsset.update({
    where: { id },
    data: {
      title: updates.title,
      description: updates.description,
      tags: updates.tags,
      isActive: updates.isActive,
      expiresAt: updates.expiresAt,
    },
  });

  return {
    id: asset.id,
    filename: asset.fileName,
    originalName: asset.originalFileName,
    mimeType: asset.mimeType,
    size: asset.size || 0,
    category: asset.category || "",
    title: asset.title || undefined,
    description: asset.description || undefined,
    tags: asset.tags as string[],
    url: asset.url || "",
    isActive: asset.isActive,
    uploadedById: asset.uploadedById,
    uploadedAt: asset.createdAt,
    expiresAt: asset.expiresAt || undefined,
  };
}

// Delete a public asset
export async function deletePublicAsset(id: string): Promise<void> {
  // Get asset info first
  const asset = await prisma.publicAsset.findUnique({
    where: { id },
  });

  if (!asset) {
    throw new Error('Asset not found');
  }

  try {
    // Delete from storage
    if (storageConfig.type === 's3') {
      await deleteFromS3(asset.fileName);
    } else {
      await deleteFromLocal(asset.fileName);
    }
  } catch (error) {
    console.warn(`Failed to delete file from storage: ${asset.fileName}`, error);
    // Continue with database deletion even if file deletion fails
  }

  // Delete from database
  await prisma.publicAsset.delete({
    where: { id },
  });
}

// Search public assets
export async function searchPublicAssets(query: {
  search?: string;
  category?: string;
  tags?: string[];
  isActive?: boolean;
  limit?: number;
  offset?: number;
}): Promise<{ assets: PublicAssetData[]; total: number }> {
  const where: any = {};

  if (query.category) {
    where.category = query.category;
  }

  if (query.isActive !== undefined) {
    where.isActive = query.isActive;
  }

  if (query.tags && query.tags.length > 0) {
    where.tags = {
      hasEvery: query.tags,
    };
  }

  if (query.search) {
    where.OR = [
      { title: { contains: query.search, mode: 'insensitive' } },
      { description: { contains: query.search, mode: 'insensitive' } },
      { originalFileName: { contains: query.search, mode: 'insensitive' } },
    ];
  }

  const [assets, total] = await Promise.all([
    prisma.publicAsset.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: query.limit || 20,
      skip: query.offset || 0,
    }),
    prisma.publicAsset.count({ where }),
  ]);

  return {
    assets: assets.map((asset: any) => ({
      id: asset.id,
      filename: asset.fileName,
      originalName: asset.originalFileName,
      mimeType: asset.mimeType,
      size: asset.size || 0,
      category: asset.category || "",
      title: asset.title || undefined,
      description: asset.description || undefined,
      tags: asset.tags as string[],
      url: asset.url || "",
      isActive: asset.isActive,
      uploadedById: asset.uploadedById,
      uploadedAt: asset.createdAt,
      expiresAt: asset.expiresAt || undefined,
    })),
    total,
  };
}

// Get asset statistics
export async function getPublicAssetStats(): Promise<{
  totalAssets: number;
  activeAssets: number;
  totalSize: number;
  categoryCounts: Record<string, number>;
}> {
  const [totalAssets, activeAssets, assets] = await Promise.all([
    prisma.publicAsset.count(),
    prisma.publicAsset.count({ where: { isActive: true } }),
    prisma.publicAsset.findMany({
      select: {
        size: true,
        category: true,
      },
    }),
  ]);

  const totalSize = assets.reduce((sum: number, asset: any) => sum + asset.size, 0);
  const categoryCounts: Record<string, number> = {};

  assets.forEach((asset: any) => {
    categoryCounts[asset.category] = (categoryCounts[asset.category] || 0) + 1;
  });

  return {
    totalAssets,
    activeAssets,
    totalSize,
    categoryCounts,
  };
}

// Get storage configuration info
export function getStorageInfo(): {
  type: string;
  configured: boolean;
  config: Partial<StorageConfig>;
} {
  return {
    type: storageConfig.type,
    configured: storageConfig.type === 's3' ? !!storageConfig.s3?.accessKeyId : !!storageConfig.local?.uploadPath,
    config: {
      type: storageConfig.type,
      local: storageConfig.local ? {
        uploadPath: storageConfig.local.uploadPath,
        publicUrl: storageConfig.local.publicUrl,
      } : undefined,
      s3: storageConfig.s3 ? {
        endpoint: storageConfig.s3.endpoint,
        region: storageConfig.s3.region,
        bucket: storageConfig.s3.bucket,
        accessKeyId: '[HIDDEN]',
        secretAccessKey: '[HIDDEN]',
        publicUrl: storageConfig.s3.publicUrl,
        forcePathStyle: storageConfig.s3.forcePathStyle,
        // Don't expose actual credentials
      } : undefined,
    },
  };
}

// Test storage connection
export async function testStorageConnection(): Promise<{ success: boolean; error?: string }> {
  try {
    if (storageConfig.type === 's3') {
      if (!s3Client || !storageConfig.s3) {
        return { success: false, error: 'S3 client not configured' };
      }

      // Test by trying to list objects (this will fail if credentials are wrong)
      const command = new GetObjectCommand({
        Bucket: storageConfig.s3.bucket,
        Key: 'test-connection', // This file doesn't need to exist
      });

      try {
        await s3Client.send(command);
      } catch (error: any) {
        // If it's a NoSuchKey error, that means we can connect but the file doesn't exist (which is fine)
        if (error.name === 'NoSuchKey') {
          return { success: true };
        }
        // Other errors indicate connection problems
        return { success: false, error: error.message };
      }

      return { success: true };
    } else {
      // Test local storage by checking if directory exists or can be created
      if (!storageConfig.local) {
        return { success: false, error: 'Local storage not configured' };
      }

      const uploadPath = storageConfig.local.uploadPath;
      if (!existsSync(uploadPath)) {
        try {
          await mkdir(uploadPath, { recursive: true });
        } catch (error: any) {
          return { success: false, error: `Cannot create upload directory: ${error.message}` };
        }
      }

      return { success: true };
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

// Clean up expired assets
export async function cleanupExpiredAssets(): Promise<{ deletedCount: number; errors: string[] }> {
  const now = new Date();
  const expiredAssets = await prisma.publicAsset.findMany({
    where: {
      expiresAt: {
        lt: now,
      },
    },
  });

  let deletedCount = 0;
  const errors: string[] = [];

  for (const asset of expiredAssets) {
    try {
      await deletePublicAsset(asset.id);
      deletedCount++;
    } catch (error: any) {
      errors.push(`Failed to delete ${asset.fileName}: ${error.message}`);
    }
  }

  return { deletedCount, errors };
}

// Class-based storage service for backward compatibility
export class PublicAssetStorage {
  async uploadAsset(
    file: File,
    options: PublicAssetUploadOptions,
    uploadedById: string
  ): Promise<PublicAssetData> {
    return uploadPublicAsset(file, options, uploadedById);
  }

  async listPublicAssets(category?: string): Promise<PublicAssetData[]> {
    if (category) {
      return getPublicAssetsByCategory(category);
    }

    // Get all assets if no category specified
    const result = await searchPublicAssets({ isActive: true });
    return result.assets;
  }

  async getAsset(id: string): Promise<PublicAssetData | null> {
    return getPublicAsset(id);
  }

  async updateAsset(
    id: string,
    updates: Partial<Pick<PublicAssetData, 'title' | 'description' | 'tags' | 'isActive' | 'expiresAt'>>
  ): Promise<PublicAssetData> {
    return updatePublicAsset(id, updates);
  }

  async deleteAsset(id: string): Promise<void> {
    return deletePublicAsset(id);
  }

  async searchAssets(query: {
    search?: string;
    category?: string;
    tags?: string[];
    isActive?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<{ assets: PublicAssetData[]; total: number }> {
    return searchPublicAssets(query);
  }

  async getStats(): Promise<{
    totalAssets: number;
    activeAssets: number;
    totalSize: number;
    categoryCounts: Record<string, number>;
  }> {
    return getPublicAssetStats();
  }

  getStorageInfo(): {
    type: string;
    configured: boolean;
    config: Partial<StorageConfig>;
  } {
    return getStorageInfo();
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    return testStorageConnection();
  }

  async cleanupExpired(): Promise<{ deletedCount: number; errors: string[] }> {
    return cleanupExpiredAssets();
  }
}

// Export singleton instance for backward compatibility
export const publicAssetStorage = new PublicAssetStorage();
