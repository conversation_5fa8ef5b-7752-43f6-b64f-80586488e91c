import { clsx, type ClassValue } from "clsx"
import { format } from "date-fns"
import { id as idLocale } from "date-fns/locale"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number, currency: string = 'IDR', locale: string = 'id-ID'): string {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (error) {
    console.warn('Error formatting currency:', error);
    return `Rp ${formatNumber(amount)}`;
  }
}

export function formatDate(date: Date | string | null | undefined, formatStr: string = 'dd MMMM yyyy'): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) return 'Invalid Date';

    return format(dateObj, formatStr, { locale: idLocale });
  } catch (error) {
    console.warn('Error formatting date:', error);
    return 'Invalid Date';
  }
}

export function formatNumber(num: number, decimals?: number, locale: string = 'id-ID'): string {
  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(num);
  } catch (error) {
    console.warn('Error formatting number:', error);
    return num.toString();
  }
}

export function generateId(prefix?: string, length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return prefix ? `${prefix}_${result}` : result;
}

export function validateEmail(email: string | null | undefined): boolean {
  if (!email || typeof email !== 'string') return false;

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

export function validatePhone(phone: string | null | undefined): boolean {
  if (!phone || typeof phone !== 'string') return false;

  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');

  // Check if it's a valid length (8-15 digits)
  if (cleanPhone.length < 8 || cleanPhone.length > 15) return false;

  // Check if it starts with valid Indonesian prefixes or international format
  const validPrefixes = /^(\+62|62|0)/;
  return validPrefixes.test(phone.trim());
}
