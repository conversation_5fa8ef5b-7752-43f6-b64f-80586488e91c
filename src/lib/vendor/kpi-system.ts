import type { VendorIssue as PrismaVendorIssue, KpiCategory, Prisma, ApprovalStatus } from '@prisma/client';

import { prisma } from '@/lib/db';

export interface KPITemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  metrics: KPIMetric[];
  weightings: Record<string, number>;
  thresholds: Record<string, { min: number; max: number; target: number }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface KPIMetric {
  id: string;
  name: string;
  description?: string;
  type: 'PERCENTAGE' | 'NUMBER' | 'CURRENCY' | 'BOOLEAN' | 'RATING';
  unit?: string;
  weight: number;
  calculation: 'SUM' | 'AVERAGE' | 'COUNT' | 'PERCENTAGE' | 'CUSTOM';
  formula?: string;
  isRequired: boolean;
}

export interface VendorEvaluation {
  id: string;
  vendorId: string;
  templateId: string;
  evaluationPeriod: {
    startDate: Date;
    endDate: Date;
  };
  scores: Record<string, number>;
  overallScore: number;
  ranking: number;
  status: 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED';
  evaluatedBy: string;
  evaluatedAt: Date;
  comments?: string;
  metadata?: Record<string, unknown>;
}

// Additional interfaces for better type safety
export interface MetricScore {
  metricId: string;
  score: number;
  weight: number;
}

export interface PurchaseOrder {
  id: string;
  status: ApprovalStatus;
  totalValue: number;
  createdAt: Date;
  deliveryDate?: Date;
  expectedDeliveryDate?: Date;
}

export interface VendorIssue {
  id: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
  createdAt: Date;
  resolvedAt?: Date;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface VendorInfo {
  id: string;
  companyName: string;
  contactEmail: string;
  status: 'PENDING_VERIFICATION' | 'VERIFIED' | 'REJECTED' | 'SUSPENDED' | 'BLACKLISTED';
}

export interface VendorPerformanceData {
  vendorId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  metrics: {
    deliveryPerformance: number;
    qualityScore: number;
    costEffectiveness: number;
    responsiveness: number;
    compliance: number;
    innovation: number;
  };
  transactions: {
    totalOrders: number;
    completedOrders: number;
    cancelledOrders: number;
    totalValue: number;
    averageOrderValue: number;
  };
  issues: {
    totalIssues: number;
    resolvedIssues: number;
    averageResolutionTime: number;
  };
}

export interface VendorRanking {
  vendorId: string;
  vendorName: string;
  overallScore: number;
  ranking: number;
  category: string;
  evaluationCount: number;
  lastEvaluationDate: Date;
  trend: 'IMPROVING' | 'STABLE' | 'DECLINING';
  strengths: string[];
  weaknesses: string[];
}

export class VendorKPISystem {
  async createKPITemplate(templateData: {
    name: string;
    description?: string;
    category: string;
    metrics: Array<{
      name: string;
      description?: string;
      type: string;
      weight: number;
      calculation: string;
      isRequired: boolean;
    }>;
    createdById: string;
  }): Promise<KPITemplate> {
    // Validate metric weights sum to 100
    if (!this.validateMetricWeights(templateData.metrics)) {
      throw new Error('Total metric weights must equal 100%');
    }

    // Prepare metrics and weights as JSON
    const metricsJson = templateData.metrics.map((metric, index) => ({
      id: `metric_${index}`,
      name: metric.name,
      description: metric.description,
      type: metric.type,
      calculation: metric.calculation,
      isRequired: metric.isRequired,
    }));

    const weightsJson = templateData.metrics.reduce((acc: Record<string, number>, metric, index) => {
      acc[`metric_${index}`] = metric.weight;
      return acc;
    }, {});

    const template = await prisma.vendorKpiTemplate.create({
      data: {
        name: templateData.name,
        description: templateData.description,
        category: templateData.category as KpiCategory,
        metrics: metricsJson,
        weights: weightsJson,
        thresholds: {
          excellent: { min: 90, max: 100 },
          good: { min: 75, max: 89 },
          fair: { min: 60, max: 74 },
          poor: { min: 0, max: 59 },
        },
        isActive: true,
        createdById: templateData.createdById,
      },
    });

    // Map database result to KPITemplate interface
    const metrics = Array.isArray(template.metrics)
      ? (template.metrics as unknown as KPIMetric[])
      : [];

    const weightings = typeof template.weights === 'object' && template.weights !== null
      ? (template.weights as Record<string, number>)
      : {};

    const thresholds = typeof template.thresholds === 'object' && template.thresholds !== null
      ? (template.thresholds as Record<string, { min: number; max: number; target: number }>)
      : {};

    return {
      id: template.id,
      name: template.name,
      description: template.description || undefined,
      category: template.category,
      metrics,
      weightings,
      thresholds,
      isActive: template.isActive,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };
  }

  async evaluateVendorKPI(evaluationData: {
    vendorId: string;
    templateId: string;
    evaluatedById: string;
    scores: Array<{
      metricId: string;
      score: number;
    }>;
    comments?: string;
  }): Promise<VendorEvaluation> {
    // Get template with metrics
    const template = await prisma.vendorKpiTemplate.findUnique({
      where: { id: evaluationData.templateId },
    });

    if (!template) {
      throw new Error('KPI template not found');
    }

    // Parse metrics and weights from JSON
    const metrics = Array.isArray(template.metrics) ? template.metrics as unknown as KPIMetric[] : [];
    const weights = typeof template.weights === 'object' && template.weights !== null
      ? template.weights as Record<string, number>
      : {};

    // Validate all metrics are scored
    const scoredMetricIds = evaluationData.scores.map(s => s.metricId);
    const requiredMetricIds = metrics.filter(m => m.isRequired).map(m => m.id);
    const missingMetrics = requiredMetricIds.filter(id => !scoredMetricIds.includes(id));

    if (missingMetrics.length > 0) {
      throw new Error('All required metrics must be scored');
    }

    // Calculate weighted overall score
    const overallScore = this.calculateWeightedScore(
      evaluationData.scores.map(s => ({
        score: s.score,
        weight: weights[s.metricId] || 0,
      }))
    );

    // Prepare metric scores as JSON
    const metricScoresJson = evaluationData.scores.reduce((acc: Record<string, number>, score) => {
      acc[score.metricId] = score.score;
      return acc;
    }, {});

    // Create evaluation
    const evaluation = await prisma.vendorKpiEvaluation.create({
      data: {
        vendorId: evaluationData.vendorId,
        templateId: evaluationData.templateId,
        evaluationPeriod: `${new Date().getFullYear()}-Q${Math.ceil((new Date().getMonth() + 1) / 3)}`,
        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        endDate: new Date(),
        overallScore,
        categoryScores: {}, // Could be calculated from metric categories
        metricScores: metricScoresJson,
        rating: this.getPerformanceRating(overallScore),
        evaluatedById: evaluationData.evaluatedById,
        recommendations: evaluationData.comments,
      },
    });

    // Create individual scores
    for (const score of evaluationData.scores) {
      await prisma.vendorKpiScore.create({
        data: {
          evaluationId: evaluation.id,
          metricId: score.metricId,
          score: score.score,
        },
      });
    }

    // Map to VendorEvaluation interface
    return {
      id: evaluation.id,
      vendorId: evaluation.vendorId,
      templateId: evaluation.templateId,
      evaluationPeriod: {
        startDate: evaluation.startDate,
        endDate: evaluation.endDate,
      },
      scores: metricScoresJson,
      overallScore: evaluation.overallScore,
      ranking: 0, // Would be calculated separately
      status: evaluation.status as 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED',
      evaluatedBy: evaluation.evaluatedById,
      evaluatedAt: evaluation.createdAt,
      comments: evaluation.recommendations || undefined,
      metadata: {},
    };
  }

  async updateKPITemplate(id: string, updates: Partial<KPITemplate>): Promise<KPITemplate> {
    const updated = await prisma.vendorKpiTemplate.update({
      where: { id },
      data: {
        name: updates.name,
        description: updates.description,
        category: updates.category as KpiCategory,
        isActive: updates.isActive,
        updatedAt: new Date(),
      },
    });

    return updated as unknown as KPITemplate;
  }

  async deleteKPITemplate(id: string): Promise<void> {
    await prisma.vendorKpiTemplate.update({
      where: { id },
      data: { isActive: false },
    });
  }

  async evaluateVendor(
    vendorId: string,
    templateId: string,
    evaluationData: Partial<VendorEvaluation>,
    evaluatedBy: string
  ): Promise<VendorEvaluation> {
    const template = await prisma.vendorKpiTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new Error('KPI template not found');
    }

    // Calculate overall score based on weighted metrics
    const weights = typeof template.weights === 'object' ? template.weights as Record<string, number> : {};
    const overallScore = this.calculateOverallScore(
      evaluationData.scores || {},
      weights
    );

    const evaluation = await prisma.vendorKpiEvaluation.create({
      data: {
        vendorId,
        templateId,
        evaluationPeriod: evaluationData.evaluationPeriod?.startDate ?
          `${evaluationData.evaluationPeriod.startDate.getFullYear()}-Q${Math.ceil((evaluationData.evaluationPeriod.startDate.getMonth() + 1) / 3)}` :
          `${new Date().getFullYear()}-Q${Math.ceil((new Date().getMonth() + 1) / 3)}`,
        startDate: evaluationData.evaluationPeriod?.startDate || new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        endDate: evaluationData.evaluationPeriod?.endDate || new Date(),
        overallScore,
        categoryScores: {}, // Could be calculated from metric categories
        metricScores: evaluationData.scores || {},
        rating: this.getPerformanceRating(overallScore),
        recommendations: evaluationData.comments,
        evaluatedById: evaluatedBy,
      },
    });

    // Update vendor rankings
    await this.updateVendorRankings(templateId);

    return evaluation as unknown as VendorEvaluation;
  }

  async getVendorEvaluations(
    vendorId?: string,
    templateId?: string,
    period?: { startDate: Date; endDate: Date }
  ): Promise<VendorEvaluation[]> {
    const where: Prisma.VendorKpiEvaluationWhereInput = {};
    
    if (vendorId) where.vendorId = vendorId;
    if (templateId) where.templateId = templateId;
    if (period) {
      where.startDate = {
        gte: period.startDate,
      };
      where.endDate = {
        lte: period.endDate,
      };
    }

    const evaluations = await prisma.vendorKpiEvaluation.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });

    return evaluations as unknown as VendorEvaluation[];
  }

  async getVendorPerformanceData(
    vendorId: string,
    period: { startDate: Date; endDate: Date }
  ): Promise<VendorPerformanceData> {
    try {
      // Get purchase orders for the vendor in the specified period
      const purchaseOrders = await prisma.purchaseOrder.findMany({
        where: {
          vendorId,
          createdAt: {
            gte: period.startDate,
            lte: period.endDate,
          },
        },
        include: {
          items: true,
          goodReceipts: {
            include: {
              items: true,
            },
          },
        },
      });

      // Get vendor evaluations for the period
      const evaluations = await prisma.vendorKpiEvaluation.findMany({
        where: {
          vendorId,
          createdAt: {
            gte: period.startDate,
            lte: period.endDate,
          },
        },
      });

      // Get vendor issues/complaints for the period
      const issues = await prisma.vendorIssue.findMany({
        where: {
          vendorId,
          createdAt: {
            gte: period.startDate,
            lte: period.endDate,
          },
        },
      });

      // Calculate delivery performance
      const deliveryPerformance = this.calculateDeliveryPerformance(purchaseOrders);

      // Calculate quality score from evaluations
      const qualityScore = this.calculateQualityScore(evaluations);

      // Calculate cost effectiveness
      const costEffectiveness = this.calculateCostEffectiveness(purchaseOrders);

      // Calculate responsiveness (based on response times to communications)
      const responsiveness = await this.calculateResponsiveness(vendorId, period);

      // Calculate compliance score
      const compliance = await this.calculateComplianceScore(vendorId, period);

      // Map database evaluations to VendorEvaluation type
      const mappedEvaluations: VendorEvaluation[] = evaluations.map(evaluation => ({
        id: evaluation.id,
        vendorId: evaluation.vendorId,
        templateId: evaluation.templateId,
        evaluationPeriod: {
          startDate: evaluation.startDate,
          endDate: evaluation.endDate,
        },
        scores: typeof evaluation.metricScores === 'object' && evaluation.metricScores !== null
          ? evaluation.metricScores as Record<string, number>
          : {},
        overallScore: evaluation.overallScore,
        ranking: 0, // Would be calculated separately
        status: evaluation.status as 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED',
        evaluatedBy: evaluation.evaluatedById,
        evaluatedAt: evaluation.createdAt,
        comments: evaluation.recommendations || undefined,
        metadata: {},
      }));

      // Calculate innovation score from evaluations
      const innovation = this.calculateInnovationScore(mappedEvaluations);

      // Calculate transaction metrics
      const totalOrders = purchaseOrders.length;
      const completedOrders = purchaseOrders.filter(po => po.status === 'APPROVED').length; // Using APPROVED as completed status
      const cancelledOrders = purchaseOrders.filter(po => po.status === 'CANCELLED').length;
      const totalValue = purchaseOrders.reduce((sum: number, po) => sum + (po.totalValue || 0), 0);
      const averageOrderValue = totalOrders > 0 ? totalValue / totalOrders : 0;

      // Calculate issue metrics
      const totalIssues = issues.length;
      const resolvedIssues = issues.filter(issue => issue.status === 'RESOLVED').length;
      const averageResolutionTime = this.calculateAverageResolutionTime(issues);

      const performanceData: VendorPerformanceData = {
        vendorId,
        period,
        metrics: {
          deliveryPerformance,
          qualityScore,
          costEffectiveness,
          responsiveness,
          compliance,
          innovation,
        },
        transactions: {
          totalOrders,
          completedOrders,
          cancelledOrders,
          totalValue,
          averageOrderValue,
        },
        issues: {
          totalIssues,
          resolvedIssues,
          averageResolutionTime,
        },
      };

      return performanceData;
    } catch (error) {
      console.error('Error calculating vendor performance data:', error);
      throw new Error('Failed to calculate vendor performance data');
    }
  }

  async getVendorRankings(
    category?: string,
    limit: number = 50
  ): Promise<VendorRanking[]> {
    try {
      // Get recent evaluations (last 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const whereClause: Prisma.VendorKpiEvaluationWhereInput = {
        createdAt: {
          gte: sixMonthsAgo,
        },
        status: 'APPROVED',
      };

      if (category) {
        whereClause.vendor = {
          businessCategory: category,
        };
      }

      // Get average scores per vendor
      const evaluations = await prisma.vendorKpiEvaluation.findMany({
        where: whereClause,
        include: {
          vendor: {
            select: {
              id: true,
              companyName: true,
              businessCategory: true,
            },
          },
        },
      });

      // Group by vendor and calculate average scores
      const vendorScores = new Map<string, { vendor: { id: string; companyName: string; businessCategory: string }; scores: number[]; totalEvaluations: number }>();

      evaluations.forEach((evaluation) => {
        const vendorId = evaluation.vendorId;
        if (!vendorScores.has(vendorId)) {
          vendorScores.set(vendorId, {
            vendor: evaluation.vendor,
            scores: [],
            totalEvaluations: 0,
          });
        }
        const vendorData = vendorScores.get(vendorId);
        if (!vendorData) return;
        vendorData.scores.push(evaluation.overallScore);
        vendorData.totalEvaluations++;
      });

      // Calculate rankings
      const rankings: VendorRanking[] = Array.from(vendorScores.entries())
        .map(([vendorId, data]) => {
          const averageScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
          return {
            vendorId,
            vendorName: data.vendor.companyName,
            category: data.vendor.businessCategory,
            overallScore: averageScore,
            ranking: 0, // Will be set below
            evaluationCount: data.totalEvaluations,
            lastEvaluationDate: new Date(),
            trend: 'STABLE' as const,
            strengths: [],
            weaknesses: [],
          };
        })
        .sort((a, b) => b.overallScore - a.overallScore)
        .slice(0, limit);

      // Assign ranks
      rankings.forEach((ranking, index) => {
        ranking.ranking = index + 1;
      });

      return rankings;
    } catch (error) {
      console.error('Error calculating vendor rankings:', error);
      return [];
    }
  }

  async generateVendorReport(
    vendorId: string,
    period: { startDate: Date; endDate: Date }
  ): Promise<{
    vendor: VendorInfo;
    performanceData: VendorPerformanceData;
    evaluations: VendorEvaluation[];
    ranking: VendorRanking | null;
    recommendations: string[];
  }> {
    const [vendor, performanceData, evaluations] = await Promise.all([
      prisma.vendor.findUnique({ where: { id: vendorId } }),
      this.getVendorPerformanceData(vendorId, period),
      this.getVendorEvaluations(vendorId, undefined, period),
    ]);

    if (!vendor) {
      throw new Error('Vendor not found');
    }

    const rankings = await this.getVendorRankings();
    const ranking = rankings.find(r => r.vendorId === vendorId) || null;

    const recommendations = this.generateRecommendations(performanceData, evaluations);

    return {
      vendor,
      performanceData,
      evaluations,
      ranking,
      recommendations,
    };
  }

  private validateMetricWeights(metrics: Array<{ weight: number }>): boolean {
    const totalWeight = metrics.reduce((sum, metric) => sum + metric.weight, 0);
    return Math.abs(totalWeight - 100) < 0.01; // Allow for floating point precision
  }

  private calculateWeightedScore(scores: Array<{ score: number; weight: number }>): number {
    const totalWeightedScore = scores.reduce((sum, item) => sum + (item.score * item.weight / 100), 0);
    return Math.round(totalWeightedScore * 100) / 100; // Round to 2 decimal places
  }

  private calculateOverallScore(scores: Record<string, number>, weightings: Record<string, number>): number {
    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const [metricId, score] of Object.entries(scores)) {
      const weight = weightings[metricId] || 0;
      totalWeightedScore += score * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
  }

  private async updateVendorRankings(templateId: string): Promise<void> {
    // Get evaluation count for this template to validate the update
    const evaluationCount = await prisma.vendorKpiEvaluation.count({
      where: { templateId },
    });

    // Rankings are calculated dynamically based on overallScore ordering
    // No need to store ranking in database as it can be computed on-demand
    if (evaluationCount === 0) {
      throw new Error(`No evaluations found for template ${templateId}`);
    }
  }

  private calculateDeliveryPerformance(purchaseOrders: PurchaseOrder[]): number {
    if (purchaseOrders.length === 0) return 0;

    const onTimeDeliveries = purchaseOrders.filter(po => {
      // Simplified logic - in reality, you'd compare actual vs expected delivery dates
      return po.status === 'APPROVED';
    }).length;

    return (onTimeDeliveries / purchaseOrders.length) * 100;
  }

  private calculateQualityScore(evaluations: { overallScore: number }[]): number {
    if (evaluations.length === 0) return 0;

    const totalScore = evaluations.reduce((sum, evaluation) => sum + evaluation.overallScore, 0);
    return totalScore / evaluations.length;
  }

  private calculateCostEffectiveness(purchaseOrders: PurchaseOrder[]): number {
    if (purchaseOrders.length === 0) return 0;

    // Simplified calculation - compare actual costs vs budgeted costs
    // For now, return a baseline score
    return 75; // Placeholder
  }

  private async calculateResponsiveness(vendorId: string, period: { startDate: Date; endDate: Date }): Promise<number> {
    // Calculate based on response times to communications, RFQ responses, etc.
    try {
      // Get vendor evaluations in the period to analyze responsiveness metrics
      const evaluations = await prisma.vendorKpiEvaluation.findMany({
        where: {
          vendorId,
          startDate: {
            gte: period.startDate,
          },
          endDate: {
            lte: period.endDate,
          },
        },
      });

      if (evaluations.length === 0) return 75; // Default score if no data

      // Extract responsiveness scores from evaluations
      const responsivenessScores: number[] = [];

      evaluations.forEach(evaluation => {
        if (evaluation.metricScores && typeof evaluation.metricScores === 'object') {
          const scores = evaluation.metricScores as Record<string, number>;
          // Look for responsiveness-related metrics
          Object.entries(scores).forEach(([metricId, score]) => {
            if (metricId.toLowerCase().includes('responsiveness') ||
                metricId.toLowerCase().includes('response')) {
              responsivenessScores.push(score);
            }
          });
        }
      });

      if (responsivenessScores.length === 0) return 75;

      // Calculate average responsiveness score
      const averageScore = responsivenessScores.reduce((sum: number, score: number) => sum + score, 0) / responsivenessScores.length;
      return Math.round(averageScore);
    } catch (error) {
      console.error('Error calculating responsiveness:', error);
      return 75; // Default score on error
    }
  }

  private async calculateComplianceScore(vendorId: string, period: { startDate: Date; endDate: Date }): Promise<number> {
    // Calculate based on regulatory compliance, contract adherence, etc.
    const issues = await prisma.vendorIssue.findMany({
      where: {
        vendorId,
        category: 'COMPLIANCE',
        createdAt: {
          gte: period.startDate,
          lte: period.endDate,
        },
      },
    });

    // Simple calculation: fewer compliance issues = higher score
    const baseScore = 100;
    const penaltyPerIssue = 10;
    return Math.max(0, baseScore - (issues.length * penaltyPerIssue));
  }

  private calculateInnovationScore(evaluations: VendorEvaluation[]): number {
    // Calculate innovation score from evaluation comments and specific metrics
    if (evaluations.length === 0) return 70; // Default score if no evaluations

    const innovationScores: number[] = [];

    evaluations.forEach(evaluation => {
      // Look for innovation-related metrics in scores
      Object.entries(evaluation.scores).forEach(([metricId, score]) => {
        if (metricId.toLowerCase().includes('innovation') ||
            metricId.toLowerCase().includes('creative') ||
            metricId.toLowerCase().includes('improvement')) {
          innovationScores.push(score);
        }
      });

      // Analyze comments for innovation keywords (simplified NLP)
      if (evaluation.comments) {
        const innovationKeywords = ['innovative', 'creative', 'improvement', 'new', 'advanced', 'cutting-edge'];
        const commentLower = evaluation.comments.toLowerCase();
        const keywordCount = innovationKeywords.filter(keyword => commentLower.includes(keyword)).length;

        // Bonus points for innovation mentions in comments
        if (keywordCount > 0) {
          innovationScores.push(Math.min(100, 70 + (keywordCount * 5)));
        }
      }
    });

    if (innovationScores.length === 0) return 70;

    // Calculate average innovation score
    const averageScore = innovationScores.reduce((sum, score) => sum + score, 0) / innovationScores.length;
    return Math.round(averageScore);
  }

  private calculateAverageResolutionTime(issues: PrismaVendorIssue[]): number {
    const resolvedIssues = issues.filter(issue => issue.resolvedAt);
    if (resolvedIssues.length === 0) return 0;

    const totalResolutionTime = resolvedIssues.reduce((sum: number, issue) => {
      if (!issue.resolvedAt || !issue.createdAt) return sum;
      const resolutionTime = issue.resolvedAt.getTime() - issue.createdAt.getTime();
      return sum + (resolutionTime / (1000 * 60 * 60)); // Convert to hours
    }, 0);

    return totalResolutionTime / resolvedIssues.length;
  }

  private getPerformanceRating(score: number): 'EXCELLENT' | 'GOOD' | 'SATISFACTORY' | 'NEEDS_IMPROVEMENT' | 'POOR' {
    if (score >= 90) return 'EXCELLENT';
    if (score >= 75) return 'GOOD';
    if (score >= 60) return 'SATISFACTORY';
    if (score >= 45) return 'NEEDS_IMPROVEMENT';
    return 'POOR';
  }



  async getVendorKPIEvaluations(vendorId: string): Promise<VendorEvaluation[]> {
    const evaluations = await prisma.vendorKpiEvaluation.findMany({
      where: { vendorId },
      include: {
        template: true,
        scores: {
          include: {
            metric: true,
          },
        },
        evaluatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Map to VendorEvaluation interface
    return evaluations.map(evaluation => ({
      id: evaluation.id,
      vendorId: evaluation.vendorId,
      templateId: evaluation.templateId,
      evaluationPeriod: {
        startDate: evaluation.startDate,
        endDate: evaluation.endDate,
      },
      scores: typeof evaluation.metricScores === 'object' && evaluation.metricScores !== null
        ? evaluation.metricScores as Record<string, number>
        : {},
      overallScore: evaluation.overallScore,
      ranking: 0, // Would be calculated separately
      status: evaluation.status as 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED',
      evaluatedBy: evaluation.evaluatedById,
      evaluatedAt: evaluation.createdAt,
      comments: evaluation.recommendations || undefined,
      metadata: {},
    }));
  }

  async getVendorKPIStatistics(vendorId: string): Promise<{
    averageScore: number;
    evaluationCount: number;
    lastEvaluationDate: Date | null;
    trend: 'IMPROVING' | 'STABLE' | 'DECLINING';
  }> {
    const evaluations = await prisma.vendorKpiEvaluation.findMany({
      where: { vendorId },
      orderBy: { createdAt: 'desc' },
      take: 10, // Last 10 evaluations for trend analysis
    });

    if (evaluations.length === 0) {
      return {
        averageScore: 0,
        evaluationCount: 0,
        lastEvaluationDate: null,
        trend: 'STABLE',
      };
    }

    const averageScore = evaluations.reduce((sum: number, evaluation: { overallScore: number }) => sum + evaluation.overallScore, 0) / evaluations.length;

    // Calculate trend
    let trend: 'IMPROVING' | 'STABLE' | 'DECLINING' = 'STABLE';
    if (evaluations.length >= 3) {
      const recent = evaluations.slice(0, 3);
      const older = evaluations.slice(3, 6);

      if (recent.length >= 3 && older.length >= 3) {
        const recentAvg = recent.reduce((sum: number, evaluation: { overallScore: number }) => sum + evaluation.overallScore, 0) / recent.length;
        const olderAvg = older.reduce((sum: number, evaluation: { overallScore: number }) => sum + evaluation.overallScore, 0) / older.length;

        if (recentAvg > olderAvg + 5) trend = 'IMPROVING';
        else if (recentAvg < olderAvg - 5) trend = 'DECLINING';
      }
    }

    return {
      averageScore,
      evaluationCount: evaluations.length,
      lastEvaluationDate: evaluations[0].createdAt,
      trend,
    };
  }





  private generateRecommendations(
    performanceData: VendorPerformanceData,
    evaluations: VendorEvaluation[]
  ): string[] {
    const recommendations: string[] = [];

    // Analyze performance metrics
    if (performanceData.metrics.deliveryPerformance < 90) {
      recommendations.push('Improve delivery performance - consider better logistics planning');
    }

    if (performanceData.metrics.qualityScore < 85) {
      recommendations.push('Focus on quality improvement - implement quality control measures');
    }

    if (performanceData.metrics.responsiveness < 80) {
      recommendations.push('Enhance communication and responsiveness to requests');
    }

    // Analyze trends from evaluations
    if (evaluations.length >= 2) {
      const latest = evaluations[0];
      const previous = evaluations[1];
      
      if (latest.overallScore < previous.overallScore) {
        recommendations.push('Overall performance declining - schedule performance review meeting');
      }
    }

    return recommendations;
  }














}

// Export singleton instance
export const vendorKPISystem = new VendorKPISystem();
