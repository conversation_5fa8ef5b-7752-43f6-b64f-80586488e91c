// Extended type definitions for the e-procurement system
// This file contains additional types and enums that extend the base Prisma types

// Extended approval status enum that includes evaluation stages
export type ExtendedApprovalStatus =
  | "PENDING"
  | "APPROVED"
  | "REJECTED"
  | "CANCELLED"
  | "EVALUATING_ADMIN"
  | "PASSED_ADMIN"
  | "FAILED_ADMIN"
  | "EVALUATING_TECH"
  | "PASSED_TECH"
  | "FAILED_TECH"
  | "EVALUATING_PRICE"
  | "PASSED_PRICE"
  | "FAILED_PRICE"
  | "EVALUATING_COMMERCIAL"
  | "PASSED_COMMERCIAL"
  | "FAILED_COMMERCIAL"
  | "NEGOTIATING"
  | "BACKUP_1"
  | "BACKUP_2"
  | "SUBMITTED";

// Extended procurement status with additional workflow states
export type ExtendedProcurementStatus =
  | "DRAFT"
  | "PUBLISHED"
  | "SUBMISSION_OPEN"
  | "SUBMISSION_CLOSED"
  | "EVALUATION"
  | "AWARDED"
  | "COMPLETED"
  | "CANCELLED"
  | "UNDER_REVIEW"
  | "CLARIFICATION_PHASE"
  | "NEGOTIATION_PHASE";

// Extended audit actions for comprehensive tracking
export type ExtendedAuditAction =
  | "CREATE"
  | "READ"
  | "UPDATE"
  | "DELETE"
  | "LOGIN"
  | "LOGOUT"
  | "APPROVE"
  | "REJECT"
  | "SUBMIT"
  | "CANCEL"
  | "EXPORT"
  | "IMPORT"
  | "UPLOAD"
  | "DOWNLOAD"
  | "SEND_EMAIL"
  | "GENERATE_DOCUMENT"
  | "SIGN_DOCUMENT"
  | "ESCALATE"
  | "DELEGATE"
  | "COMMENT"
  | "BLACKLIST"
  | "UNBLACKLIST"
  | "VERIFY"
  | "SUSPEND"
  | "ACTIVATE"
  | "ARCHIVE"
  | "RESTORE"
  | "NEGOTIATE"
  | "EVALUATE"
  | "AWARD"
  | "PRICE_CORRECT"
  | "STATUS_CHANGE"
  | "WORKFLOW_TRANSITION"
  | "VENDOR_REGISTER"
  | "VENDOR_VERIFY"
  | "OFFER_SUBMIT"
  | "DISCUSSION_CREATE"
  | "NOTIFICATION_SEND";

// Vendor verification statuses
export type VendorVerificationStatus =
  | "PENDING_VERIFICATION"
  | "UNDER_REVIEW"
  | "DOCUMENT_REQUIRED"
  | "VERIFIED"
  | "REJECTED"
  | "SUSPENDED"
  | "BLACKLISTED";

// Notification types for the system
export type ExtendedNotificationType =
  | "INFO"
  | "SUCCESS"
  | "WARNING"
  | "ERROR"
  | "APPROVAL_REQUEST"
  | "DEADLINE_REMINDER"
  | "STATUS_UPDATE"
  | "SYSTEM_ALERT"
  | "VENDOR_NOTIFICATION"
  | "PROCUREMENT_UPDATE"
  | "EVALUATION_ASSIGNED"
  | "OFFER_RECEIVED"
  | "WORKFLOW_ESCALATION";

// Queue job types
export type QueueJobType =
  | "EMAIL"
  | "SMS"
  | "PUSH_NOTIFICATION"
  | "WEBHOOK"
  | "DOCUMENT_GENERATION"
  | "AUDIT_LOG"
  | "DATA_SYNC"
  | "REPORT_GENERATION"
  | "NOTIFICATION_ESCALATION"
  | "VENDOR_VERIFICATION"
  | "PROCUREMENT_WORKFLOW"
  | "APPROVAL_REMINDER";

// Queue job status
export type QueueJobStatus =
  | "PENDING"
  | "PROCESSING"
  | "COMPLETED"
  | "FAILED"
  | "RETRYING"
  | "CANCELLED"
  | "DELAYED";

// Email queue configuration
export interface EmailQueueJob {
  id: string;
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  body: string;
  htmlBody?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
  templateId?: string;
  templateData?: Record<string, string | number | boolean | Date>;
  priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  status: QueueJobStatus;
  attempts: number;
  maxAttempts?: number;
  scheduledAt?: Date;
  processedAt?: Date;
  failedAt?: Date;
  error?: string;
  metadata?: Record<string, string | number | boolean | Date>;
  createdAt: Date;
  updatedAt: Date;
}

// SMS queue configuration
export interface SmsQueueJob {
  id: string;
  to: string;
  message: string;
  templateId?: string;
  templateData?: Record<string, string | number | boolean | Date>;
  priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  status: QueueJobStatus;
  attempts: number;
  maxAttempts?: number;
  scheduledAt?: Date;
  processedAt?: Date;
  failedAt?: Date;
  error?: string;
  metadata?: Record<string, string | number | boolean | Date>;
  createdAt: Date;
  updatedAt: Date;
}

// Push notification queue configuration
export interface PushQueueJob {
  id: string;
  userId: string;
  title: string;
  body: string;
  data?: Record<string, string | number | boolean>;
  badge?: number;
  sound?: string;
  templateId?: string;
  priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  status: QueueJobStatus;
  attempts: number;
  maxAttempts?: number;
  scheduledAt?: Date;
  processedAt?: Date;
  failedAt?: Date;
  error?: string;
  metadata?: Record<string, string | number | boolean | Date>;
  createdAt: Date;
  updatedAt: Date;
}

// Webhook queue configuration
export interface WebhookQueueJob {
  id: string;
  url: string;
  method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
  headers?: Record<string, string>;
  body?: Record<string, unknown> | string;
  payload?: Record<string, unknown> | string;
  priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  status: QueueJobStatus;
  attempts: number;
  maxAttempts?: number;
  scheduledAt?: Date;
  processedAt?: Date;
  failedAt?: Date;
  error?: string;
  metadata?: Record<string, string | number | boolean | Date>;
  createdAt: Date;
  updatedAt: Date;
}

// KPI metric definition for templates
export interface KpiMetricDefinition {
  id: string;
  name: string;
  description?: string;
  type: "PERCENTAGE" | "NUMBER" | "CURRENCY" | "BOOLEAN" | "RATING";
  unit?: string;
  weight: number;
  calculation: "SUM" | "AVERAGE" | "COUNT" | "PERCENTAGE" | "CUSTOM";
  formula?: string;
  isRequired: boolean;
  category?: string;
  minValue?: number;
  maxValue?: number;
  targetValue?: number;
}

// Public asset types and security levels
export type PublicAssetType =
  | "PROCUREMENT_DOCUMENT"
  | "TEMPLATE"
  | "ANNOUNCEMENT"
  | "NEWS_ARTICLE"
  | "EVALUATION_RESULT"
  | "AWARD_NOTICE"
  | "TENDER_DOCUMENT"
  | "SPECIFICATION"
  | "TERMS_CONDITIONS"
  | "USER_GUIDE"
  | "FORM"
  | "REPORT";

export type PublicAssetSecurityLevel =
  | "PUBLIC"
  | "REGISTERED_USERS"
  | "VERIFIED_VENDORS"
  | "INTERNAL_ONLY"
  | "CONFIDENTIAL"
  | "RESTRICTED";

// Workflow instance status with timeout support
export type WorkflowInstanceStatus =
  | "PENDING"
  | "IN_PROGRESS"
  | "APPROVED"
  | "REJECTED"
  | "CANCELLED"
  | "TIMEOUT"
  | "EXPIRED"
  | "ESCALATED";

// Workflow step status with timeout support
export type WorkflowStepStatus =
  | "PENDING"
  | "IN_PROGRESS"
  | "APPROVED"
  | "REJECTED"
  | "SKIPPED"
  | "TIMEOUT"
  | "EXPIRED"
  | "ESCALATED";

// Price correction types
export type PriceCorrectionType =
  | "ARITHMETIC_ERROR"
  | "UNIT_PRICE_ADJUSTMENT"
  | "QUANTITY_ADJUSTMENT"
  | "DISCOUNT_APPLICATION"
  | "TAX_ADJUSTMENT"
  | "CURRENCY_CONVERSION"
  | "SPECIFICATION_CHANGE"
  | "VENDOR_REQUEST"
  | "COMMITTEE_DECISION"
  | "NEGOTIATION_RESULT";

// Document template component types
export type DocumentComponentType =
  | "TEXT"
  | "VARIABLE"
  | "TABLE"
  | "LIST"
  | "IMAGE"
  | "SIGNATURE"
  | "CHECKBOX"
  | "DATE"
  | "CURRENCY"
  | "CONDITIONAL"
  | "LOOP"
  | "CALCULATION"
  | "BARCODE"
  | "QR_CODE";

// Evaluation criteria types
export type EvaluationCriteriaType =
  | "TECHNICAL"
  | "COMMERCIAL"
  | "ADMINISTRATIVE"
  | "FINANCIAL"
  | "EXPERIENCE"
  | "METHODOLOGY"
  | "TIMELINE"
  | "QUALITY"
  | "SUSTAINABILITY"
  | "LOCAL_CONTENT";

// Performance trend indicators
export type PerformanceTrendIndicator =
  | "IMPROVING"
  | "STABLE"
  | "DECLINING"
  | "VOLATILE"
  | "NEW_VENDOR"
  | "INSUFFICIENT_DATA";

// Document template types
export interface TemplateVariable {
  id: string;
  name: string;
  type: "TEXT" | "NUMBER" | "DATE" | "BOOLEAN" | "ARRAY" | "OBJECT";
  description?: string;
  required?: boolean;
  defaultValue?: string | number | boolean | Date | null;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    options?: string[];
  };
}

export interface DocumentSettings {
  pageSize: "A4" | "A3" | "LETTER" | "LEGAL";
  orientation: "portrait" | "landscape";
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  header?: {
    enabled: boolean;
    content?: string;
    height?: number;
  };
  footer?: {
    enabled: boolean;
    content?: string;
    height?: number;
  };
  watermark?: {
    enabled: boolean;
    text?: string;
    opacity?: number;
  };
  fonts?: {
    primary: string;
    secondary?: string;
    size: {
      body: number;
      heading: number;
      small: number;
    };
  };
}

// Document template interface
export interface DocumentTemplate {
  id: string;
  name: string;
  description?: string;
  type: DocumentType;
  htmlTemplate: string;
  cssStyles?: string;
  variables: TemplateVariable[];
  settings: DocumentSettings;
  isActive: boolean;
  version: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

// Generated document interface
export interface GeneratedDocument {
  id: string;
  templateId: string;
  templateName: string;
  documentType: DocumentType;
  entityType: string;
  entityId: string;
  name: string;
  data: Record<string, unknown>;
  content: string | Buffer;
  pdfUrl?: string;
  generatedAt: Date;
  status: string;
  version: number;
  metadata?: Record<string, string | number | boolean | Date>;
  createdBy: string;
  createdAt: Date;
}

// Document variable interface
export interface DocumentVariable {
  id: string;
  name: string;
  type: string;
  value: string | number | boolean | Date | null;
  description?: string;
}

// Signature field interface
export interface SignatureField {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  page: number;
  required: boolean;
  signerRole?: string;
  signerEmail?: string;
}

// Notification recipient interface
export interface NotificationRecipient {
  id: string;
  type: "USER" | "EMAIL" | "ROLE" | "DEPARTMENT";
  value: string;
  name?: string;
  metadata?: Record<string, string | number | boolean>;
}

// Workflow condition interface
export interface WorkflowCondition {
  field: string;
  operator: "EQUALS" | "NOT_EQUALS" | "GREATER_THAN" | "LESS_THAN" | "CONTAINS" | "IN" | "NOT_IN";
  value: string | number | boolean | string[] | number[];
  logicalOperator?: "AND" | "OR";
}

// Step configuration interface
export interface StepConfig {
  timeoutHours?: number;
  escalationRules?: {
    escalateToIds: string[];
    escalationMessage: string;
  };
  conditions?: WorkflowCondition[];
  autoApprove?: boolean;
  requireComments?: boolean;
  allowDelegation?: boolean;
}

// Approver configuration interface
export interface ApproverConfig {
  userIds?: string[];
  roleIds?: string[];
  departmentIds?: string[];
  conditions?: WorkflowCondition[];
  fallbackApprovers?: string[];
}

// Form input types for vendor registration
export interface VendorRegistrationInput {
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  companyName: string;
  companyType?: string;
  businessLicense?: string;
  address: string;
  city?: string;
  province?: string;
  postalCode?: string;
  businessCategory?: string;
  picName: string;
  picEmail: string;
  picPhone: string;
  npwpNumber: string;
  npwpAddress: string;
  businessEntityType: "PT" | "CV" | "UD" | "KOPERASI" | "YAYASAN";
  termsAndConditions: boolean;
  establishedYear?: number;
  employeeCount?: number;
  annualRevenue?: number;
  website?: string;
  businessDescription?: string;
  documents?: Array<{
    type?: string;
    file?: File;
    documentType: string;
    fileName?: string;
    fileUrl?: string;
    fileType?: string;
  }>;
}

// Form input types for procurement creation
export interface ProcurementInput {
  status?: "AANWIJZING" | "DRAFT" | "CANCELLED" | "PUBLISHED" | "NEGOTIATION" | "COMPLETED" | "EVALUATION" | "AWARDED" | "WINNER_ANNOUNCEMENT" | "SUBMISSION";
  type: "RFQ" | "TENDER";
  title: string;
  description?: string;
  category?: string;
  estimatedValue?: number;
  currency?: string;
  submissionDeadline?: Date;
  evaluationMethod: string;
  ownerEstimate: number;
  items: Array<{
    name: string;
    description?: string;
    quantity: number;
    unit: string;
    ownerEstimate: number;
  }>;
  stages: Array<{
    name: string;
    sequence: number;
    startDate: Date;
    endDate: Date;
  }>;
  committee: Array<{
    userId: string;
    committeeRole: string;
  }>;
  showOwnerEstimateToVendor?: boolean;
}

// Extended types for compatibility
export type DocumentType =
  | "RFQ"
  | "CONTRACT"
  | "PURCHASE_ORDER"
  | "INVOICE"
  | "BAST"
  | "AANWIJZING"
  | "EVALUATION_REPORT"
  | "AWARD_LETTER"
  | "CUSTOM"
  | "PURCHASE_REQUISITION"
  | "DELIVERY_NOTE"
  | "VENDOR_EVALUATION"
  | "GRN"
  | "QUOTATION";
