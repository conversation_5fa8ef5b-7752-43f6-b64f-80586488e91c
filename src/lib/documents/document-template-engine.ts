import Handlebars from "handlebars";

import { prisma } from "@/lib/db";
import { TemplateVariable, DocumentSettings } from "@/lib/types";
import { toPrismaJson } from "@/lib/utils/json";

// Dynamic import for puppeteer to avoid bundling if not needed
async function getPuppeteer() {
  try {
    const puppeteer = await import("puppeteer");
    return puppeteer.default;
  } catch (error) {
    console.error("Puppeteer not installed. Install with: npm install puppeteer");
    throw new Error("Puppeteer is required for PDF generation. Please install it with: npm install puppeteer");
  }
}

export interface DocumentTemplate {
  id: string;
  name: string;
  type: DocumentType;
  description?: string;
  htmlTemplate: string;
  cssStyles?: string;
  variables: TemplateVariable[];
  settings: DocumentSettings;
  version: number;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Using types from @/lib/types for consistency

export type DocumentType =
  | "PURCHASE_ORDER"
  | "CONTRACT"
  | "BAST"
  | "VENDOR_EVALUATION"
  | "PURCHASE_REQUISITION"
  | "GOOD_RECEIPT"
  | "INVOICE"
  | "QUOTATION"
  | "CUSTOM";

export interface GeneratedDocument {
  id: string;
  templateId: string;
  templateName: string;
  documentType: DocumentType;
  entityType: string;
  entityId: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  version: number;
  generatedBy: string;
  generatedAt: Date;
  data: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface DocumentGenerationOptions {
  templateId: string;
  data: Record<string, any>;
  fileName?: string;
  version?: number;
  metadata?: Record<string, any>;
}

export class DocumentTemplateEngine {
  private handlebars: typeof Handlebars;

  constructor() {
    this.handlebars = Handlebars.create();
    this.registerHelpers();
  }

  async generateDocument(
    options: DocumentGenerationOptions,
    userId: string,
    entityType: string,
    entityId: string
  ): Promise<GeneratedDocument> {
    // Get template
    const template = await this.getTemplate(options.templateId);
    if (!template || !template.isActive) {
      throw new Error("Template not found or inactive");
    }

    // Validate data against template variables
    this.validateTemplateData(template, options.data);

    // Render HTML content
    const htmlContent = await this.renderTemplate(template, options.data);

    // Generate PDF
    const pdfBuffer = await this.generatePDF(htmlContent, template.settings);

    // Save document to storage
    const fileName =
      options.fileName || this.generateFileName(template, options.data);
    const filePath = await this.saveDocument(pdfBuffer, fileName);

    // Create document record
    const document = await prisma.generatedDocument.create({
      data: {
        templateId: template.id,
        templateName: template.name,
        documentType: template.type as any,
        entityType,
        entityId,
        name: fileName,
        content: toPrismaJson({ htmlContent }),
        version: options.version || 1,
        createdBy: userId,
        data: toPrismaJson(options.data),
        metadata: toPrismaJson(options.metadata || {}),
      },
    });

    return {
      ...document,
      fileName: fileName,
      filePath: filePath,
      fileSize: Buffer.byteLength(htmlContent),
      mimeType: "application/pdf",
      generatedBy: document.createdBy,
    } as GeneratedDocument;
  }

  async getTemplate(templateId: string): Promise<DocumentTemplate | null> {
    const template = await prisma.documentTemplate.findUnique({
      where: { id: templateId },
    });

    return template as DocumentTemplate | null;
  }

  async createTemplate(
    templateData: Omit<DocumentTemplate, "id" | "createdAt" | "updatedAt">,
    userId: string
  ): Promise<DocumentTemplate> {
    const template = await prisma.documentTemplate.create({
      data: {
        name: templateData.name,
        description: templateData.description,
        type: templateData.type as any,
        category: "GENERAL",
        content: toPrismaJson({ htmlTemplate: templateData.htmlTemplate, cssStyles: templateData.cssStyles, variables: templateData.variables, settings: templateData.settings }),
        variables: toPrismaJson(templateData.variables),
        isActive: templateData.isActive,
        version: templateData.version,
        createdBy: userId,
      },
    });

    return {
      ...template,
      htmlTemplate: templateData.htmlTemplate,
      settings: templateData.settings,
      variables: templateData.variables,
    } as DocumentTemplate;
  }

  async updateTemplate(
    templateId: string,
    updates: Partial<DocumentTemplate>,
    userId: string
  ): Promise<DocumentTemplate> {
    const template = await prisma.documentTemplate.update({
      where: { id: templateId },
      data: {
        name: updates.name,
        description: updates.description,
        type: updates.type as any,
        variables: updates.variables ? toPrismaJson(updates.variables) : undefined,
        isActive: updates.isActive,
        version: updates.version,
        updatedAt: new Date(),
      },
    });

    return {
      ...template,
      htmlTemplate: updates.htmlTemplate || "",
      settings: updates.settings || {},
      variables: (template.variables as any) || [],
    } as DocumentTemplate;
  }

  async getTemplatesByType(type: DocumentType): Promise<DocumentTemplate[]> {
    const templates = await prisma.documentTemplate.findMany({
      where: {
        type: type as any,
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return templates.map(template => ({
      ...template,
      htmlTemplate: (template.content as any)?.htmlTemplate || "",
      settings: (template.content as any)?.settings || {},
      variables: (template.variables as any) || [],
    })) as DocumentTemplate[];
  }

  async getDocumentHistory(
    entityType: string,
    entityId: string
  ): Promise<GeneratedDocument[]> {
    const documents = await prisma.generatedDocument.findMany({
      where: {
        entityType,
        entityId,
      },
      // Remove the include since generatedByUser relation doesn't exist in schema
      orderBy: {
        generatedAt: "desc",
      },
    });

    return documents.map(doc => ({
      ...doc,
      fileName: doc.name + ".pdf",
      filePath: "/documents/" + doc.name + ".pdf",
      fileSize: 0,
      mimeType: "application/pdf",
      generatedBy: doc.createdBy,
    })) as GeneratedDocument[];
  }

  private async renderTemplate(
    template: DocumentTemplate,
    data: Record<string, any>
  ): Promise<string> {
    try {
      // Extract template content from JSON
      const content = (template as any).content;
      const htmlTemplate = content?.htmlTemplate || (template as any).htmlTemplate || '';
      const cssStyles = content?.cssStyles || (template as any).cssStyles || '';

      if (!htmlTemplate) {
        throw new Error('Template HTML content is missing');
      }

      // Compile the template
      const compiledTemplate = this.handlebars.compile(htmlTemplate);

      // Prepare data with additional context
      const templateData = {
        ...data,
        _meta: {
          generatedAt: new Date(),
          templateName: template.name,
          templateVersion: template.version,
        },
      };

      // Render the template
      const htmlContent = compiledTemplate(templateData);

      // Add CSS styles
      const styledContent = this.addStyles(htmlContent, cssStyles);

      return styledContent;
    } catch (error) {
      throw new Error(
        `Template rendering failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  private async generatePDF(
    htmlContent: string,
    settings: DocumentSettings
  ): Promise<Buffer> {
    const puppeteer = await getPuppeteer();

    // Configure browser launch options for production
    const launchOptions: any = {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
      ],
    };

    // In production environments (like Docker), use additional options
    if (process.env.NODE_ENV === 'production') {
      launchOptions.executablePath = process.env.PUPPETEER_EXECUTABLE_PATH || '/usr/bin/chromium-browser';
    }

    const browser = await puppeteer.launch(launchOptions);

    try {
      const page = await browser.newPage();

      // Set viewport for consistent rendering
      await page.setViewport({ width: 1200, height: 800 });

      // Set content with proper wait conditions
      await page.setContent(htmlContent, {
        waitUntil: ["networkidle0", "domcontentloaded"],
        timeout: 30000
      });

      // Wait for any dynamic content to load
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate PDF with optimized settings
      const pdfBuffer = await page.pdf({
        format: settings.pageSize.toLowerCase() as any,
        landscape: settings.orientation === "landscape",
        margin: {
          top: `${settings.margins.top}mm`,
          right: `${settings.margins.right}mm`,
          bottom: `${settings.margins.bottom}mm`,
          left: `${settings.margins.left}mm`,
        },
        printBackground: true,
        displayHeaderFooter: settings.header?.enabled || settings.footer?.enabled,
        headerTemplate: settings.header?.enabled ? settings.header.content || "" : "",
        footerTemplate: settings.footer?.enabled ? settings.footer.content || "" : "",
        preferCSSPageSize: true,
        timeout: 30000,
      });

      return Buffer.from(pdfBuffer);
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      await browser.close();
    }
  }

  private async saveDocument(
    buffer: Buffer,
    fileName: string
  ): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, "_");
    const finalFileName = `${timestamp}_${sanitizedFileName}`;

    // Determine storage method based on environment
    if (process.env.AWS_S3_BUCKET) {
      return await this.saveToS3(buffer, finalFileName);
    } else {
      return await this.saveToLocalFileSystem(buffer, finalFileName);
    }
  }

  private async saveToS3(buffer: Buffer, fileName: string): Promise<string> {
    try {
      const { S3Client, PutObjectCommand } = await import('@aws-sdk/client-s3');

      const s3Client = new S3Client({
        region: process.env.AWS_REGION || 'us-east-1',
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      });

      const bucketName = process.env.AWS_S3_BUCKET!;
      const key = `documents/${fileName}`;

      const command = new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: buffer,
        ContentType: 'application/pdf',
        ServerSideEncryption: 'AES256',
      });

      await s3Client.send(command);

      // Return the S3 URL
      return `https://${bucketName}.s3.${process.env.AWS_REGION || 'us-east-1'}.amazonaws.com/${key}`;
    } catch (error) {
      console.error('Failed to save document to S3:', error);
      throw new Error('Failed to save document to cloud storage');
    }
  }

  private async saveToLocalFileSystem(buffer: Buffer, fileName: string): Promise<string> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      // Create documents directory if it doesn't exist
      const documentsDir = path.join(process.cwd(), 'uploads', 'documents');
      await fs.mkdir(documentsDir, { recursive: true });

      // Save file
      const filePath = path.join(documentsDir, fileName);
      await fs.writeFile(filePath, buffer);

      // Return relative path for serving
      return `/uploads/documents/${fileName}`;
    } catch (error) {
      console.error('Failed to save document to local filesystem:', error);
      throw new Error('Failed to save document to local storage');
    }
  }

  private validateTemplateData(
    template: DocumentTemplate,
    data: Record<string, any>
  ): void {
    // Handle template.variables as JSON array
    const variables = Array.isArray(template.variables)
      ? template.variables
      : (template.variables as any)?.variables || [];

    for (const variable of variables) {
      const value = data[variable.name];

      // Check required fields
      if (variable.required && (value === undefined || value === null)) {
        throw new Error(`Required variable '${variable.name}' is missing`);
      }

      // Type validation
      if (value !== undefined && value !== null) {
        this.validateVariableType(variable, value);
      }
    }
  }

  private validateVariableType(variable: TemplateVariable, value: any): void {
    switch (variable.type) {
      case "TEXT":
        if (typeof value !== "string") {
          throw new Error(`Variable '${variable.name}' must be a string`);
        }
        break;
      case "NUMBER":
        if (typeof value !== "number") {
          throw new Error(`Variable '${variable.name}' must be a number`);
        }
        break;
      case "DATE":
        if (!(value instanceof Date) && typeof value !== "string") {
          throw new Error(`Variable '${variable.name}' must be a date`);
        }
        break;
      case "BOOLEAN":
        if (typeof value !== "boolean") {
          throw new Error(`Variable '${variable.name}' must be a boolean`);
        }
        break;
      case "OBJECT":
        if (typeof value !== "object" || Array.isArray(value)) {
          throw new Error(`Variable '${variable.name}' must be an object`);
        }
        break;
      default:
        // For any other type, just log a warning and continue
        console.warn(`Unknown variable type '${variable.type}' for variable '${variable.name}'`);
        break;
    }
  }

  private addStyles(htmlContent: string, cssStyles?: string): string {
    const defaultStyles = `
      <style>
        body {
          font-family: 'Arial', sans-serif;
          font-size: 12px;
          line-height: 1.4;
          color: #333;
          margin: 0;
          padding: 20px;
        }
        .header {
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
          margin-bottom: 20px;
        }
        .footer {
          border-top: 1px solid #ccc;
          padding-top: 10px;
          margin-top: 20px;
          font-size: 10px;
          color: #666;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 10px 0;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .font-bold { font-weight: bold; }
        .text-lg { font-size: 14px; }
        .text-xl { font-size: 16px; }
        .mb-4 { margin-bottom: 16px; }
        .mt-4 { margin-top: 16px; }
      </style>
    `;

    const customStyles = cssStyles ? `<style>${cssStyles}</style>` : "";

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          ${defaultStyles}
          ${customStyles}
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `;
  }

  private generateFileName(
    template: DocumentTemplate,
    data: Record<string, any>
  ): string {
    const timestamp = new Date().toISOString().split("T")[0];
    const templateName = template.name.replace(/[^a-zA-Z0-9]/g, "_");

    // Try to include relevant identifiers from data
    let identifier = "";
    if (data.number || data.id || data.code) {
      identifier = `_${data.number || data.id || data.code}`;
    }

    return `${templateName}${identifier}_${timestamp}.pdf`;
  }

  private registerHelpers(): void {
    // Date formatting helper
    this.handlebars.registerHelper(
      "formatDate",
      (date: string | Date, format: string = "DD/MM/YYYY") => {
        if (!date) return "";
        const d = new Date(date);
        return d.toLocaleDateString("id-ID");
      }
    );

    // Currency formatting helper
    this.handlebars.registerHelper("formatCurrency", (amount: number) => {
      if (typeof amount !== "number") return "";
      return new Intl.NumberFormat("id-ID", {
        style: "currency",
        currency: "IDR",
        minimumFractionDigits: 0,
      }).format(amount);
    });

    // Number formatting helper
    this.handlebars.registerHelper(
      "formatNumber",
      (number: number, decimals: number = 0) => {
        if (typeof number !== "number") return "";
        return number.toLocaleString("id-ID", {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals,
        });
      }
    );

    // Conditional helper
    this.handlebars.registerHelper(
      "ifEquals",
      function (
        this: unknown,
        arg1: unknown,
        arg2: unknown,
        options: Handlebars.HelperOptions
      ) {
        return arg1 == arg2 ? options.fn(this) : options.inverse(this);
      }
    );

    // Loop with index helper
    this.handlebars.registerHelper(
      "eachWithIndex",
      function (
        this: unknown,
        array: unknown[],
        options: Handlebars.HelperOptions
      ) {
        let result = "";
        for (let i = 0; i < array.length; i++) {
          result += options.fn({
            ...(array[i] as object),
            index: i + 1,
            isFirst: i === 0,
            isLast: i === array.length - 1,
          });
        }
        return result;
      }
    );

    // Math helpers
    this.handlebars.registerHelper("add", (a: number, b: number) => a + b);
    this.handlebars.registerHelper("subtract", (a: number, b: number) => a - b);
    this.handlebars.registerHelper("multiply", (a: number, b: number) => a * b);
    this.handlebars.registerHelper("divide", (a: number, b: number) =>
      b !== 0 ? a / b : 0
    );

    // String helpers
    this.handlebars.registerHelper(
      "uppercase",
      (str: string) => str?.toUpperCase() || ""
    );
    this.handlebars.registerHelper(
      "lowercase",
      (str: string) => str?.toLowerCase() || ""
    );
    this.handlebars.registerHelper("capitalize", (str: string) => {
      if (!str) return "";
      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    });
  }
}

export const documentTemplateEngine = new DocumentTemplateEngine();
