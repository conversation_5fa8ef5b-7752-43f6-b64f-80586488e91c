import { auditLogger } from "@/lib/audit/comprehensive-audit-logger";
import { prisma } from "@/lib/db";

import { documentTemplateEngine } from "./document-template-engine";

export class ProcurementDocumentGenerator {
  
  // Generate Purchase Order from approved procurement
  async generatePurchaseOrder(
    procurementId: string,
    winnerId: string,
    userId: string
  ): Promise<any> {
    // Get procurement and winner details
    const procurement = await prisma.procurement.findUnique({
      where: { id: procurementId },
      include: {
        items: {
          include: {
            item: true,
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    const winner = await prisma.offer.findUnique({
      where: { id: winnerId },
      include: {
        vendor: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            }
          }
        },
        // items: {
        //   include: {
        //     item: true,
        //   }
        // }
      }
    });

    if (!procurement || !winner) {
      throw new Error("Procurement or winner not found");
    }

    // Get company information
    const company = await this.getCompanyInfo();

    // Prepare PO data
    const poData = {
      poNumber: await this.generatePONumber(),
      orderDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      vendor: {
        companyName: "Vendor Company", // winner.vendor.companyName,
        address: "Vendor Address", // winner.vendor.address,
        contactPerson: "Contact Person", // winner.vendor.contactPerson,
        contactPhone: "Contact Phone", // winner.vendor.contactPhone,
        contactEmail: "<EMAIL>", // winner.vendor.contactEmail,
      },
      items: [], // winner.items.map((item: any) => ({
      // Commented out due to schema mismatch
      // }),
      subtotal: winner.totalAmount,
      taxRate: 11, // PPN 11%
      taxAmount: winner.totalAmount * 0.11,
      totalAmount: winner.totalAmount * 1.11,
      paymentTerms: "Net 30 days",
      deliveryTerms: "FOB Destination",
      deliveryAddress: company.address,
      notes: (procurement as any).notes || "",
      preparedBy: {
        name: procurement.createdBy.name,
        title: "Procurement Officer",
      },
      approvedBy: {
        name: "Manager", // This should come from approval workflow
        title: "Procurement Manager",
      },
      company,
    };

    // Get PO template
    const templates = await documentTemplateEngine.getTemplatesByType("PURCHASE_ORDER");
    if (templates.length === 0) {
      throw new Error("No Purchase Order template found");
    }

    // Generate document
    const document = await documentTemplateEngine.generateDocument(
      {
        templateId: templates[0].id,
        data: poData,
        fileName: `PO_${poData.poNumber}.pdf`,
        metadata: {
          procurementId,
          winnerId,
          generationType: "AUTOMATIC",
        }
      },
      userId,
      "PROCUREMENT",
      procurementId
    );

    // Log document generation
    await auditLogger.logCrudOperation(
      userId,
      "CREATE",
      "PURCHASE_ORDER_DOCUMENT",
      document.id,
      undefined,
      {
        procurementId,
        winnerId,
        poNumber: poData.poNumber,
        totalAmount: poData.totalAmount,
      }
    );

    return document;
  }

  // Generate BAST document
  async generateBAST(
    goodReceiptId: string,
    userId: string,
    vendorRepresentativeId: string,
    internalRepresentativeId: string
  ): Promise<any> {
    // Get good receipt details
    const goodReceipt = await prisma.goodReceipt.findUnique({
      where: { id: goodReceiptId },
      include: {
        po: {
          include: {
            vendor: true,
            items: {
              include: {
                item: true,
              }
            }
          }
        },
        items: {
          include: {
            purchaseOrderItem: {
              include: {
                item: true,
              }
            }
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    if (!goodReceipt) {
      throw new Error("Good Receipt not found");
    }

    // Get representative details
    const [vendorRep, internalRep] = await Promise.all([
      prisma.user.findUnique({
        where: { id: vendorRepresentativeId },
        select: { id: true, name: true, email: true }
      }),
      prisma.user.findUnique({
        where: { id: internalRepresentativeId },
        select: { id: true, name: true, email: true }
      })
    ]);

    if (!vendorRep || !internalRep) {
      throw new Error("Representative not found");
    }

    // Get company information
    const company = await this.getCompanyInfo();

    // Prepare BAST data
    const bastData = {
      bastNumber: await this.generateBASTNumber(),
      handoverDate: new Date(),
      po: {
        poNumber: goodReceipt.po.poNumber,
        orderDate: goodReceipt.po.createdAt,
        totalAmount: (goodReceipt.po as any).totalAmount || 0,
      },
      vendor: {
        companyName: goodReceipt.po.vendor.companyName,
        address: goodReceipt.po.vendor.address,
        contactPerson: goodReceipt.po.vendor.contactPerson,
      },
      items: goodReceipt.items.map((grItem: any) => ({
        item: {
          name: grItem.purchaseOrderItem.item.name,
          description: grItem.purchaseOrderItem.item.description,
          unit: grItem.purchaseOrderItem.item.unit,
        },
        contractQuantity: grItem.purchaseOrderItem.quantity,
        receivedQuantity: grItem.receivedQuantity,
        condition: grItem.condition,
      })),
      notes: (goodReceipt as any).notes || "",
      vendorRepresentative: {
        name: vendorRep.name,
        title: "Vendor Representative",
      },
      internalRepresentative: {
        name: internalRep.name,
        title: "Internal Representative",
      },
      company,
    };

    // Get BAST template
    const templates = await documentTemplateEngine.getTemplatesByType("BAST");
    if (templates.length === 0) {
      throw new Error("No BAST template found");
    }

    // Generate document
    const document = await documentTemplateEngine.generateDocument(
      {
        templateId: templates[0].id,
        data: bastData,
        fileName: `BAST_${bastData.bastNumber}.pdf`,
        metadata: {
          goodReceiptId,
          vendorRepresentativeId,
          internalRepresentativeId,
          generationType: "MANUAL",
        }
      },
      userId,
      "GOOD_RECEIPT",
      goodReceiptId
    );

    // Log document generation
    await auditLogger.logCrudOperation(
      userId,
      "CREATE",
      "BAST_DOCUMENT",
      document.id,
      undefined,
      {
        goodReceiptId,
        bastNumber: bastData.bastNumber,
        vendorRepresentativeId,
        internalRepresentativeId,
      }
    );

    return document;
  }

  // Generate Vendor Evaluation Report
  async generateVendorEvaluationReport(
    evaluationId: string,
    userId: string
  ): Promise<any> {
    // Get evaluation details
    const evaluation = await prisma.vendorEvaluation.findUnique({
      where: { id: evaluationId },
      include: {
        procurement: {
          select: {
            id: true,
            title: true,
            procurementNumber: true,
          }
        },
        vendor: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true,
            contactEmail: true,
            contactPhone: true,
          }
        },
        // evaluator: {
        //   select: {
        //     id: true,
        //     name: true,
        //     email: true,
        //   }
        // },
        // criteria: true,
      }
    });

    if (!evaluation) {
      throw new Error("Vendor evaluation not found");
    }

    // Prepare evaluation data
    const evaluationData = {
      evaluationId: evaluation.id,
      evaluationDate: evaluation.evaluatedAt,
      procurement: evaluation.procurement,
      vendor: evaluation.vendor,
      evaluator: {
        name: "Evaluator Name", // evaluation.evaluator.name,
        title: "Evaluation Officer",
      },
      reviewer: {
        name: "Manager", // This should come from approval workflow
        title: "Procurement Manager",
      },
      criteria: [], // evaluation.criteria.map((criterion: any) => ({
        // name: criterion.name,
        // weight: criterion.weight,
        // score: criterion.score,
        // weightedScore: (criterion.score * criterion.weight) / 100,
        // rating: this.getScoreRating(criterion.score),
      // })),
      totalScore: evaluation.score,
      overallRating: this.getScoreRating(evaluation.score),
      recommendation: this.getRecommendation(evaluation.score),
      strengths: (evaluation as any).strengths || [],
      weaknesses: (evaluation as any).weaknesses || [],
      comments: (evaluation as any).comments || evaluation.notes,
      status: "COMPLETED", // evaluation.status,
    };

    // Get evaluation template
    const templates = await documentTemplateEngine.getTemplatesByType("VENDOR_EVALUATION");
    if (templates.length === 0) {
      throw new Error("No Vendor Evaluation template found");
    }

    // Generate document
    const document = await documentTemplateEngine.generateDocument(
      {
        templateId: templates[0].id,
        data: evaluationData,
        fileName: `Vendor_Evaluation_${evaluation.vendor?.companyName || 'Vendor'}_${evaluation.id}.pdf`,
        metadata: {
          evaluationId,
          procurementId: evaluation.procurementId,
          vendorId: evaluation.vendorId,
          generationType: "MANUAL",
        }
      },
      userId,
      "VENDOR_EVALUATION",
      evaluationId
    );

    // Log document generation
    await auditLogger.logCrudOperation(
      userId,
      "CREATE",
      "VENDOR_EVALUATION_DOCUMENT",
      document.id,
      undefined,
      {
        evaluationId,
        vendorId: evaluation.vendorId,
        totalScore: evaluation.score,
        recommendation: evaluationData.recommendation,
      }
    );

    return document;
  }

  // Helper methods
  private async generatePONumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Get the last PO number for this month
    const lastPO = await prisma.purchaseOrder.findFirst({
      where: {
        poNumber: {
          startsWith: `PO/${year}/${month}/`
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    let sequence = 1;
    if (lastPO) {
      const lastSequence = parseInt(lastPO.poNumber.split('/').pop() || '0');
      sequence = lastSequence + 1;
    }

    return `PO/${year}/${month}/${String(sequence).padStart(4, '0')}`;
  }

  private async generateBASTNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Get the last BAST number for this month
    const lastBAST = await prisma.bAST.findFirst({
      where: {
        bastNumber: {
          startsWith: `BAST/${year}/${month}/`
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    let sequence = 1;
    if (lastBAST) {
      const lastSequence = parseInt(lastBAST.bastNumber.split('/').pop() || '0');
      sequence = lastSequence + 1;
    }

    return `BAST/${year}/${month}/${String(sequence).padStart(4, '0')}`;
  }

  private async getCompanyInfo(): Promise<any> {
    // This should be configurable in the system
    return {
      name: "PT. E-Procurement Indonesia",
      address: "Jl. Sudirman No. 123, Jakarta 10220",
      phone: "+62 21 1234 5678",
      email: "<EMAIL>",
      website: "www.e-procurement.co.id",
    };
  }

  private getScoreRating(score: number): string {
    if (score >= 90) return "Excellent";
    if (score >= 80) return "Very Good";
    if (score >= 70) return "Good";
    if (score >= 60) return "Fair";
    return "Poor";
  }

  private getRecommendation(score: number): string {
    if (score >= 80) return "Highly Recommended";
    if (score >= 70) return "Recommended";
    if (score >= 60) return "Conditionally Recommended";
    return "Not Recommended";
  }
}

export const procurementDocumentGenerator = new ProcurementDocumentGenerator();
