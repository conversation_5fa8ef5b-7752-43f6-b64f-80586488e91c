import { promises as fs } from "fs";
import path from "path";

import { format } from "date-fns";
import { id } from "date-fns/locale";
import puppeteer from "puppeteer";

import { prisma } from "@/lib/db";
import { toPrismaJson } from "@/lib/utils/json";

export interface DocumentTemplate {
  id: string;
  name: string;
  type: DocumentType;
  htmlTemplate: string;
  cssStyles?: string;
  variables: DocumentVariable[];
  signatureFields: SignatureField[];
  isActive: boolean;
  version: number;
}

export interface DocumentVariable {
  name: string;
  type: "text" | "number" | "date" | "currency" | "boolean" | "array" | "object";
  required: boolean;
  defaultValue?: any;
  description?: string;
}

export interface SignatureField {
  id: string;
  name: string;
  stepSequence?: number;
  position: { x: number; y: number; page?: number };
  size: { width: number; height: number };
  required: boolean;
  signerRole?: string;
  signerTitle?: string;
}

export type DocumentType = 
  | "CONTRACT" 
  | "PURCHASE_ORDER" 
  | "INVOICE" 
  | "DELIVERY_NOTE" 
  | "BAST" 
  | "GRN" 
  | "PAYMENT_VOUCHER"
  | "VENDOR_CERTIFICATE"
  | "PROCUREMENT_ANNOUNCEMENT"
  | "EVALUATION_REPORT";

export interface GenerateDocumentOptions {
  templateId: string;
  data: Record<string, any>;
  outputFormat: "pdf" | "html";
  fileName?: string;
  watermark?: string;
  approvalWorkflowId?: string;
  entityId?: string;
  entityType?: string;
  digitalSignatures?: Array<{
    signerName: string;
    signerTitle: string;
    signatureImage?: string;
    timestamp: Date;
    position?: { x: number; y: number; page?: number };
    size?: { width: number; height: number };
    stepSequence?: number;
    approved?: boolean;
  }>;
  generateSignaturePlaceholders?: boolean;
}

export interface DocumentGenerationResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  signatureFields?: SignatureField[];
  pendingSignatures?: Array<{
    stepSequence: number;
    signerRole: string;
    position: { x: number; y: number; page?: number };
  }>;
  error?: string;
}

export class EnhancedDocumentGenerator {
  private templatesPath: string;
  private outputPath: string;

  constructor() {
    this.templatesPath = path.join(process.cwd(), "src/templates/documents");
    this.outputPath = path.join(process.cwd(), "generated-documents");
  }

  // Generate document with approval workflow integration
  async generateDocument(options: GenerateDocumentOptions): Promise<DocumentGenerationResult> {
    try {
      // Load template from database
      const template = await this.loadTemplateFromDB(options.templateId);
      if (!template) {
        return { success: false, error: "Template not found" };
      }

      // Validate required variables
      const validation = this.validateTemplateData(template, options.data);
      if (!validation.valid) {
        return { success: false, error: `Missing required variables: ${validation.missing.join(", ")}` };
      }

      // Get approval workflow signatures if workflow is provided
      let workflowSignatures: SignatureField[] = [];
      if (options.approvalWorkflowId) {
        workflowSignatures = await this.getWorkflowSignatures(
          options.approvalWorkflowId,
          options.entityId,
          options.entityType
        );
      }

      // Merge template signatures with workflow signatures
      const allSignatureFields = [...template.signatureFields, ...workflowSignatures];

      // Process template with data and signatures
      const processedHtml = await this.processTemplate(
        template, 
        options.data, 
        allSignatureFields,
        options.digitalSignatures,
        options.generateSignaturePlaceholders
      );

      // Generate output based on format
      if (options.outputFormat === "pdf") {
        const result = await this.generatePDF(processedHtml, options);
        return {
          ...result,
          signatureFields: allSignatureFields,
          pendingSignatures: this.getPendingSignatures(allSignatureFields, options.digitalSignatures),
        };
      } else {
        const result = await this.generateHTML(processedHtml, options);
        return {
          ...result,
          signatureFields: allSignatureFields,
          pendingSignatures: this.getPendingSignatures(allSignatureFields, options.digitalSignatures),
        };
      }
    } catch (error) {
      console.error("Document generation error:", error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      };
    }
  }

  // Load template from database
  private async loadTemplateFromDB(templateId: string): Promise<DocumentTemplate | null> {
    try {
      const template = await prisma.documentTemplate.findUnique({
        where: { id: templateId, isActive: true },
      });

      if (!template) return null;

      return {
        id: template.id,
        name: template.name,
        type: template.type as DocumentType,
        htmlTemplate: (template.content as any).htmlTemplate || "",
        cssStyles: (template.content as any).cssStyles,
        variables: (template.variables as any) || [],
        signatureFields: (template.content as any).signatureFields || [],
        isActive: template.isActive,
        version: template.version,
      };
    } catch (error) {
      console.error("Template loading error:", error);
      return null;
    }
  }

  // Get signature fields from approval workflow
  private async getWorkflowSignatures(
    workflowId: string,
    entityId?: string,
    entityType?: string
  ): Promise<SignatureField[]> {
    try {
      const workflow = await prisma.approvalWorkflow.findUnique({
        where: { id: workflowId },
        include: {
          steps: {
            where: {
              stepType: "SIGNATURE",
            },
            orderBy: { sequence: "asc" },
          },
        },
      });

      if (!workflow) return [];

      const signatureFields: SignatureField[] = [];

      for (const step of workflow.steps) {
        const signatureConfig = step.signatureConfig as any;
        if (signatureConfig) {
          signatureFields.push({
            id: `workflow_${step.id}`,
            name: step.name,
            stepSequence: step.sequence,
            position: signatureConfig.position,
            size: signatureConfig.size,
            required: signatureConfig.required || step.isRequired,
            signerRole: step.approverType,
            signerTitle: step.name,
          });
        }
      }

      return signatureFields;
    } catch (error) {
      console.error("Error getting workflow signatures:", error);
      return [];
    }
  }

  // Get pending signatures
  private getPendingSignatures(
    signatureFields: SignatureField[],
    providedSignatures?: Array<{ stepSequence?: number; approved?: boolean }>
  ) {
    return signatureFields
      .filter(field => {
        if (!field.stepSequence) return true;
        const provided = providedSignatures?.find(sig => sig.stepSequence === field.stepSequence);
        return !provided || !provided.approved;
      })
      .map(field => ({
        stepSequence: field.stepSequence || 0,
        signerRole: field.signerRole || "Unknown",
        position: field.position,
      }));
  }

  // Validate template data against required variables
  private validateTemplateData(template: DocumentTemplate, data: Record<string, any>) {
    const missing: string[] = [];
    
    for (const variable of template.variables) {
      if (variable.required && !(variable.name in data)) {
        missing.push(variable.name);
      }
    }

    return {
      valid: missing.length === 0,
      missing,
    };
  }

  // Process template with data and signatures
  private async processTemplate(
    template: DocumentTemplate, 
    data: Record<string, any>,
    signatureFields: SignatureField[],
    digitalSignatures?: Array<any>,
    generatePlaceholders?: boolean
  ): Promise<string> {
    let html = template.htmlTemplate;

    // Add CSS styles
    if (template.cssStyles) {
      html = `<style>${template.cssStyles}</style>${html}`;
    }

    // Replace variables in template
    for (const variable of template.variables) {
      const value = data[variable.name] ?? variable.defaultValue ?? "";
      const formattedValue = this.formatValue(value, variable.type);
      
      // Replace all occurrences of {{variableName}}
      const regex = new RegExp(`{{\\s*${variable.name}\\s*}}`, "g");
      html = html.replace(regex, formattedValue);
    }

    // Process loops and conditionals
    html = this.processTemplateLogic(html, data);

    // Add signature fields
    html = this.addSignatureFields(html, signatureFields, digitalSignatures, generatePlaceholders);

    // Add Indonesian formatting
    html = this.addIndonesianFormatting(html);

    return html;
  }

  // Add signature fields to HTML
  private addSignatureFields(
    html: string,
    signatureFields: SignatureField[],
    digitalSignatures?: Array<any>,
    generatePlaceholders?: boolean
  ): string {
    let signatureHtml = "";

    for (const field of signatureFields) {
      const providedSignature = digitalSignatures?.find(
        sig => sig.stepSequence === field.stepSequence
      );

      if (providedSignature && providedSignature.approved) {
        // Add actual signature
        signatureHtml += `
          <div class="signature-field" style="
            position: absolute;
            left: ${field.position.x}px;
            top: ${field.position.y}px;
            width: ${field.size.width}px;
            height: ${field.size.height}px;
            border: 1px solid #ccc;
            text-align: center;
            padding: 5px;
          ">
            ${providedSignature.signatureImage ? 
              `<img src="${providedSignature.signatureImage}" style="max-width: 100%; max-height: 60%;" />` :
              `<div style="font-weight: bold;">${providedSignature.signerName}</div>`
            }
            <div style="font-size: 10px;">${providedSignature.signerTitle}</div>
            <div style="font-size: 8px;">${format(new Date(providedSignature.timestamp), "dd/MM/yyyy HH:mm", { locale: id })}</div>
          </div>
        `;
      } else if (generatePlaceholders) {
        // Add signature placeholder
        signatureHtml += `
          <div class="signature-placeholder" style="
            position: absolute;
            left: ${field.position.x}px;
            top: ${field.position.y}px;
            width: ${field.size.width}px;
            height: ${field.size.height}px;
            border: 2px dashed #ccc;
            text-align: center;
            padding: 5px;
            background-color: #f9f9f9;
          ">
            <div style="font-size: 10px; color: #666;">
              ${field.name}
              ${field.required ? " (Required)" : " (Optional)"}
            </div>
            <div style="font-size: 8px; color: #999;">
              ${field.signerTitle || field.signerRole}
            </div>
          </div>
        `;
      }
    }

    // Add signature fields to the end of the document
    if (signatureHtml) {
      html += `<div class="signature-container">${signatureHtml}</div>`;
    }

    return html;
  }

  // Format values based on type
  private formatValue(value: any, type: string): string {
    switch (type) {
      case "currency":
        return new Intl.NumberFormat("id-ID", {
          style: "currency",
          currency: "IDR",
          minimumFractionDigits: 0,
        }).format(Number(value) || 0);
      
      case "number":
        return Number(value || 0).toLocaleString("id-ID");
      
      case "date":
        if (!value) return "";
        return format(new Date(value), "dd MMMM yyyy", { locale: id });
      
      case "boolean":
        return value ? "Ya" : "Tidak";
      
      case "array":
        if (Array.isArray(value)) {
          return value.join(", ");
        }
        return String(value || "");
      
      default:
        return String(value || "");
    }
  }

  // Process template logic (loops, conditionals)
  private processTemplateLogic(html: string, data: Record<string, any>): string {
    // Process {{#each array}} loops
    html = html.replace(/{{#each\s+(\w+)}}([\s\S]*?){{\/each}}/g, (match, arrayName, content) => {
      const array = data[arrayName];
      if (!Array.isArray(array)) return "";
      
      return array.map((item, index) => {
        let itemContent = content;
        // Replace {{this.property}} with item values
        itemContent = itemContent.replace(/{{this\.(\w+)}}/g, (_: any, prop: any) => {
          return this.formatValue(item[prop], "text");
        });
        // Replace {{@index}} with current index
        itemContent = itemContent.replace(/{{@index}}/g, String(index + 1));
        return itemContent;
      }).join("");
    });

    // Process {{#if condition}} conditionals
    html = html.replace(/{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g, (match, condition, content) => {
      return data[condition] ? content : "";
    });

    return html;
  }

  // Add Indonesian-specific formatting
  private addIndonesianFormatting(html: string): string {
    // Add Indonesian document styling
    const indonesianStyles = `
      <style>
        body { font-family: 'Times New Roman', serif; font-size: 12pt; line-height: 1.5; }
        .header { text-align: center; margin-bottom: 30px; }
        .letterhead { border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
        .document-title { font-size: 16pt; font-weight: bold; text-align: center; margin: 20px 0; }
        .signature-section { margin-top: 50px; position: relative; }
        .signature-field { border: 1px solid #000; margin: 10px 0; }
        .signature-placeholder { border: 2px dashed #ccc; margin: 10px 0; background-color: #f9f9f9; }
        .signature-container { position: relative; }
        .page-break { page-break-before: always; }
        .currency { text-align: right; }
        .center { text-align: center; }
        .bold { font-weight: bold; }
        .underline { text-decoration: underline; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f0f0f0; font-weight: bold; }
        .no-border { border: none; }
        .footer { position: fixed; bottom: 0; width: 100%; font-size: 10pt; text-align: center; }
        @media print {
          .signature-placeholder { display: none; }
        }
      </style>
    `;

    return indonesianStyles + html;
  }

  // Generate PDF using Puppeteer
  private async generatePDF(html: string, options: GenerateDocumentOptions): Promise<DocumentGenerationResult> {
    let browser;
    try {
      // Ensure output directory exists
      await fs.mkdir(this.outputPath, { recursive: true });

      // Launch browser
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      
      // Set content
      await page.setContent(html, { waitUntil: 'networkidle0' });

      // Generate filename
      const fileName = options.fileName || `document_${Date.now()}.pdf`;
      const filePath = path.join(this.outputPath, fileName);

      // PDF options
      const pdfOptions: any = {
        path: filePath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm',
        },
        displayHeaderFooter: true,
        headerTemplate: '<div></div>',
        footerTemplate: `
          <div style="font-size: 10px; text-align: center; width: 100%;">
            <span>Halaman <span class="pageNumber"></span> dari <span class="totalPages"></span></span>
          </div>
        `,
      };

      // Add watermark if specified
      if (options.watermark) {
        await page.evaluate((watermark) => {
          const style = document.createElement('style');
          style.textContent = `
            body::before {
              content: "${watermark}";
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) rotate(-45deg);
              font-size: 72px;
              color: rgba(0, 0, 0, 0.1);
              z-index: 1000;
              pointer-events: none;
            }
          `;
          document.head.appendChild(style);
        }, options.watermark);
      }

      // Generate PDF
      await page.pdf(pdfOptions);

      // Get file stats
      const stats = await fs.stat(filePath);

      return {
        success: true,
        filePath,
        fileName,
        fileSize: stats.size,
      };
    } catch (error) {
      console.error("PDF generation error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "PDF generation failed",
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  // Generate HTML file
  private async generateHTML(html: string, options: GenerateDocumentOptions): Promise<DocumentGenerationResult> {
    try {
      // Ensure output directory exists
      await fs.mkdir(this.outputPath, { recursive: true });

      // Generate filename
      const fileName = options.fileName || `document_${Date.now()}.html`;
      const filePath = path.join(this.outputPath, fileName);

      // Write HTML file
      await fs.writeFile(filePath, html, "utf-8");

      // Get file stats
      const stats = await fs.stat(filePath);

      return {
        success: true,
        filePath,
        fileName,
        fileSize: stats.size,
      };
    } catch (error) {
      console.error("HTML generation error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "HTML generation failed",
      };
    }
  }

  // Create or update template in database
  async createTemplate(template: Omit<DocumentTemplate, "id" | "version">, createdById: string): Promise<string> {
    const created = await prisma.documentTemplate.create({
      data: {
        name: template.name,
        type: template.type as any,
        category: "SYSTEM",
        content: toPrismaJson({
          htmlTemplate: template.htmlTemplate,
          cssStyles: template.cssStyles,
          signatureFields: template.signatureFields,
        }),
        variables: toPrismaJson(template.variables),
        status: "APPROVED",
        isActive: template.isActive,
        createdBy: createdById,
      },
    });

    return created.id;
  }

  // List available templates
  async listTemplates(type?: DocumentType): Promise<DocumentTemplate[]> {
    try {
      const templates = await prisma.documentTemplate.findMany({
        where: {
          isActive: true,
          ...(type && { type: type as any }),
        },
        orderBy: { name: "asc" },
      });

      return templates.map((template: any) => ({
        id: template.id,
        name: template.name,
        type: template.type as DocumentType,
        htmlTemplate: (template.content as any).htmlTemplate || "",
        cssStyles: (template.content as any).cssStyles,
        variables: (template.variables as any) || [],
        signatureFields: (template.content as any).signatureFields || [],
        isActive: template.isActive,
        version: template.version,
      }));
    } catch (error) {
      console.error("Error listing templates:", error);
      return [];
    }
  }
}

// Singleton instance
export const enhancedDocumentGenerator = new EnhancedDocumentGenerator();
