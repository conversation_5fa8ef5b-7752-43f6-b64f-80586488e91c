import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

export enum DocumentPermission {
  READ = 'READ',
  WRITE = 'WRITE',
  DELETE = 'DELETE',
  SHARE = 'SHARE',
  APPROVE = 'APPROVE',
  ADMIN = 'ADMIN',
  DOWNLOAD = 'DOWNLOAD'
}

export enum AccessLevel {
  PUBLIC = 'PUBLIC',
  INTERNAL = 'INTERNAL',
  CONFIDENTIAL = 'CONFIDENTIAL',
  RESTRICTED = 'RESTRICTED'
}

export interface DocumentAccessRule {
  id: string;
  documentId: string;
  entityType: 'USER' | 'ROLE' | 'DEPARTMENT' | 'ORGANIZATION';
  entityId: string;
  permissions: DocumentPermission[];
  accessLevel: AccessLevel;
  conditions?: {
    timeRestriction?: {
      startDate?: Date;
      endDate?: Date;
      allowedHours?: { start: number; end: number };
    };
    ipRestriction?: string[];
    locationRestriction?: string[];
  };
  createdAt: Date;
  createdBy: string;
}

export interface AuditLogEntry {
  id: string;
  documentId: string;
  userId: string;
  action: 'VIEW' | 'DOWNLOAD' | 'EDIT' | 'DELETE' | 'SHARE' | 'APPROVE' | 'REJECT';
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

export class DocumentSecurity {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = process.env.DOCUMENT_ENCRYPTION_KEY || 'default-key-change-in-production';
  }

  /**
   * Check if user has permission to perform action on document
   */
  async checkPermission(
    documentId: string,
    userId: string,
    permission: DocumentPermission,
    context?: {
      ipAddress?: string;
      userAgent?: string;
      location?: string;
    }
  ): Promise<{ allowed: boolean; reason?: string }> {
    try {
      // TODO: Implement actual permission checking once schema is fixed
      // For now, return mock permission check
      
      // Get user roles and permissions
      const userRoles = await this.getUserRoles(userId);
      const documentAccessRules = await this.getDocumentAccessRules(documentId);
      
      // Check direct user permissions
      const userRule = documentAccessRules.find(rule => 
        rule.entityType === 'USER' && rule.entityId === userId
      );
      
      if (userRule && userRule.permissions.includes(permission)) {
        const conditionCheck = await this.checkAccessConditions(userRule, context);
        if (!conditionCheck.allowed) {
          return conditionCheck;
        }
        return { allowed: true };
      }

      // Check role-based permissions
      for (const role of userRoles) {
        const roleRule = documentAccessRules.find(rule => 
          rule.entityType === 'ROLE' && rule.entityId === role
        );
        
        if (roleRule && roleRule.permissions.includes(permission)) {
          const conditionCheck = await this.checkAccessConditions(roleRule, context);
          if (!conditionCheck.allowed) {
            return conditionCheck;
          }
          return { allowed: true };
        }
      }

      // Check if user is document owner
      const isOwner = await this.isDocumentOwner(documentId, userId);
      if (isOwner) {
        return { allowed: true };
      }

      return { 
        allowed: false, 
        reason: `Insufficient permissions. Required: ${permission}` 
      };
    } catch (error) {
      console.error('Error checking document permission:', error);
      return { 
        allowed: false, 
        reason: 'Permission check failed' 
      };
    }
  }

  /**
   * Grant permission to user or role for document
   */
  async grantPermission(
    documentId: string,
    entityType: 'USER' | 'ROLE' | 'DEPARTMENT',
    entityId: string,
    permissions: DocumentPermission[],
    accessLevel: AccessLevel,
    grantedBy: string,
    conditions?: DocumentAccessRule['conditions']
  ): Promise<void> {
    try {
      // TODO: Implement actual permission granting once schema is fixed
      console.log(`Grant permissions to ${entityType} ${entityId} for document ${documentId}:`, {
        permissions,
        accessLevel,
        conditions,
        grantedBy,
      });

      // Log the permission grant
      await this.logAuditEvent({
        documentId,
        userId: grantedBy,
        action: 'SHARE',
        details: {
          entityType,
          entityId,
          permissions,
          accessLevel,
          conditions,
        },
        success: true,
      });
    } catch (error) {
      console.error('Error granting document permission:', error);
      throw error;
    }
  }

  /**
   * Revoke permission from user or role for document
   */
  async revokePermission(
    documentId: string,
    entityType: 'USER' | 'ROLE' | 'DEPARTMENT',
    entityId: string,
    revokedBy: string
  ): Promise<void> {
    try {
      // TODO: Implement actual permission revocation once schema is fixed
      console.log(`Revoke permissions from ${entityType} ${entityId} for document ${documentId} by ${revokedBy}`);

      // Log the permission revocation
      await this.logAuditEvent({
        documentId,
        userId: revokedBy,
        action: 'SHARE',
        details: {
          action: 'revoke',
          entityType,
          entityId,
        },
        success: true,
      });
    } catch (error) {
      console.error('Error revoking document permission:', error);
      throw error;
    }
  }

  /**
   * Encrypt document content
   */
  encryptContent(content: string): { encryptedContent: string; iv: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    cipher.setAutoPadding(true);
    
    let encryptedContent = cipher.update(content, 'utf8', 'hex');
    encryptedContent += cipher.final('hex');
    
    return {
      encryptedContent,
      iv: iv.toString('hex'),
    };
  }

  /**
   * Decrypt document content
   */
  decryptContent(encryptedContent: string, iv: string): string {
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    decipher.setAutoPadding(true);
    
    let decryptedContent = decipher.update(encryptedContent, 'hex', 'utf8');
    decryptedContent += decipher.final('utf8');
    
    return decryptedContent;
  }

  /**
   * Generate secure sharing token
   */
  generateSharingToken(documentId: string, permissions: DocumentPermission[], expiresAt?: Date): string {
    const payload = {
      documentId,
      permissions,
      expiresAt: expiresAt?.getTime(),
      createdAt: Date.now(),
    };
    
    const token = crypto.createHmac('sha256', this.encryptionKey)
      .update(JSON.stringify(payload))
      .digest('hex');
    
    return Buffer.from(JSON.stringify({ ...payload, token })).toString('base64');
  }

  /**
   * Validate sharing token
   */
  validateSharingToken(token: string): { 
    valid: boolean; 
    documentId?: string; 
    permissions?: DocumentPermission[];
    expired?: boolean;
  } {
    try {
      const payload = JSON.parse(Buffer.from(token, 'base64').toString('utf8'));
      
      // Check if token has expired
      if (payload.expiresAt && Date.now() > payload.expiresAt) {
        return { valid: false, expired: true };
      }
      
      // Verify token signature
      const expectedToken = crypto.createHmac('sha256', this.encryptionKey)
        .update(JSON.stringify({
          documentId: payload.documentId,
          permissions: payload.permissions,
          expiresAt: payload.expiresAt,
          createdAt: payload.createdAt,
        }))
        .digest('hex');
      
      if (payload.token !== expectedToken) {
        return { valid: false };
      }
      
      return {
        valid: true,
        documentId: payload.documentId,
        permissions: payload.permissions,
      };
    } catch (error) {
      return { valid: false };
    }
  }

  /**
   * Log audit event
   */
  async logAuditEvent(event: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> {
    try {
      // TODO: Implement actual audit logging once schema is fixed
      const auditEntry: AuditLogEntry = {
        id: `audit-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        timestamp: new Date(),
        ...event,
      };
      
      console.log('Audit Log:', auditEntry);
      
      // In production, this would save to database
      // await prisma.documentAuditLog.create({ data: auditEntry });
    } catch (error) {
      console.error('Error logging audit event:', error);
      // Don't throw error to avoid breaking main functionality
    }
  }

  /**
   * Get audit trail for document
   */
  async getAuditTrail(
    documentId: string,
    options?: {
      userId?: string;
      action?: AuditLogEntry['action'];
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      offset?: number;
    }
  ): Promise<AuditLogEntry[]> {
    try {
      // TODO: Implement actual audit trail retrieval once schema is fixed
      // For now, return mock audit trail
      return [
        {
          id: 'audit-1',
          documentId,
          userId: 'user-1',
          action: 'VIEW',
          details: { method: 'web' },
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0...',
          timestamp: new Date(),
          success: true,
        },
      ];
    } catch (error) {
      console.error('Error getting audit trail:', error);
      return [];
    }
  }

  /**
   * Check access conditions (time, IP, location restrictions)
   */
  private async checkAccessConditions(
    rule: DocumentAccessRule,
    context?: {
      ipAddress?: string;
      userAgent?: string;
      location?: string;
    }
  ): Promise<{ allowed: boolean; reason?: string }> {
    if (!rule.conditions) {
      return { allowed: true };
    }

    // Check time restrictions
    if (rule.conditions.timeRestriction) {
      const now = new Date();
      const { startDate, endDate, allowedHours } = rule.conditions.timeRestriction;
      
      if (startDate && now < startDate) {
        return { allowed: false, reason: 'Access not yet allowed' };
      }
      
      if (endDate && now > endDate) {
        return { allowed: false, reason: 'Access expired' };
      }
      
      if (allowedHours) {
        const currentHour = now.getHours();
        if (currentHour < allowedHours.start || currentHour > allowedHours.end) {
          return { allowed: false, reason: 'Access not allowed at this time' };
        }
      }
    }

    // Check IP restrictions
    if (rule.conditions.ipRestriction && context?.ipAddress) {
      if (!rule.conditions.ipRestriction.includes(context.ipAddress)) {
        return { allowed: false, reason: 'Access not allowed from this IP address' };
      }
    }

    // Check location restrictions
    if (rule.conditions.locationRestriction && context?.location) {
      if (!rule.conditions.locationRestriction.includes(context.location)) {
        return { allowed: false, reason: 'Access not allowed from this location' };
      }
    }

    return { allowed: true };
  }

  /**
   * Get user roles
   */
  private async getUserRoles(userId: string): Promise<string[]> {
    try {
      // TODO: Implement actual user role retrieval once schema is fixed
      // For now, return mock roles
      return ['PROCUREMENT_USER', 'DOCUMENT_VIEWER'];
    } catch (error) {
      console.error('Error getting user roles:', error);
      return [];
    }
  }

  /**
   * Get document access rules
   */
  private async getDocumentAccessRules(documentId: string): Promise<DocumentAccessRule[]> {
    try {
      // TODO: Implement actual access rule retrieval once schema is fixed
      // For now, return mock rules
      return [];
    } catch (error) {
      console.error('Error getting document access rules:', error);
      return [];
    }
  }

  /**
   * Check if user is document owner
   */
  private async isDocumentOwner(documentId: string, userId: string): Promise<boolean> {
    try {
      // TODO: Implement actual ownership check once schema is fixed
      // For now, return false
      return false;
    } catch (error) {
      console.error('Error checking document ownership:', error);
      return false;
    }
  }

  /**
   * Get security summary for document
   */
  async getSecuritySummary(documentId: string): Promise<{
    accessLevel: AccessLevel;
    isEncrypted: boolean;
    hasTimeRestrictions: boolean;
    hasIpRestrictions: boolean;
    sharedWith: number;
    auditLogCount: number;
  }> {
    try {
      // TODO: Implement actual security summary once schema is fixed
      return {
        accessLevel: AccessLevel.INTERNAL,
        isEncrypted: false,
        hasTimeRestrictions: false,
        hasIpRestrictions: false,
        sharedWith: 0,
        auditLogCount: 0,
      };
    } catch (error) {
      console.error('Error getting security summary:', error);
      throw error;
    }
  }
}

export const documentSecurity = new DocumentSecurity();
