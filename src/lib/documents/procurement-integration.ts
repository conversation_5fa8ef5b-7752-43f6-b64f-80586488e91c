import { PrismaClient } from '@prisma/client';

import { documentManager } from './document-manager';
import { documentService } from './document-service';
import { templateEngine } from './template-engine';

const prisma = new PrismaClient();

export interface ProcurementStageDocumentConfig {
  stageType: string;
  requiredDocuments: {
    templateId: string;
    documentType: string;
    isRequired: boolean;
    autoGenerate: boolean;
    approvalRequired: boolean;
  }[];
  allowedDocumentTypes: string[];
  documentValidationRules: {
    maxFileSize: number;
    allowedFormats: string[];
    requiresDigitalSignature: boolean;
  };
}

export interface DocumentGenerationContext {
  procurementId: string;
  stageId: string;
  stageType: string;
  procurementData: any;
  vendorData?: any;
  additionalData?: Record<string, any>;
}

export class ProcurementDocumentIntegration {
  /**
   * Get document configuration for a procurement stage
   */
  async getStageDocumentConfig(stageType: string): Promise<ProcurementStageDocumentConfig> {
    // Define document requirements for each procurement stage
    const stageConfigs: Record<string, ProcurementStageDocumentConfig> = {
      ANNOUNCEMENT: {
        stageType: 'ANNOUNCEMENT',
        requiredDocuments: [
          {
            templateId: 'announcement-template',
            documentType: 'ANNOUNCEMENT',
            isRequired: true,
            autoGenerate: true,
            approvalRequired: true,
          },
          {
            templateId: 'rfq-template',
            documentType: 'RFQ',
            isRequired: true,
            autoGenerate: true,
            approvalRequired: true,
          },
        ],
        allowedDocumentTypes: ['ANNOUNCEMENT', 'RFQ', 'SPECIFICATION', 'TERMS_CONDITIONS'],
        documentValidationRules: {
          maxFileSize: 10 * 1024 * 1024, // 10MB
          allowedFormats: ['pdf', 'doc', 'docx'],
          requiresDigitalSignature: true,
        },
      },
      SUBMISSION: {
        stageType: 'SUBMISSION',
        requiredDocuments: [],
        allowedDocumentTypes: ['PROPOSAL', 'TECHNICAL_SPEC', 'FINANCIAL_PROPOSAL', 'COMPANY_PROFILE'],
        documentValidationRules: {
          maxFileSize: 50 * 1024 * 1024, // 50MB
          allowedFormats: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
          requiresDigitalSignature: false,
        },
      },
      EVALUATION: {
        stageType: 'EVALUATION',
        requiredDocuments: [
          {
            templateId: 'evaluation-template',
            documentType: 'EVALUATION_REPORT',
            isRequired: true,
            autoGenerate: false,
            approvalRequired: true,
          },
        ],
        allowedDocumentTypes: ['EVALUATION_REPORT', 'SCORING_SHEET', 'CLARIFICATION'],
        documentValidationRules: {
          maxFileSize: 20 * 1024 * 1024, // 20MB
          allowedFormats: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
          requiresDigitalSignature: true,
        },
      },
      AWARD: {
        stageType: 'AWARD',
        requiredDocuments: [
          {
            templateId: 'award-letter-template',
            documentType: 'AWARD_LETTER',
            isRequired: true,
            autoGenerate: true,
            approvalRequired: true,
          },
          {
            templateId: 'po-template',
            documentType: 'PURCHASE_ORDER',
            isRequired: true,
            autoGenerate: true,
            approvalRequired: true,
          },
        ],
        allowedDocumentTypes: ['AWARD_LETTER', 'PURCHASE_ORDER', 'CONTRACT'],
        documentValidationRules: {
          maxFileSize: 10 * 1024 * 1024, // 10MB
          allowedFormats: ['pdf', 'doc', 'docx'],
          requiresDigitalSignature: true,
        },
      },
      CONTRACT: {
        stageType: 'CONTRACT',
        requiredDocuments: [
          {
            templateId: 'contract-template',
            documentType: 'CONTRACT',
            isRequired: true,
            autoGenerate: true,
            approvalRequired: true,
          },
        ],
        allowedDocumentTypes: ['CONTRACT', 'ADDENDUM', 'AMENDMENT'],
        documentValidationRules: {
          maxFileSize: 20 * 1024 * 1024, // 20MB
          allowedFormats: ['pdf', 'doc', 'docx'],
          requiresDigitalSignature: true,
        },
      },
    };

    return stageConfigs[stageType] || {
      stageType,
      requiredDocuments: [],
      allowedDocumentTypes: [],
      documentValidationRules: {
        maxFileSize: 10 * 1024 * 1024,
        allowedFormats: ['pdf', 'doc', 'docx'],
        requiresDigitalSignature: false,
      },
    };
  }

  /**
   * Auto-generate required documents when a procurement stage starts
   */
  async generateStageDocuments(
    context: DocumentGenerationContext,
    userId: string
  ): Promise<any[]> {
    try {
      const stageConfig = await this.getStageDocumentConfig(context.stageType);
      const generatedDocuments = [];

      for (const docConfig of stageConfig.requiredDocuments) {
        if (docConfig.autoGenerate) {
          try {
            // Prepare template data
            const templateData = await this.prepareTemplateData(context, docConfig.documentType);

            // Generate document from template
            const document = await templateEngine.generateDocument(userId, {
              templateId: docConfig.templateId,
              name: `${docConfig.documentType}_${context.procurementId}_${Date.now()}`,
              data: templateData,
              entityType: 'PROCUREMENT',
              entityId: context.procurementId,
              generatePdf: true,
            });

            // Upload generated document to document management
            const uploadedDoc = await documentService.uploadDocument(userId, {
              buffer: Buffer.from(JSON.stringify(document.content)),
              originalname: `${document.name}.json`,
              mimetype: 'application/json',
            }, {
              documentType: docConfig.documentType,
              description: `Auto-generated ${docConfig.documentType} for procurement ${context.procurementId}`,
              procurementId: context.procurementId,
              requiresApproval: docConfig.approvalRequired,
              tags: ['auto-generated', 'procurement', context.stageType.toLowerCase()],
              metadata: {
                templateId: docConfig.templateId,
                stageId: context.stageId,
                stageType: context.stageType,
                generatedAt: new Date(),
              },
            });

            generatedDocuments.push({
              ...uploadedDoc,
              templateDocument: document,
            });

          } catch (error) {
            console.error(`Error generating document ${docConfig.documentType}:`, error);
            // Continue with other documents even if one fails
          }
        }
      }

      return generatedDocuments;
    } catch (error) {
      console.error('Error generating stage documents:', error);
      throw error;
    }
  }

  /**
   * Prepare template data based on procurement and context
   */
  private async prepareTemplateData(
    context: DocumentGenerationContext,
    documentType: string
  ): Promise<Record<string, any>> {
    // For now, return mock data
    // TODO: Implement actual data preparation from procurement records
    const baseData = {
      procurement: {
        id: context.procurementId,
        title: 'Sample Procurement',
        description: 'Sample procurement description',
        estimatedValue: 1000000,
        currency: 'IDR',
        category: 'GOODS',
        method: 'TENDER',
        createdAt: new Date(),
      },
      organization: {
        name: 'Sample Organization',
        address: 'Sample Address',
        phone: '+62-21-1234567',
        email: '<EMAIL>',
      },
      stage: {
        id: context.stageId,
        type: context.stageType,
        name: `${context.stageType} Stage`,
      },
      currentDate: new Date(),
      currentYear: new Date().getFullYear(),
      ...context.additionalData,
    };

    // Add document-specific data
    switch (documentType) {
      case 'RFQ':
        return {
          ...baseData,
          rfq: {
            number: `RFQ-${new Date().getFullYear()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
            submissionDeadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
            openingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
          },
        };

      case 'CONTRACT':
        return {
          ...baseData,
          contract: {
            number: `CONT-${new Date().getFullYear()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
            startDate: new Date(),
            endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
            vendor: context.vendorData || {
              name: 'Selected Vendor',
              address: 'Vendor Address',
              contact: '<EMAIL>',
            },
          },
        };

      case 'PURCHASE_ORDER':
        return {
          ...baseData,
          purchaseOrder: {
            number: `PO-${new Date().getFullYear()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
            deliveryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            vendor: context.vendorData || {
              name: 'Selected Vendor',
              address: 'Vendor Address',
              contact: '<EMAIL>',
            },
          },
        };

      case 'AWARD_LETTER':
        return {
          ...baseData,
          award: {
            winnerVendor: context.vendorData || {
              name: 'Winning Vendor',
              address: 'Winner Address',
              contact: '<EMAIL>',
            },
            awardDate: new Date(),
            awardValue: baseData.procurement.estimatedValue,
          },
        };

      default:
        return baseData;
    }
  }

  /**
   * Validate document against stage requirements
   */
  async validateStageDocument(
    stageType: string,
    documentType: string,
    file: { buffer: Buffer; originalname: string; mimetype: string }
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];
    const stageConfig = await this.getStageDocumentConfig(stageType);

    // Check if document type is allowed
    if (!stageConfig.allowedDocumentTypes.includes(documentType)) {
      errors.push(`Document type '${documentType}' is not allowed for stage '${stageType}'`);
    }

    // Validate file size
    if (file.buffer.length > stageConfig.documentValidationRules.maxFileSize) {
      errors.push(`File size exceeds maximum allowed size of ${stageConfig.documentValidationRules.maxFileSize} bytes`);
    }

    // Validate file format
    const fileExtension = file.originalname.split('.').pop()?.toLowerCase();
    if (fileExtension && !stageConfig.documentValidationRules.allowedFormats.includes(fileExtension)) {
      errors.push(`File format '${fileExtension}' is not allowed. Allowed formats: ${stageConfig.documentValidationRules.allowedFormats.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get documents for a specific procurement stage
   */
  async getStageDocuments(procurementId: string, stageId: string): Promise<any[]> {
    try {
      // For now, return mock documents
      // TODO: Implement actual document retrieval once schema is fixed
      return [];
    } catch (error) {
      console.error('Error getting stage documents:', error);
      throw error;
    }
  }

  /**
   * Link document to procurement stage
   */
  async linkDocumentToStage(
    documentId: string,
    procurementId: string,
    stageId: string,
    stageType: string
  ): Promise<void> {
    try {
      // For now, just log the linking
      // TODO: Implement actual document linking once schema is fixed
      console.log(`Linking document ${documentId} to procurement ${procurementId}, stage ${stageId} (${stageType})`);
    } catch (error) {
      console.error('Error linking document to stage:', error);
      throw error;
    }
  }

  /**
   * Get template suggestions for a procurement stage
   */
  async getStageTemplateSuggestions(
    stageType: string,
    procurementType?: string,
    category?: string
  ): Promise<any[]> {
    try {
      const stageConfig = await this.getStageDocumentConfig(stageType);
      const suggestions = await templateEngine.getTemplateSuggestions(
        procurementType || '',
        stageType,
        category
      );

      // Add stage-specific template recommendations
      const stageTemplates = stageConfig.requiredDocuments.map(doc => ({
        templateId: doc.templateId,
        documentType: doc.documentType,
        isRequired: doc.isRequired,
        autoGenerate: doc.autoGenerate,
        approvalRequired: doc.approvalRequired,
      }));

      return [...suggestions, ...stageTemplates];
    } catch (error) {
      console.error('Error getting stage template suggestions:', error);
      return [];
    }
  }
}

export const procurementDocumentIntegration = new ProcurementDocumentIntegration();
