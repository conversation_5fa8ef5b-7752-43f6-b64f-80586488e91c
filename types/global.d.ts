/// <reference types="node" />

declare global {
  // Remove empty Promise interfaces as they're already defined in TypeScript lib

  namespace NodeJS {
    interface Process {
      env: ProcessEnv;
      exit(code?: number): never;
      argv: string[];
    }

    interface ProcessEnv {
      [key: string]: string | undefined;
    }
  }

  const process: NodeJS.Process;
  const Buffer: BufferConstructor;
  const console: Console;

  interface Console {
    log(...data: unknown[]): void;
    error(...data: unknown[]): void;
    warn(...data: unknown[]): void;
    info(...data: unknown[]): void;
  }
}

// Module declarations for packages without types
declare module 'bcryptjs' {
  export function hash(data: string, saltOrRounds: string | number): Promise<string>;
  export function compare(data: string, encrypted: string): Promise<boolean>;
  export function genSalt(rounds?: number): Promise<string>;
  export function hashSync(data: string, saltOrRounds: string | number): string;
  export function compareSync(data: string, encrypted: string): boolean;
  export function genSaltSync(rounds?: number): string;
}

declare module 'jsonwebtoken' {
  export interface SignOptions {
    expiresIn?: string | number;
    algorithm?: string;
    audience?: string | string[];
    issuer?: string;
    jwtid?: string;
    subject?: string;
    noTimestamp?: boolean;
    header?: Record<string, unknown>;
    keyid?: string;
  }

  export interface VerifyOptions {
    algorithms?: string[];
    audience?: string | string[];
    issuer?: string | string[];
    jwtid?: string;
    subject?: string;
    clockTolerance?: number;
    maxAge?: string | number;
    clockTimestamp?: number;
    nonce?: string;
  }

  export interface JwtPayload {
    iss?: string;
    sub?: string;
    aud?: string | string[];
    exp?: number;
    nbf?: number;
    iat?: number;
    jti?: string;
    [key: string]: unknown;
  }

  export function sign(payload: string | Buffer | object, secretOrPrivateKey: string, options?: SignOptions): string;
  export function verify(token: string, secretOrPublicKey: string, options?: VerifyOptions): JwtPayload | string;
  export function decode(token: string, options?: { complete?: boolean; json?: boolean }): JwtPayload | string | null;
}

declare module '@jest/globals' {
  export interface DescribeFunction {
    (name: string, fn: () => void): void;
    each<T extends readonly unknown[]>(table: T): (name: string, fn: (...args: T[number][]) => void) => void;
    only: DescribeFunction;
    skip: DescribeFunction;
  }

  export interface TestFunction {
    (name: string, fn?: () => void | Promise<void>, timeout?: number): void;
    each<T extends readonly unknown[]>(table: T): (name: string, fn: (...args: T[number][]) => void | Promise<void>, timeout?: number) => void;
    only: TestFunction;
    skip: TestFunction;
    todo: (name: string) => void;
  }

  export interface ExpectFunction {
    <T = unknown>(actual: T): jest.Matchers<void, T>;
    extend(matchers: Record<string, unknown>): void;
  }

  export interface HookFunction {
    (fn: () => void | Promise<void>, timeout?: number): void;
  }

  export const describe: DescribeFunction;
  export const it: TestFunction;
  export const test: TestFunction;
  export const expect: ExpectFunction;
  export const beforeEach: HookFunction;
  export const afterEach: HookFunction;
  export const beforeAll: HookFunction;
  export const afterAll: HookFunction;
  export const jest: typeof import('jest');
}

export {};
