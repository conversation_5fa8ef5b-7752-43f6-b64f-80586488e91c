/// <reference types="react" />
/// <reference types="react-dom" />

declare namespace React {
  // Remove empty interfaces as they're already properly defined in @types/react

  // Element reference types with proper typing
  type ElementRef<T extends keyof JSX.IntrinsicElements | React.ComponentType<unknown>> =
    T extends keyof JSX.IntrinsicElements
      ? JSX.IntrinsicElements[T] extends React.DetailedHTMLProps<infer _P, infer E>
        ? E
        : never
      : T extends React.ComponentType<infer _P>
        ? T extends React.ForwardRefExoticComponent<infer P>
          ? P extends React.RefAttributes<infer E>
            ? E
            : never
          : never
        : never;

  // Component props without ref
  type ComponentPropsWithoutRef<T extends keyof JSX.IntrinsicElements | React.ComponentType<unknown>> =
    T extends keyof JSX.IntrinsicElements
      ? JSX.IntrinsicElements[T] extends React.DetailedHTMLProps<infer P, unknown>
        ? Omit<P, 'ref'>
        : never
      : T extends React.ComponentType<infer P>
        ? Omit<P, 'ref'>
        : never;

  // Component props with ref
  type ComponentPropsWithRef<T extends keyof JSX.IntrinsicElements | React.ComponentType<unknown>> =
    T extends keyof JSX.IntrinsicElements
      ? JSX.IntrinsicElements[T]
      : T extends React.ComponentType<infer P>
        ? P
        : never;
}

declare namespace JSX {
  // Use proper React types instead of any
  interface Element extends React.ReactElement<unknown, string | React.JSXElementConstructor<unknown>> {}
  interface ElementClass extends React.Component<unknown> {}
  interface ElementAttributesProperty {
    props: Record<string, unknown>;
  }
  interface ElementChildrenAttribute {
    children: Record<string, unknown>;
  }
  interface IntrinsicElements {
    [elemName: string]: Record<string, unknown>;
  }
}


