import { PrismaClient, ApprovalInstanceStatus, VendorStatus } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seeding...");

  // Create roles
  console.log("📝 Creating roles...");
  const adminRole = await prisma.role.upsert({
    where: { name: "ADMIN" },
    update: {},
    create: {
      name: "ADMIN",
      description: "System Administrator",
      permissions: [
        "user.create", "user.read", "user.update", "user.delete",
        "vendor.create", "vendor.read", "vendor.update", "vendor.delete", "vendor.verify",
        "procurement.create", "procurement.read", "procurement.update", "procurement.delete",
        "approval.create", "approval.read", "approval.update", "approval.delete",
        "document.create", "document.read", "document.update", "document.delete",
        "system.configure", "audit.read", "report.generate"
      ],
      isActive: true,
    },
  });

  const procurementRole = await prisma.role.upsert({
    where: { name: "PROCUREMENT_USER" },
    update: {},
    create: {
      name: "PROCUREMENT_USER",
      description: "Procurement Officer",
      permissions: [
        "procurement.create", "procurement.read", "procurement.update",
        "vendor.read", "vendor.verify",
        "approval.read", "approval.approve",
        "document.create", "document.read"
      ],
      isActive: true,
    },
  });

  const approverRole = await prisma.role.upsert({
    where: { name: "APPROVER" },
    update: {},
    create: {
      name: "APPROVER",
      description: "Approval Authority",
      permissions: [
        "procurement.read",
        "vendor.read",
        "approval.read", "approval.approve", "approval.reject",
        "document.read"
      ],
      isActive: true,
    },
  });

  const vendorRole = await prisma.role.upsert({
    where: { name: "VENDOR" },
    update: {},
    create: {
      name: "VENDOR",
      description: "Vendor/Supplier",
      permissions: [
        "procurement.read",
        "offer.create", "offer.read", "offer.update",
        "document.create", "document.read",
        "discussion.read", "discussion.participate"
      ],
      isActive: true,
    },
  });

  const committeeRole = await prisma.role.upsert({
    where: { name: "COMMITTEE" },
    update: {},
    create: {
      name: "COMMITTEE",
      description: "Procurement Committee Member",
      permissions: [
        "procurement.read", "procurement.evaluate",
        "vendor.read", "vendor.evaluate",
        "approval.read", "approval.approve",
        "document.read", "document.create"
      ],
      isActive: true,
    },
  });

  // Create admin user
  console.log("👤 Creating admin user...");
  const hashedPassword = await bcrypt.hash("admin123", 10);
  
  const adminUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "System Administrator",
      password: hashedPassword,
      phone: "+62-21-1234567",
      isActive: true,
      emailVerified: true,
      emailVerifiedAt: new Date(),
      roles: ["ADMIN"],
    },
  });

  // Assign admin role
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id,
      assignedBy: adminUser.id,
    },
  });

  // Create procurement officers
  console.log("👥 Creating procurement officers...");
  const procurementOfficers = [
    {
      email: "<EMAIL>",
      name: "Budi Santoso",
      phone: "+62-21-1234568",
    },
    {
      email: "<EMAIL>", 
      name: "Siti Rahayu",
      phone: "+62-21-1234569",
    },
  ];

  for (const officer of procurementOfficers) {
    const user = await prisma.user.upsert({
      where: { email: officer.email },
      update: {},
      create: {
        ...officer,
        password: hashedPassword,
        isActive: true,
        emailVerified: true,
        emailVerifiedAt: new Date(),
        roles: ["PROCUREMENT_USER"],
      },
    });

    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: user.id,
          roleId: procurementRole.id,
        },
      },
      update: {},
      create: {
        userId: user.id,
        roleId: procurementRole.id,
        assignedBy: adminUser.id,
      },
    });
  }

  // Create approvers
  console.log("✅ Creating approvers...");
  const approvers = [
    {
      email: "<EMAIL>",
      name: "Dr. Ahmad Wijaya",
      phone: "+62-21-1234570",
    },
    {
      email: "<EMAIL>",
      name: "Ir. Maria Susanti",
      phone: "+62-21-1234571",
    },
  ];

  for (const approver of approvers) {
    const user = await prisma.user.upsert({
      where: { email: approver.email },
      update: {},
      create: {
        ...approver,
        password: hashedPassword,
        isActive: true,
        emailVerified: true,
        emailVerifiedAt: new Date(),
        roles: ["APPROVER"],
      },
    });

    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: user.id,
          roleId: approverRole.id,
        },
      },
      update: {},
      create: {
        userId: user.id,
        roleId: approverRole.id,
        assignedBy: adminUser.id,
      },
    });
  }

  // Create committee members
  console.log("👥 Creating committee members...");
  const committeeMembers = [
    {
      email: "<EMAIL>",
      name: "Prof. Dr. Siti Nurhaliza",
      phone: "+62-21-1234575",
      department: "Procurement Committee",
      position: "Committee Chair",
    },
    {
      email: "<EMAIL>",
      name: "Dr. Bambang Sutrisno",
      phone: "+62-21-1234576",
      department: "Procurement Committee",
      position: "Technical Expert",
    },
  ];

  for (const memberInfo of committeeMembers) {
    const hashedPassword = await bcrypt.hash("committee123", 10);
    const user = await prisma.user.upsert({
      where: { email: memberInfo.email },
      update: {},
      create: {
        email: memberInfo.email,
        name: memberInfo.name,
        phone: memberInfo.phone,
        password: hashedPassword,
        isActive: true,
        emailVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: user.id,
          roleId: committeeRole.id,
        },
      },
      update: {},
      create: {
        userId: user.id,
        roleId: committeeRole.id,
        assignedBy: adminUser.id,
      },
    });
  }

  // Create sample vendors
  console.log("🏢 Creating sample vendors...");
  const vendors = [
    {
      email: "<EMAIL>",
      name: "PT. Technology Corporation",
      companyName: "PT. Technology Corporation",
      companyType: "PT",
      businessLicense: "123456789012345",
      taxId: "01.234.567.8-901.000",
      address: "Jl. Sudirman No. 123",
      city: "Jakarta",
      province: "DKI Jakarta",
      postalCode: "12345",
      contactPerson: "John Doe",
      contactPhone: "+62-21-5551234",
      contactEmail: "<EMAIL>",
      businessCategory: "Information Technology",
      businessDescription: "Software development and IT consulting services",
      establishedYear: 2010,
      employeeCount: 150,
      annualRevenue: ***********,
      status: "VERIFIED",
      verifiedAt: new Date(),
    },
    {
      email: "<EMAIL>",
      name: "CV. Build Pro Construction",
      companyName: "CV. Build Pro Construction", 
      companyType: "CV",
      businessLicense: "234567890123456",
      taxId: "02.345.678.9-012.000",
      address: "Jl. Gatot Subroto No. 456",
      city: "Bandung",
      province: "Jawa Barat",
      postalCode: "40123",
      contactPerson: "Jane Smith",
      contactPhone: "+62-22-5551234",
      contactEmail: "<EMAIL>",
      businessCategory: "Construction",
      businessDescription: "Building construction and infrastructure development",
      establishedYear: 2005,
      employeeCount: 75,
      annualRevenue: ***********,
      status: "VERIFIED",
      verifiedAt: new Date(),
    },
    {
      email: "<EMAIL>",
      name: "PT. Supply Corporation",
      companyName: "PT. Supply Corporation",
      companyType: "PT",
      businessLicense: "345678901234567",
      taxId: "03.456.789.0-123.000",
      address: "Jl. Thamrin No. 789",
      city: "Surabaya",
      province: "Jawa Timur",
      postalCode: "60123",
      contactPerson: "Bob Johnson",
      contactPhone: "+62-31-5551234",
      contactEmail: "<EMAIL>",
      businessCategory: "Office Supplies",
      businessDescription: "Office equipment and supplies distribution",
      establishedYear: 2015,
      employeeCount: 50,
      annualRevenue: ***********,
      status: "PENDING_VERIFICATION",
    },
  ];

  for (const vendorData of vendors) {
    const { email, name, ...vendorInfo } = vendorData;
    
    const user = await prisma.user.upsert({
      where: { email },
      update: {},
      create: {
        email,
        name,
        password: hashedPassword,
        isActive: true,
        emailVerified: true,
        emailVerifiedAt: new Date(),
        roles: ["VENDOR"],
      },
    });

    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: user.id,
          roleId: vendorRole.id,
        },
      },
      update: {},
      create: {
        userId: user.id,
        roleId: vendorRole.id,
        assignedBy: adminUser.id,
      },
    });

    await prisma.vendor.upsert({
      where: { userId: user.id },
      update: {},
      create: {
        userId: user.id,
        companyName: vendorInfo.companyName,
        companyType: vendorInfo.companyType,
        businessLicense: vendorInfo.businessLicense,
        taxId: vendorInfo.taxId,
        address: vendorInfo.address,
        city: vendorInfo.city,
        province: vendorInfo.province,
        postalCode: vendorInfo.postalCode,
        contactPerson: vendorInfo.contactPerson,
        picName: vendorInfo.contactPerson, // Alias for compatibility
        contactPhone: vendorInfo.contactPhone,
        contactEmail: vendorInfo.contactEmail,
        businessCategory: vendorInfo.businessCategory,
        businessDescription: vendorInfo.businessDescription,
        establishedYear: vendorInfo.establishedYear,
        employeeCount: vendorInfo.employeeCount,
        annualRevenue: vendorInfo.annualRevenue,
        status: vendorInfo.status as VendorStatus,
        verifiedAt: vendorInfo.verifiedAt,
      },
    });
  }

  // Create tax types
  console.log("💰 Creating tax types...");
  const taxTypes = [
    { name: "PPN", description: "Pajak Pertambahan Nilai", rate: 11.0 },
    { name: "PPh 21", description: "Pajak Penghasilan Pasal 21", rate: 5.0 },
    { name: "PPh 22", description: "Pajak Penghasilan Pasal 22", rate: 1.5 },
    { name: "PPh 23", description: "Pajak Penghasilan Pasal 23", rate: 2.0 },
    { name: "PPh 4(2)", description: "Pajak Penghasilan Pasal 4 ayat 2", rate: 10.0 },
  ];

  for (const taxType of taxTypes) {
    await prisma.taxType.upsert({
      where: { name: taxType.name },
      update: {},
      create: taxType,
    });
  }

  // Create news categories
  console.log("📰 Creating news categories...");
  const newsCategories = [
    { name: "Pengumuman", slug: "pengumuman", description: "Pengumuman resmi sistem e-procurement", isActive: true },
    { name: "Berita", slug: "berita", description: "Berita terkait pengadaan barang dan jasa", isActive: true },
    { name: "Tender", slug: "tender", description: "Informasi tender dan lelang pengadaan", isActive: true },
  ];

  for (const category of newsCategories) {
    await prisma.newsCategory.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    });
  }

  // Create workflow templates and related data
  console.log("🔄 Creating workflow templates...");

  // Create approval workflows
  const workflows = [
    {
      name: "Standard Procurement Approval",
      description: "Standard approval workflow for procurement under 100M",
      entityType: "PROCUREMENT",
      isActive: true,
      version: 1,
      conditions: { maxValue: 100000000 },
      createdById: adminUser.id
    },
    {
      name: "High Value Procurement Approval",
      description: "Enhanced approval workflow for procurement over 100M",
      entityType: "PROCUREMENT",
      isActive: true,
      version: 1,
      conditions: { minValue: 100000000 },
      createdById: adminUser.id
    },
    {
      name: "Emergency Procurement Approval",
      description: "Fast-track approval for emergency procurements",
      entityType: "PROCUREMENT",
      isActive: true,
      version: 1,
      conditions: { emergency: true },
      createdById: adminUser.id
    }
  ];

  const createdWorkflows = [];
  for (const workflow of workflows) {
    const created = await prisma.approvalWorkflow.create({
      data: workflow,
    });
    createdWorkflows.push(created);
  }

  // Create procurement workflow templates
  const procurementTemplates = [
    {
      name: "IT Equipment Procurement",
      description: "Template for IT equipment and software procurement",
      type: "TENDER",
      category: "GOODS",
      isActive: true,
      config: {
        stages: ["planning", "specification", "tender", "evaluation", "award"],
        department: "IT",
        estimatedDuration: 30
      },
      createdBy: adminUser.id
    },
    {
      name: "Construction Services",
      description: "Template for construction and infrastructure services",
      type: "TENDER",
      category: "CONSTRUCTION",
      isActive: true,
      config: {
        stages: ["planning", "design", "tender", "evaluation", "award", "execution"],
        department: "Infrastructure",
        estimatedDuration: 60
      },
      createdBy: adminUser.id
    },
    {
      name: "Consulting Services",
      description: "Template for professional consulting services",
      type: "RFQ",
      category: "SERVICES",
      isActive: true,
      config: {
        stages: ["planning", "rfq", "proposal", "evaluation", "award"],
        department: "General",
        estimatedDuration: 21
      },
      createdBy: adminUser.id
    }
  ];

  const createdTemplates = [];
  for (const template of procurementTemplates) {
    const created = await prisma.procurementWorkflowTemplate.create({
      data: template,
    });
    createdTemplates.push(created);
  }

  // Create vendor requirement templates
  console.log("📋 Creating vendor requirement templates...");
  const vendorRequirements = [
    {
      templateId: createdTemplates[0].id, // IT Equipment
      name: "IT Vendor Certification",
      description: "Required certifications for IT vendors",
      category: "TECHNICAL",
      type: "MANDATORY",
      criteria: { certifications: ["ISO 27001", "ISO 9001"], minScore: 80 },
      validationRules: { certifications: ["ISO 27001", "ISO 9001"] },
      isActive: true,
      createdBy: adminUser.id
    },
    {
      templateId: createdTemplates[1].id, // Construction
      name: "Construction License",
      description: "Valid construction business license",
      category: "LEGAL",
      type: "MANDATORY",
      criteria: { licenseTypes: ["SIUJK", "SBU"], validUntil: "required" },
      validationRules: { licenseTypes: ["SIUJK", "SBU"] },
      isActive: true,
      createdBy: adminUser.id
    }
  ];

  for (const requirement of vendorRequirements) {
    await prisma.vendorRequirementTemplate.create({
      data: requirement,
    });
  }

  // Create schedule templates
  console.log("📅 Creating schedule templates...");
  const scheduleTemplates = [
    {
      templateId: createdTemplates[0].id, // IT Equipment
      name: "IT Procurement Schedule",
      description: "Standard timeline for IT procurement",
      stages: {
        planning: { duration: 7, dependencies: [] },
        tender: { duration: 14, dependencies: ["planning"] },
        evaluation: { duration: 7, dependencies: ["tender"] }
      },
      milestones: {
        "tender_opening": { stage: "tender", offset: 0 },
        "evaluation_complete": { stage: "evaluation", offset: 7 }
      },
      buffers: { planning: 1, tender: 2, evaluation: 1 },
      workingDays: { monday: true, tuesday: true, wednesday: true, thursday: true, friday: true },
      isActive: true,
      createdBy: adminUser.id
    },
    {
      templateId: createdTemplates[1].id, // Construction
      name: "Construction Schedule",
      description: "Timeline for construction projects",
      stages: {
        planning: { duration: 21, dependencies: [] },
        design: { duration: 14, dependencies: ["planning"] },
        tender: { duration: 30, dependencies: ["design"] }
      },
      milestones: {
        "design_approval": { stage: "design", offset: 14 },
        "tender_submission": { stage: "tender", offset: 30 }
      },
      buffers: { planning: 3, design: 2, tender: 5 },
      workingDays: { monday: true, tuesday: true, wednesday: true, thursday: true, friday: true },
      isActive: true,
      createdBy: adminUser.id
    }
  ];

  for (const schedule of scheduleTemplates) {
    await prisma.procurementScheduleTemplate.create({
      data: schedule,
    });
  }

  // Create sample approval instances for dashboard stats
  console.log("📊 Creating sample approval instances...");
  const approvalInstances = [
    {
      workflowId: createdWorkflows[0].id,
      entityType: "PROCUREMENT",
      entityId: "sample-procurement-1",
      status: ApprovalInstanceStatus.APPROVED,
      title: "Office Equipment Procurement",
      description: "Procurement of office computers and printers",
      startedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      initiatedById: adminUser.id
    },
    {
      workflowId: createdWorkflows[1].id,
      entityType: "PROCUREMENT",
      entityId: "sample-procurement-2",
      status: ApprovalInstanceStatus.PENDING,
      title: "Infrastructure Development",
      description: "Major infrastructure development project",
      startedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      dueDate: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000), // 4 days from now
      initiatedById: adminUser.id
    },
    {
      workflowId: createdWorkflows[0].id,
      entityType: "PROCUREMENT",
      entityId: "sample-procurement-3",
      status: ApprovalInstanceStatus.IN_PROGRESS,
      title: "Software Licensing",
      description: "Annual software license renewal",
      startedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      dueDate: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000), // 6 days from now
      initiatedById: adminUser.id
    }
  ];

  for (const instance of approvalInstances) {
    await prisma.approvalInstance.create({
      data: instance,
    });
  }

  console.log("✅ Database seeding completed successfully!");
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
