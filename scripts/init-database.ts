#!/usr/bin/env tsx

/**
 * Database Initialization Script
 * 
 * This script performs complete database initialization for the E-Procurement system:
 * - Generates Prisma client
 * - Applies database migrations
 * - Sets up database partitioning for performance
 * - Seeds the database with initial data
 * - Creates default approval workflows
 * - Creates default document templates
 * - Optimizes database indexes
 * - Sets up maintenance jobs
 */

import { execSync } from 'child_process';

import { PrismaClient, Prisma } from '@prisma/client';
// import { toPrismaJson } from '../src/lib/utils/json';

// Simple JSON conversion for scripts
function toPrismaJson(value: unknown): Prisma.InputJsonValue {
  return value as Prisma.InputJsonValue;
}

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step: string) {
  log(`\n🔄 ${step}`, 'cyan');
}

function logSuccess(message: string) {
  log(`✅ ${message}`, 'green');
}

function logError(message: string) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, 'yellow');
}

async function runCommand(command: string, description: string): Promise<void> {
  try {
    log(`   Running: ${command}`, 'blue');
    execSync(command, { stdio: 'inherit' });
    logSuccess(description);
  } catch (error) {
    logError(`Failed: ${description}`);
    throw error;
  }
}

async function checkDatabaseConnection(): Promise<void> {
  logStep('Checking database connection...');
  try {
    await prisma.$connect();
    logSuccess('Database connection established');
  } catch (error) {
    logError('Failed to connect to database');
    logError('Please check your DATABASE_URL in .env file');
    throw error;
  }
}

async function generatePrismaClient(): Promise<void> {
  logStep('Generating Prisma client...');
  await runCommand('npx prisma generate', 'Prisma client generated');
}

async function applyMigrations(): Promise<void> {
  logStep('Applying database migrations...');
  try {
    // Check if this is a fresh database
    const tables = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
    ` as { table_name: string }[];

    if (tables.length === 0) {
      log('   Fresh database detected, deploying migrations...', 'blue');
      await runCommand('npx prisma migrate deploy', 'Database migrations applied');
    } else {
      log('   Existing database detected, applying new migrations...', 'blue');
      await runCommand('npx prisma migrate dev --name auto-migration', 'Database migrations applied');
    }
  } catch (error) {
    logWarning('Migration command failed, trying alternative approach...');
    log(`   Migration error: ${error instanceof Error ? error.message : String(error)}`, 'yellow');
    await runCommand('npx prisma db push', 'Database schema synchronized');
  }
}

async function setupDatabasePartitioning(): Promise<void> {
  logStep('Setting up database partitioning for performance...');
  
  try {
    // Create partitioning for audit logs (monthly partitions)
    await prisma.$executeRaw`
      CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
      RETURNS void AS $$
      DECLARE
        partition_name text;
        end_date date;
      BEGIN
        partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
        end_date := start_date + interval '1 month';
        
        EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I 
                       FOR VALUES FROM (%L) TO (%L)',
                       partition_name, table_name, start_date, end_date);
      END;
      $$ LANGUAGE plpgsql;
    `;
    
    logSuccess('Database partitioning functions created');
  } catch (error) {
    logWarning('Partitioning setup failed (this is optional for development)');
    console.log('   Error:', error);
  }
}

async function optimizeIndexes(): Promise<void> {
  logStep('Optimizing database indexes...');
  
  try {
    // Create additional performance indexes
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_timestamp_user 
      ON audit_logs (timestamp DESC, user_id);
    `;
    
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_read 
      ON notifications (user_id, read, created_at DESC);
    `;
    
    logSuccess('Database indexes optimized');
  } catch (error) {
    logWarning('Index optimization failed (this is optional)');
    console.log('   Error:', error);
  }
}

async function seedDatabase(): Promise<void> {
  logStep('Seeding database with initial data...');
  await runCommand('npx prisma db seed', 'Database seeded with initial data');
}

async function createDefaultWorkflows(): Promise<void> {
  logStep('Creating default approval workflows...');
  
  try {
    // Get admin user for workflow creation
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!adminUser) {
      logWarning('Admin user not found, skipping workflow creation');
      return;
    }

    // Create default procurement approval workflow
    const existingWorkflow = await prisma.approvalWorkflow.findFirst({
      where: { name: 'Default Procurement Approval' }
    });

    if (!existingWorkflow) {
      await prisma.approvalWorkflow.create({
        data: {
          name: 'Default Procurement Approval',
          description: 'Standard approval workflow for procurement creation',
          entityType: 'procurement',
          stage: 'creation',
          isActive: true,
          isDefault: true,
          conditions: toPrismaJson({
            minValue: 0,
            maxValue: 1000000000, // 1 billion IDR
          }),
          createdById: adminUser.id,
        }
      });
    }

    logSuccess('Default approval workflows created');
  } catch (error) {
    logWarning('Failed to create default workflows');
    console.log('   Error:', error);
  }
}

async function createDefaultDocumentTemplates(): Promise<void> {
  logStep('Creating default document templates...');
  
  try {
    // Get admin user for template creation
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!adminUser) {
      logWarning('Admin user not found, skipping template creation');
      return;
    }

    // Create default RFQ template
    const existingTemplate = await prisma.documentTemplate.findFirst({
      where: { name: 'Default RFQ Template' }
    });

    if (!existingTemplate) {
      await prisma.documentTemplate.create({
        data: {
          name: 'Default RFQ Template',
          description: 'Standard Request for Quotation template',
          type: 'RFQ',
          category: 'Procurement',
          content: toPrismaJson({
            htmlTemplate: `
              <div class="rfq-template">
                <h1>REQUEST FOR QUOTATION</h1>
                <p>RFQ Number: {{rfqNumber}}</p>
                <p>Date: {{date}}</p>
                <h2>Vendor Information</h2>
                <p>Company: {{vendor.companyName}}</p>
                <h2>Items Requested</h2>
                {{#each items}}
                <div class="item">
                  <p>{{name}} - Quantity: {{quantity}} {{unit}}</p>
                </div>
                {{/each}}
              </div>
            `,
            cssStyles: `
              .rfq-template { font-family: Arial, sans-serif; }
              h1 { color: #2563eb; }
              .item { margin: 10px 0; padding: 10px; border: 1px solid #e5e7eb; }
            `
          }),
          variables: toPrismaJson([
            { name: 'rfqNumber', type: 'string', required: true },
            { name: 'date', type: 'date', required: true },
            { name: 'vendor', type: 'object', required: true },
            { name: 'items', type: 'array', required: true }
          ]),
          version: 1,
          status: 'APPROVED',
          isActive: true,
          createdBy: adminUser.id,
          approvedBy: adminUser.id,
          approvedAt: new Date(),
        }
      });
    }

    logSuccess('Default document templates created');
  } catch (error) {
    logWarning('Failed to create default templates');
    console.log('   Error:', error);
  }
}

async function setupMaintenanceJobs(): Promise<void> {
  logStep('Setting up maintenance jobs...');
  
  try {
    logSuccess('Maintenance jobs configured (manual setup required for production)');
    
    log('   📝 Manual setup required:', 'yellow');
    log('   - Set up daily database backup job', 'yellow');
    log('   - Configure log rotation', 'yellow');
    log('   - Set up performance monitoring alerts', 'yellow');
    log('   - Configure automated partition maintenance', 'yellow');
  } catch (error) {
    logWarning('Maintenance jobs setup failed');
    console.log('   Error:', error);
  }
}

async function verifySetup(): Promise<void> {
  logStep('Verifying database setup...');
  
  try {
    // Check if admin user exists
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (adminUser) {
      logSuccess('Admin user created successfully');
    } else {
      logWarning('Admin user not found');
    }

    // Check if roles exist
    const rolesCount = await prisma.role.count();
    if (rolesCount > 0) {
      logSuccess(`${rolesCount} roles created`);
    } else {
      logWarning('No roles found');
    }

    // Check if document templates exist
    const templatesCount = await prisma.documentTemplate.count();
    if (templatesCount > 0) {
      logSuccess(`${templatesCount} document templates created`);
    } else {
      logWarning('No document templates found');
    }

    logSuccess('Database setup verification completed');
  } catch (error) {
    logError('Setup verification failed');
    throw error;
  }
}

async function main(): Promise<void> {
  log('\n🚀 E-Procurement Database Initialization', 'bright');
  log('==========================================', 'bright');
  
  try {
    await checkDatabaseConnection();
    await generatePrismaClient();
    await applyMigrations();
    await setupDatabasePartitioning();
    await seedDatabase();
    await createDefaultWorkflows();
    await createDefaultDocumentTemplates();
    await optimizeIndexes();
    await setupMaintenanceJobs();
    await verifySetup();
    
    log('\n🎉 Database initialization completed successfully!', 'green');
    log('==========================================', 'green');
    log('\n📋 Next steps:', 'cyan');
    log('1. Start the development server: npm run dev', 'blue');
    log('2. Access the admin panel: http://localhost:3000/admin', 'blue');
    log('3. Login with: <EMAIL> / admin123', 'blue');
    log('4. Configure your environment variables in .env', 'blue');
    
  } catch (error) {
    log('\n💥 Database initialization failed!', 'red');
    log('==========================================', 'red');
    logError('Error details:');
    console.error(error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:');
  console.error(promise);
  logError('Reason:');
  console.error(reason);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  logError('Fatal error during initialization:');
  console.error(error);
  process.exit(1);
});
