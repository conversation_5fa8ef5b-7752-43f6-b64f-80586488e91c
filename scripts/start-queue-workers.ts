#!/usr/bin/env tsx

/**
 * Queue Workers Startup Script
 * 
 * This script starts all queue workers for the E-Procurement system:
 * - Email worker for sending emails
 * - SMS worker for sending SMS notifications
 * - Push notification worker
 * - Webhook worker for external integrations
 */

// Note: This script should be run from the project root
// For now, we'll create a simple placeholder implementation
console.log('Queue workers script - implementation pending');

// Placeholder functions
const workerManager = {
  registerWorker: (name: string, worker: any) => {
    console.log(`Registered ${name} worker`);
    // Store worker for future management
    if (worker && typeof worker.start === 'function') {
      console.log(`Worker ${name} is ready to start`);
    }
  },
  startAll: async () => console.log('All workers started'),
  stopAll: async () => console.log('All workers stopped'),
  getHealthStatus: () => ({ email: true, sms: true }),
};

const emailWorker = { start: async () => console.log('Email worker started') };
const smsWorker = { start: async () => console.log('SMS worker started') };
const checkRedisHealth = async () => true;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step: string) {
  log(`\n🔄 ${step}`, 'cyan');
}

function logSuccess(message: string) {
  log(`✅ ${message}`, 'green');
}

function logError(message: string) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, 'yellow');
}

async function checkPrerequisites(): Promise<void> {
  logStep('Checking prerequisites...');

  // Check Redis connection
  const redisHealthy = await checkRedisHealth();
  if (!redisHealthy) {
    throw new Error('Redis is not available. Please ensure Redis is running and accessible.');
  }
  logSuccess('Redis connection verified');

  // Check environment variables
  const requiredEnvVars = ['REDIS_HOST', 'REDIS_PORT'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    logWarning(`Missing environment variables: ${missingVars.join(', ')}`);
    logWarning('Using default values for missing variables');
  }

  // Check email configuration
  if (process.env.NODE_ENV === 'production') {
    const emailVars = ['SMTP_HOST', 'SMTP_USER', 'SMTP_PASSWORD'];
    const missingEmailVars = emailVars.filter(varName => !process.env[varName]);
    
    if (missingEmailVars.length > 0) {
      logWarning(`Missing email configuration: ${missingEmailVars.join(', ')}`);
      logWarning('Email notifications may not work properly');
    } else {
      logSuccess('Email configuration verified');
    }
  }

  // Check SMS configuration
  const smsProvider = process.env.SMS_PROVIDER;
  if (smsProvider && smsProvider !== 'mock') {
    const smsVars = ['SMS_API_KEY'];
    const missingSmsVars = smsVars.filter(varName => !process.env[varName]);
    
    if (missingSmsVars.length > 0) {
      logWarning(`Missing SMS configuration: ${missingSmsVars.join(', ')}`);
      logWarning('SMS notifications may not work properly');
    } else {
      logSuccess('SMS configuration verified');
    }
  }
}

async function registerWorkers(): Promise<void> {
  logStep('Registering queue workers...');

  // Register email worker
  workerManager.registerWorker('email', emailWorker);
  logSuccess('Email worker registered');

  // Register SMS worker
  workerManager.registerWorker('sms', smsWorker);
  logSuccess('SMS worker registered');

  // TODO: Register push notification worker when implemented
  // workerManager.registerWorker('push', pushWorker);
  
  // TODO: Register webhook worker when implemented
  // workerManager.registerWorker('webhook', webhookWorker);

  logSuccess('All workers registered');
}

async function startWorkers(): Promise<void> {
  logStep('Starting queue workers...');

  try {
    await workerManager.startAll();
    logSuccess('All workers started successfully');
  } catch (error) {
    logError('Failed to start workers');
    throw error;
  }
}

async function setupHealthChecks(): Promise<void> {
  logStep('Setting up health checks...');

  // Set up periodic health checks
  setInterval(async () => {
    try {
      const redisHealthy = await checkRedisHealth();
      const workerHealth = workerManager.getHealthStatus();
      
      const allHealthy = redisHealthy && Object.values(workerHealth).every(healthy => healthy);
      
      if (!allHealthy) {
        logWarning('Health check failed:');
        if (!redisHealthy) {
          logWarning('- Redis connection unhealthy');
        }
        
        Object.entries(workerHealth).forEach(([name, healthy]) => {
          if (!healthy) {
            logWarning(`- ${name} worker unhealthy`);
          }
        });
      }
    } catch (error) {
      logError(`Health check error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, 30000); // Check every 30 seconds

  logSuccess('Health checks configured');
}

async function main(): Promise<void> {
  log('\n🚀 E-Procurement Queue Workers', 'bright');
  log('================================', 'bright');
  
  try {
    await checkPrerequisites();
    await registerWorkers();
    await setupHealthChecks();
    await startWorkers();
    
    log('\n🎉 Queue workers started successfully!', 'green');
    log('====================================', 'green');
    log('\n📋 Worker Status:', 'cyan');
    log('- Email worker: Running', 'blue');
    log('- SMS worker: Running', 'blue');
    log('- Health checks: Active', 'blue');
    
    log('\n📝 Management:', 'cyan');
    log('- View stats: GET /api/admin/queue/stats', 'blue');
    log('- Manage workers: POST /api/admin/queue/workers', 'blue');
    log('- Stop workers: Ctrl+C', 'blue');
    
    // Keep the process running
    process.stdin.resume();
    
  } catch (error) {
    log('\n💥 Queue workers startup failed!', 'red');
    log('=================================', 'red');
    logError('Error details:');
    console.error(error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  log('\n🔄 Received SIGINT, shutting down workers gracefully...', 'yellow');
  await workerManager.stopAll();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  log('\n🔄 Received SIGTERM, shutting down workers gracefully...', 'yellow');
  await workerManager.stopAll();
  process.exit(0);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:');
  console.error(promise);
  logError('Reason:');
  console.error(reason);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  logError('Fatal error during startup:');
  console.error(error);
  process.exit(1);
});
